.launch-switch-wrapper {
  margin: 0;
  padding: 0;
  display: inline-block;
}

#launch-checkbox {
  display: none;
}

.switch {
  position: relative;
  width: 70px;
  height: 70px;
  background-color: rgb(99, 99, 99);
  border-radius: 50%;
  z-index: 1;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid rgb(126, 126, 126);
  box-shadow: 0px 0px 3px rgb(2, 2, 2) inset;
  transition: all 0.3s ease;
}

.switch svg {
  width: 1.2em;
  transition: all 0.3s ease;
}

.switch svg path {
  fill: rgb(48, 48, 48);
  transition: all 0.3s ease;
}

#launch-checkbox:checked + .switch {
  box-shadow: 0px 0px 1px #6366F1 inset,
    0px 0px 2px #6366F1 inset, 0px 0px 10px #6366F1 inset,
    0px 0px 40px #6366F1, 0px 0px 100px #6366F1,
    0px 0px 5px #6366F1;
  border: 2px solid rgb(255, 255, 255);
  background-color: #8B9FE8;
}

#launch-checkbox:checked + .switch svg {
  filter: drop-shadow(0px 0px 5px #6366F1);
}

#launch-checkbox:checked + .switch svg path {
  fill: rgb(255, 255, 255);
}

#launch-checkbox:disabled + .switch {
  opacity: 0.6;
  cursor: not-allowed;
}