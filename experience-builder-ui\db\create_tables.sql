-- Create schema for experience builder data
CREATE SCHEMA IF NOT EXISTS experience_builder;

-- Set up extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create table for experiences
CREATE TABLE IF NOT EXISTS experience_builder.experiences (
    experience_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    background_image TEXT,
    status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived'))
);

-- Create table for modules in experiences
CREATE TABLE IF NOT EXISTS experience_builder.modules (
    module_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    experience_id UUID NOT NULL REFERENCES experience_builder.experiences(experience_id) ON DELETE CASCADE,
    type VARCHAR(50) NOT NULL,
    title VARCHAR(100) NOT NULL,
    display_order INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    content JSONB NOT NULL DEFAULT '{}'::jsonb
);

-- Create index for faster module lookups by experience
CREATE INDEX IF NOT EXISTS idx_modules_experience_id ON experience_builder.modules(experience_id);

-- Create table for QR codes
CREATE TABLE IF NOT EXISTS experience_builder.qr_codes (
    qr_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    experience_id UUID NOT NULL REFERENCES experience_builder.experiences(experience_id) ON DELETE CASCADE,
    qr_image_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_accessed TIMESTAMP WITH TIME ZONE,
    access_count INTEGER DEFAULT 0,
    CONSTRAINT fk_experience UNIQUE (experience_id)
);

-- Create index for faster QR code lookups by experience
CREATE INDEX IF NOT EXISTS idx_qr_codes_experience_id ON experience_builder.qr_codes(experience_id);

-- Create table for analytics
CREATE TABLE IF NOT EXISTS experience_builder.analytics (
    analytics_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    experience_id UUID NOT NULL REFERENCES experience_builder.experiences(experience_id) ON DELETE CASCADE,
    event_type VARCHAR(50) NOT NULL,
    event_data JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for faster analytics lookups by experience
CREATE INDEX IF NOT EXISTS idx_analytics_experience_id ON experience_builder.analytics(experience_id);

-- Create trigger to update the updated_at timestamp whenever an experience is modified
CREATE OR REPLACE FUNCTION experience_builder.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply the trigger to the experiences table
DROP TRIGGER IF EXISTS update_experiences_updated_at ON experience_builder.experiences;
CREATE TRIGGER update_experiences_updated_at
BEFORE UPDATE ON experience_builder.experiences
FOR EACH ROW
EXECUTE FUNCTION experience_builder.update_updated_at_column();

-- Function to increment a counter (used for access_count in qr_codes)
CREATE OR REPLACE FUNCTION experience_builder.increment_counter(experience_uuid UUID)
RETURNS INTEGER AS $$
DECLARE
    current_count INTEGER;
BEGIN
    SELECT access_count INTO current_count 
    FROM experience_builder.qr_codes 
    WHERE experience_id = experience_uuid;
    
    RETURN COALESCE(current_count, 0) + 1;
END;
$$ LANGUAGE plpgsql;
