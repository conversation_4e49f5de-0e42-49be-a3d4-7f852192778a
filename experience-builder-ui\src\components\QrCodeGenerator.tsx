import { useState, useEffect } from "react";
import {
  Box,
  Typography,
  Paper,
  CircularProgress,
  Button,
} from "@mui/material";
import DownloadIcon from "@mui/icons-material/Download";
import QRCode from "qrcode";
import { generateQrCodeUrl, updateQrCodeImage } from "../utils/supabaseClient";

interface QrCodeGeneratorProps {
  experienceId: string | null;
  experienceName: string;
}

const QrCodeGenerator = ({
  experienceId,
  experienceName,
}: QrCodeGeneratorProps) => {
  const [qrImageUrl, setQrImageUrl] = useState<string | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Generate QR code when experienceId is available
    if (experienceId) {
      generateQrCode(experienceId);
    } else {
      // Reset QR code when no experience is selected
      setQrImageUrl(null);
    }
  }, [experienceId]);

  const generateQrCode = async (id: string) => {
    try {
      setIsGenerating(true);
      setError(null);

      // Generate the URL that the QR code will point to
      const experienceUrl = generateQrCodeUrl(id);

      console.log("Generating QR code for URL:", experienceUrl);

      // Generate QR code as data URL
      const qrDataUrl = await QRCode.toDataURL(experienceUrl, {
        width: 240,
        margin: 2,
        color: {
          dark: "#000000",
          light: "#FFFFFF",
        },
      });

      setQrImageUrl(qrDataUrl);

      // Save the QR code image URL to the database
      await updateQrCodeImage(id, qrDataUrl);
    } catch (err) {
      console.error("Error generating QR code:", err);
      setError("Failed to generate QR code");
    } finally {
      setIsGenerating(false);
    }
  };

  const handleDownload = () => {
    if (!qrImageUrl) return;

    // Create a temporary link element
    const link = document.createElement("a");
    link.href = qrImageUrl;
    link.download = `${experienceName.replace(/\s+/g, "-").toLowerCase()}-qrcode.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <Box
      sx={{
        color: "rgb(100, 116, 139)",
        font: "400 14px/21px Inter, Roboto, Helvetica, Arial, sans-serif",
      }}
    >
      {!experienceId && (
        <p>Your QR code will appear here after launching your experience.</p>
      )}

      {isGenerating && (
        <Box display="flex" justifyContent="center" alignItems="center" py={3}>
          <CircularProgress size={40} />
        </Box>
      )}

      {error && (
        <Typography color="error" variant="body2">
          {error}
        </Typography>
      )}

      {qrImageUrl && (
        <Box display="flex" flexDirection="column" alignItems="center" mt={1}>
          <Paper
            elevation={2}
            sx={{
              p: 2,
              mb: 2,
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              bgcolor: "#fff",
            }}
          >
            <img
              src={qrImageUrl}
              alt="QR Code"
              style={{
                width: "100%",
                maxWidth: "240px",
                height: "auto",
              }}
            />
            <Box mt={2} textAlign="center">
              <Typography variant="caption" color="text.secondary" gutterBottom>
                Or open in browser:
              </Typography>
              <Typography
                variant="body2"
                component="a"
                href={generateQrCodeUrl(experienceId!)}
                target="_blank"
                rel="noopener noreferrer"
                sx={{
                  color: "primary.main",
                  textDecoration: "none",
                  wordBreak: "break-all",
                  "&:hover": {
                    textDecoration: "underline",
                  },
                }}
              >
                {generateQrCodeUrl(experienceId!)}
              </Typography>
            </Box>
            <Typography variant="caption" color="text.secondary" mt={1}>
              Scan to view experience
            </Typography>
          </Paper>

          <Button
            variant="outlined"
            size="small"
            onClick={handleDownload}
            startIcon={<DownloadIcon />}
          >
            Download QR Code
          </Button>
        </Box>
      )}
    </Box>
  );
};

export default QrCodeGenerator;
