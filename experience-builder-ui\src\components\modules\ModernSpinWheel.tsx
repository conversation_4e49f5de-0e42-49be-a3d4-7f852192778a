import React, { useRef, useState } from "react";
import { <PERSON>, Typo<PERSON>, <PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "@mui/material";
import { Trophy, Gift, Sparkle, X } from "phosphor-react";
import { styled, keyframes } from "@mui/material/styles";

// --- TypeScript Interfaces ---
export interface SpinWheelPrize {
  id: string;
  text: string;
  color: string;
  type?: "discount" | "gift" | "jackpot" | "failure";
}

export interface ModernSpinWheelProps {
  prizes: SpinWheelPrize[];
  onComplete?: (selectedPrize: SpinWheelPrize) => void;
}

// Animations
const spin = keyframes`
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
`;

const pulse = keyframes`
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
`;

const sparkleFloat = keyframes`
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-10px) rotate(180deg); }
`;

const glow = keyframes`
  0%, 100% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.3); }
  50% { box-shadow: 0 0 40px rgba(255, 215, 0, 0.6); }
`;

// Styled Components
const Container = styled(Box)(() => ({
  background: "linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%)",
  borderRadius: "24px",
  padding: "24px",
  position: "relative",
  overflow: "hidden",
  boxShadow: "0 20px 40px rgba(0,0,0,0.15)",
  display: "flex",
  flexDirection: "column",
  alignItems: "center",
  minHeight: "500px",
}));

const WheelContainer = styled(Box)(() => ({
  position: "relative",
  width: "220px",
  height: "220px",
  margin: "15px auto",
}));

const WheelSVG = styled("svg")<{ $isSpinning: boolean }>(({ $isSpinning }) => ({
  width: "100%",
  height: "100%",
  borderRadius: "50%",
  boxShadow: "0 8px 30px rgba(0,0,0,0.3)",
  animation: $isSpinning
    ? `${spin} 3s cubic-bezier(0.25, 0.46, 0.45, 0.94)`
    : "none",
  filter: "drop-shadow(0 4px 8px rgba(0,0,0,0.2))",
}));

const Pointer = styled(Box)(() => ({
  position: "absolute",
  top: "-10px",
  left: "50%",
  transform: "translateX(-50%)",
  width: 0,
  height: 0,
  borderLeft: "15px solid transparent",
  borderRight: "15px solid transparent",
  borderTop: "30px solid #FFD700",
  zIndex: 10,
  filter: "drop-shadow(0 2px 4px rgba(0,0,0,0.3))",
}));

const CenterCircle = styled(Box)(() => ({
  position: "absolute",
  top: "50%",
  left: "50%",
  transform: "translate(-50%, -50%)",
  width: "60px",
  height: "60px",
  borderRadius: "50%",
  background: "linear-gradient(135deg, #FFD700, #FFA500)",
  border: "4px solid white",
  boxShadow: "0 4px 15px rgba(0,0,0,0.3)",
  zIndex: 10,
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  animation: `${glow} 2s infinite`,
}));

const SpinButton = styled(MuiButton)<{ $isSpinning: boolean }>(
  ({ $isSpinning }) => ({
    background: $isSpinning
      ? "linear-gradient(135deg, #95A5A6, #7F8C8D)"
      : "linear-gradient(135deg, #25D366, #128C7E)",
    color: "white",
    padding: "16px 32px",
    borderRadius: "50px",
    fontSize: "1.1rem",
    fontWeight: 700,
    textTransform: "uppercase",
    letterSpacing: "1px",
    border: "none",
    boxShadow: $isSpinning
      ? "0 4px 15px rgba(149, 165, 166, 0.3)"
      : "0 8px 25px rgba(37, 211, 102, 0.3)",
    position: "relative",
    overflow: "hidden",
    cursor: $isSpinning ? "not-allowed" : "pointer",
    animation: $isSpinning ? "none" : `${pulse} 2s infinite`,
    "&:hover": {
      background: $isSpinning
        ? "linear-gradient(135deg, #95A5A6, #7F8C8D)"
        : "linear-gradient(135deg, #128C7E, #075E54)",
      transform: $isSpinning ? "none" : "translateY(-2px)",
      boxShadow: $isSpinning
        ? "0 4px 15px rgba(149, 165, 166, 0.3)"
        : "0 12px 30px rgba(37, 211, 102, 0.4)",
    },
    "&:disabled": {
      background: "linear-gradient(135deg, #95A5A6, #7F8C8D)",
      cursor: "not-allowed",
    },
  }),
);

const ClaimButton = styled(MuiButton)({
  background: "linear-gradient(135deg, #25D366, #128C7E)",
  color: "white",
  padding: "16px 32px",
  borderRadius: "50px",
  fontSize: "1.1rem",
  fontWeight: 700,
  textTransform: "uppercase",
  letterSpacing: "1px",
  border: "none",
  boxShadow: "0 8px 25px rgba(37, 211, 102, 0.3)",
  position: "relative",
  overflow: "hidden",
  animation: `${pulse} 2s infinite`,
  "&:hover": {
    background: "linear-gradient(135deg, #128C7E, #075E54)",
    transform: "translateY(-2px)",
    boxShadow: "0 12px 30px rgba(37, 211, 102, 0.4)",
  },
});

const FloatingSparkle = styled(Sparkle, {
  shouldForwardProp: (prop) => !String(prop).startsWith("$"),
})<{ delay: number }>(({ delay }) => ({
  position: "absolute",
  color: "rgba(255, 255, 255, 0.6)",
  animation: `${sparkleFloat} 3s ease-in-out infinite`,
  animationDelay: `${delay}s`,
  zIndex: 1000,
  pointerEvents: "none",
}));

const ResultChip = styled(Chip)(() => ({
  background: "linear-gradient(135deg, #FFD700, #FFA500)",
  color: "white",
  fontWeight: "bold",
  fontSize: "1rem",
  padding: "8px 16px",
  animation: `${pulse} 1s infinite`,
  "& .MuiChip-label": {
    padding: "0 12px",
  },
}));

const ModernSpinWheel: React.FC<ModernSpinWheelProps> = ({
  prizes,
  onComplete,
}) => {
  const [isSpinning, setIsSpinning] = useState(false);
  const [result, setResult] = useState<SpinWheelPrize | null>(null);
  const [showClaimButton, setShowClaimButton] = useState(false);
  const wheelRef = useRef<SVGSVGElement>(null);

  const getSegmentAngle = () => 360 / prizes.length;
  const segmentAngle = getSegmentAngle();

  const getSegmentPath = (index: number) => {
    const startAngle = index * segmentAngle - 90; // Start from top
    const endAngle = (index + 1) * segmentAngle - 90;
    const radius = 140;
    const centerX = 150;
    const centerY = 150;

    const startAngleRad = (startAngle * Math.PI) / 180;
    const endAngleRad = (endAngle * Math.PI) / 180;

    const x1 = centerX + radius * Math.cos(startAngleRad);
    const y1 = centerY + radius * Math.sin(startAngleRad);
    const x2 = centerX + radius * Math.cos(endAngleRad);
    const y2 = centerY + radius * Math.sin(endAngleRad);

    const largeArcFlag = segmentAngle > 180 ? 1 : 0;

    return `M ${centerX} ${centerY} L ${x1} ${y1} A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2} Z`;
  };

  const getTextPosition = (index: number) => {
    const angle =
      (index * segmentAngle + segmentAngle / 2 - 90) * (Math.PI / 180);
    const radius = 100;
    const centerX = 150;
    const centerY = 150;

    return {
      x: centerX + radius * Math.cos(angle),
      y: centerY + radius * Math.sin(angle),
      rotation: (index * segmentAngle + segmentAngle / 2) % 360,
    };
  };

  const handleSpin = () => {
    if (isSpinning) return;

    setIsSpinning(true);
    setResult(null);
    setShowClaimButton(false);

    // Simulate spin duration
    setTimeout(() => {
      const randomIndex = Math.floor(Math.random() * prizes.length);
      const selectedPrize = prizes[randomIndex];

      setResult(selectedPrize);
      setIsSpinning(false);

      // Show claim button for winning prizes
      if (selectedPrize.type !== "failure") {
        setShowClaimButton(true);
      }

      if (onComplete) {
        onComplete(selectedPrize);
      }
    }, 3000);
  };

  const handleClaim = () => {
    const whatsappUrl =
      "https://wa.me/1234567890?text=I%20want%20to%20claim%20my%20spin%20wheel%20prize!";
    window.open(whatsappUrl, "_blank");
  };

  const getResultIcon = (type?: string) => {
    switch (type) {
      case "discount":
        return <Sparkle size={16} weight="fill" />;
      case "gift":
        return <Gift size={16} weight="fill" />;
      case "jackpot":
        return <Trophy size={16} weight="fill" />;
      default:
        return <X size={16} weight="bold" />;
    }
  };

  return (
    <Container>
      {/* Floating decorative elements */}
      <FloatingSparkle
        delay={0}
        size={20}
        style={{ top: "10%", left: "10%" }}
      />
      <FloatingSparkle
        delay={1}
        size={16}
        style={{ top: "20%", right: "15%" }}
      />
      <FloatingSparkle
        delay={2}
        size={24}
        style={{ bottom: "20%", left: "20%" }}
      />
      <FloatingSparkle
        delay={1.5}
        size={18}
        style={{ bottom: "10%", right: "10%" }}
      />

      <Typography
        variant="h4"
        sx={{
          color: "white",
          fontWeight: 800,
          mb: 2,
          textShadow: "0 2px 4px rgba(0,0,0,0.3)",
          textAlign: "center",
        }}
      >
        🎰 Spin to Win! 🎰
      </Typography>

      {result && (
        <ResultChip
          icon={getResultIcon(result.type)}
          label={`You won: ${result.text}!`}
          sx={{ mb: 2 }}
        />
      )}

      <WheelContainer>
        <Pointer />
        <WheelSVG ref={wheelRef} $isSpinning={isSpinning} viewBox="0 0 300 300">
          {prizes.map((prize, index) => (
            <g key={index}>
              <path
                d={getSegmentPath(index)}
                fill={prize.color}
                stroke="white"
                strokeWidth="3"
              />
              {(() => {
                const { x, y, rotation } = getTextPosition(index);
                // If rotation is between 90 and 270, flip text so it's always upright
                const upright = rotation > 90 && rotation < 270;
                return (
                  <text
                    x={x}
                    y={y}
                    fill="white"
                    fontSize="12"
                    fontWeight="bold"
                    textAnchor="middle"
                    dominantBaseline="middle"
                    transform={`rotate(${upright ? rotation + 180 : rotation}, ${x}, ${y})`}
                    style={{
                      textShadow: "0 1px 2px rgba(0,0,0,0.8)",
                      alignmentBaseline: "middle",
                    }}
                  >
                    {prize.text}
                  </text>
                );
              })()}
            </g>
          ))}
        </WheelSVG>
        <CenterCircle>
          <Sparkle size={24} color="white" weight="fill" />
        </CenterCircle>
      </WheelContainer>

      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          gap: 2,
        }}
      >
        <SpinButton
          onClick={handleSpin}
          disabled={isSpinning}
          $isSpinning={isSpinning}
        >
          {isSpinning ? (
            <>
              <Sparkle size={20} weight="fill" style={{ marginRight: "8px" }} />
              SPINNING...
            </>
          ) : (
            <>
              <Trophy size={20} weight="fill" style={{ marginRight: "8px" }} />
              SPIN NOW!
            </>
          )}
        </SpinButton>

        {showClaimButton && (
          <ClaimButton onClick={handleClaim}>
            <Gift size={20} weight="fill" style={{ marginRight: "8px" }} />
            CLAIM PRIZE
          </ClaimButton>
        )}
      </Box>
    </Container>
  );
};

export default ModernSpinWheel;
