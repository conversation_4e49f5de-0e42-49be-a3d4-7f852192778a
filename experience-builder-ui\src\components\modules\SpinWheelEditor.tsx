import React, { useState, useMemo, useEffect, useRef } from 'react';
import {
  Box,
  Typography,
  TextField,
  Button,
  IconButton,
  List,
  ListItem,
  Paper,
  Grid,
  Card,
  CardContent
} from "@mui/material";
import DeleteIcon from "@mui/icons-material/Delete";
import AddIcon from "@mui/icons-material/Add";
import type { SpinWheelPrize } from "../phone/Screen";
import type { SpinWheelModuleContent } from '../../App';
import SegmentColorPickerModal from "./SegmentColorPickerModal";
import CountryCodeSelector, { countryCodes } from '../common/CountryCodeSelector';

interface SpinWheelEditorProps {
  initialContent: Partial<SpinWheelModuleContent>;
  onContentChange: (newContent: SpinWheelModuleContent) => void;
}

const SpinWheelEditor: React.FC<SpinWheelEditorProps> = ({ initialContent, onContentChange }) => {
  const prevContentPayloadRef = useRef<string | null>(null);
  const [prizes, setPrizes] = useState<SpinWheelPrize[]>(initialContent?.prizes || [
    { id: 'prize-1', text: '10% Off', color: '#FF6B35' },
    { id: 'prize-2', text: 'Free Shipping', color: '#4CAF50' },
    { id: 'prize-3', text: 'Try Again', color: '#9E9E9E' }
  ]);
  const [whatsappNumber, setWhatsappNumber] = useState(initialContent?.whatsappNumber || '');
  const [countryCode, setCountryCode] = useState(initialContent?.countryCode || '');
  const [headingText, setHeadingText] = useState(initialContent?.headingText || 'Spin to Win!');
  const [subTextContent, setSubTextContent] = useState(initialContent?.subTextContent || 'Spin the wheel for a chance to win prizes!');

  useEffect(() => {
  const fetchCountryCode = async () => {
    try {
      const response = await fetch('https://ip-api.com/json');
      const data = await response.json();
      if (data.status === 'success') {
        const countryName = data.country;
        const matchingCountry = countryCodes.find(c => c.name === countryName);
        if (matchingCountry) {
          setCountryCode(matchingCountry.code);
        }
      }
    } catch (error) {
      console.error('Failed to fetch IP-based country:', error);
    }
  };

  if (!countryCode) {
    fetchCountryCode();
  }
}, [countryCode]);
const contentPayload = useMemo((): SpinWheelModuleContent => ({
    headingText,
    subTextContent,
    prizes,
    whatsappNumber,
    countryCode,
  }), [headingText, subTextContent, prizes, whatsappNumber, countryCode]);

  useEffect(() => {
    const currentPayloadString = JSON.stringify(contentPayload);
    if (prevContentPayloadRef.current !== currentPayloadString) {
      onContentChange(contentPayload);
      prevContentPayloadRef.current = currentPayloadString;
    }
  }, [contentPayload, onContentChange]);
  const [colorModalOpen, setColorModalOpen] = useState(false);
  const [selectedPrizeIndex, setSelectedPrizeIndex] = useState<number | null>(null);

  // Remove localWhatsappNumber, localCountryCode, isEditingWhatsapp, handleEditWhatsapp, handleSaveWhatsapp, handleCancelWhatsapp

  const addPrize = () => {
    const newPrize: SpinWheelPrize = {
      id: `prize-${Date.now()}`,
      text: 'New Prize',
      color: '#FF6B35',
    };
    setPrizes([...prizes, newPrize]);
  };

  const removePrize = (index: number) => {
    setPrizes(prizes.filter((_, i) => i !== index));
  };

  const updatePrize = (index: number, field: keyof SpinWheelPrize, value: string) => {
    const updatedPrizes = prizes.map((prize, i) => 
      i === index ? { ...prize, [field]: value } : prize
    );
    setPrizes(updatedPrizes);
  };

  const openColorModal = (index: number) => {
    setSelectedPrizeIndex(index);
    setColorModalOpen(true);
  };

  const handleColorChange = (color: string) => {
    if (selectedPrizeIndex !== null) {
      updatePrize(selectedPrizeIndex, 'color', color);
    }
    setColorModalOpen(false);
    setSelectedPrizeIndex(null);
  };

  return (
    <Box sx={{ p: 2 }}>
      <Typography variant="h6" gutterBottom>Spin Wheel Editor</Typography>
      
      <Typography variant="h6" sx={{ mt: 4, mb: 2, fontWeight: 600 }}>
        WhatsApp Configuration
      </Typography>
      <Box sx={{ display: 'flex', gap: 2, alignItems: 'flex-start', mb: 2 }}>
        <CountryCodeSelector
          value={countryCode}
          onChange={setCountryCode}
          size="small"
        />
        <TextField
          fullWidth
          label="WhatsApp Number"
          value={whatsappNumber}
          onChange={(e) => {
            const value = e.target.value.replace(/[^0-9]/g, '');
            setWhatsappNumber(value);
          }}
          placeholder="1234567890 (digits only, no leading zero)"
          size="small"
          helperText="Enter the phone number digits only (no leading zero) for prize claims"
        />
      </Box>
      
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="subtitle1" gutterBottom>General Settings</Typography>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <TextField
                label="Main Heading" size="small"
                fullWidth
                variant="outlined"
                value={headingText}
                onChange={(e) => setHeadingText(e.target.value)}
                sx={{ mb: 2 }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="Subtext / Instructions" size="small"
                fullWidth
                multiline
                rows={3}
                variant="outlined"
                value={subTextContent}
                onChange={(e) => setSubTextContent(e.target.value)}
                sx={{ mb: 2 }}
              />
            </Grid>
          </Grid>
        </CardContent>
      </Card>
      
      <Typography variant="subtitle1" gutterBottom sx={{ mt: 3, mb: 2 }}>Prizes</Typography>
      <List>
        {prizes.map((prize, index) => (
          <ListItem key={prize.id || index} sx={{ mb: 2 }}>
            <Paper sx={{ p: 2, width: '100%', position: 'relative' }}>
              <IconButton
                onClick={() => removePrize(index)}
                color="error"
                size="small"
                sx={{ position: 'absolute', top: 8, right: 8 }}
              >
                <DeleteIcon fontSize="small" />
              </IconButton>
              <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', pr: 5 }}>
                <TextField
                  label="Prize Text"
                  value={prize.text}
                  onChange={(e) => updatePrize(index, 'text', e.target.value)}
                  size="small"
                  sx={{ flex: 1 }}
                />
                <Button
                  variant="outlined"
                  onClick={() => openColorModal(index)}
                  sx={{ 
                    backgroundColor: prize.color, 
                    color: 'white',
                    minWidth: 80,
                    '&:hover': {
                      backgroundColor: prize.color,
                      opacity: 0.8
                    }
                  }}
                >
                  Color
                </Button>
              </Box>
            </Paper>
          </ListItem>
        ))}
      </List>
      
      <Button
        variant="outlined"
        startIcon={<AddIcon />}
        onClick={addPrize}
        sx={{ mt: 2 }}
      >
        Add Prize
      </Button>

      <SegmentColorPickerModal
        open={colorModalOpen}
        value={selectedPrizeIndex !== null ? prizes[selectedPrizeIndex]?.color || '#FF6B35' : '#FF6B35'}
        onChange={handleColorChange}
        onClose={() => {
          setColorModalOpen(false);
          setSelectedPrizeIndex(null);
        }}
      />
    </Box>
  );
};

export default SpinWheelEditor;
