import React, { useState, useRef, useCallback, useEffect } from 'react';
import type { FC } from 'react';
import { 
  Box, 
  Typography, 
  IconButton, 
  Paper, 
  Tabs, 
  Tab, 
  Button,
  styled
} from '@mui/material';
import type { BoxProps, ButtonProps } from '@mui/material';
import MediaCropper from './MediaCropper';
import DeleteIcon from '@mui/icons-material/Delete';
import AddPhotoAlternateIcon from '@mui/icons-material/AddPhotoAlternate';
import VideocamIcon from '@mui/icons-material/Videocam';

interface Screen {
  id: string;
  name: string;
  media?: string;
  modules: string[];
}

interface MediaItem {
  id: string;
  url: string;
  type: 'image' | 'video';
  thumbnail?: string;
}

type OnCropComplete = (croppedArea: any, croppedAreaPixels: any) => void;

interface BackgroundSelectorProps {
  selectedBackground: string | null;
  onSelectBackground: (url: string | null) => void;
  modules: Array<{ id: string; type: string; title: string }>;
}

// Styled components
const StyledBox = styled(Box)<BoxProps>(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  gap: theme.spacing(2),
}));

const MediaThumbnail = styled(Box)<BoxProps & { isSelected?: boolean }>(({ theme, isSelected }) => ({
  width: 80,
  height: 80,
  borderRadius: 4,
  backgroundSize: 'cover',
  backgroundPosition: 'center',
  cursor: 'pointer',
  position: 'relative',
  border: isSelected ? `2px solid ${theme.palette.primary.main}` : '1px solid #e0e0e0',
  '&:hover': {
    opacity: 0.8,
  },
}));

const AddMediaButton = styled(Button)<ButtonProps>(({ theme }) => ({
  width: 80,
  height: 80,
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  gap: theme.spacing(1),
  border: '1px dashed #ccc',
  '&:hover': {
    borderColor: theme.palette.primary.main,
  },
}));

const GalleryContainer = styled(Box)<BoxProps>({
  display: 'flex',
  flexWrap: 'wrap',
  gap: 8,
  marginTop: 8,
  maxHeight: 300,
  overflowY: 'auto',
  padding: 8,
});

const ScreenButton = styled(Button)<ButtonProps & { isActive?: boolean }>(({ theme, isActive }) => ({
  textTransform: 'none',
  justifyContent: 'space-between',
  backgroundColor: isActive ? theme.palette.action.selected : 'transparent',
  '&:hover': {
    backgroundColor: theme.palette.action.hover,
  },
}));

const saveGallery = (gallery: MediaItem[]) => {
  localStorage.setItem('mediaGallery', JSON.stringify(gallery));
};

const loadGallery = (): MediaItem[] => {
  const saved = localStorage.getItem('mediaGallery');
  return saved ? JSON.parse(saved) : [];
};

const StyledBox = styled(Box)({
  padding: '16px',
  borderRadius: '8px',
  backgroundColor: '#fff',
  boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
});

const MediaThumbnail = styled(Box, {
  shouldForwardProp: (prop) => prop !== 'isSelected',
})<{ isSelected?: boolean }>(({ isSelected, theme }) => ({
  flexShrink: 0,
  width: '64px',
  height: '64px',
  borderRadius: '8px',
  backgroundSize: 'cover',
  backgroundPosition: 'center',
  cursor: 'pointer',
  position: 'relative',
  border: isSelected ? '2px solid #4f46e5' : '2px solid transparent',
  '&:hover': {
    borderColor: theme.palette.primary.main,
  },
}));

const AddMediaButton = styled(Box)(({ theme }) => ({
  flexShrink: 0,
  width: '64px',
  height: '64px',
  border: '2px dashed',
  borderColor: theme.palette.grey[400],
  borderRadius: '8px',
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  color: theme.palette.grey[500],
  cursor: 'pointer',
  '&:hover': {
    borderColor: theme.palette.primary.main,
    color: theme.palette.primary.main,
    backgroundColor: 'rgba(0, 0, 0, 0.02)',
  },
}));

const GalleryContainer = styled(Box)({
  display: 'flex',
  flexWrap: 'wrap',
  gap: '16px',
  overflowX: 'auto',
  padding: '8px 0',
  marginTop: '16px',
});

const ScreenButton = styled(Box, {
  shouldForwardProp: (prop) => !['isActive'].includes(prop as string),
})<{ isActive?: boolean }>(({ isActive, theme }) => ({
  padding: '8px 12px',
  borderRadius: '4px',
  backgroundColor: isActive ? theme.palette.primary.main : theme.palette.grey[200],
  color: isActive ? '#fff' : theme.palette.text.primary,
  cursor: 'pointer',
  '&:hover': {
    backgroundColor: isActive ? theme.palette.primary.dark : theme.palette.grey[300],
  },
}));

// Helper functions
const loadGallery = (): MediaItem[] => {
  try {
    const saved = localStorage.getItem('mediaGallery');
    return saved ? JSON.parse(saved) : [];
  } catch (error) {
    console.error('Failed to load gallery:', error);
    return [];
  }
};

const saveGallery = (gallery: MediaItem[]) => {
  try {
    localStorage.setItem('mediaGallery', JSON.stringify(gallery));
  } catch (error) {
    console.error('Failed to save gallery:', error);
  }
};

const BackgroundSelector: FC<BackgroundSelectorProps> = ({
  selectedBackground,
  onSelectBackground,
  modules,
}) => {
  // State management
  const [gallery, setGallery] = useState<MediaItem[]>([]);
  const [currentMedia, setCurrentMedia] = useState<MediaItem | null>(null);
  const [isCropperOpen, setIsCropperOpen] = useState(false);
  const [screens, setScreens] = useState<Screen[]>([
    { id: '1', name: 'Screen 1', modules: [] }
  ]);
  const [currentScreenIndex, setCurrentScreenIndex] = useState(0);
  const [tabValue, setTabValue] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const currentScreen = screens[currentScreenIndex] || screens[0];
  
  // Load gallery from localStorage on mount
  useEffect(() => {
    const savedGallery = localStorage.getItem('mediaGallery');
    if (savedGallery) {
      try {
        setGallery(JSON.parse(savedGallery));
      } catch (error) {
        console.error('Failed to parse saved gallery', error);
      }
    }
  }, []);
  
  // Save gallery to localStorage when it changes
  useEffect(() => {
    if (gallery.length > 0) {
      localStorage.setItem('mediaGallery', JSON.stringify(gallery));
    }
  }, [gallery]);
  
  const handleMediaSelect = useCallback((media: MediaItem) => {
    onSelectBackground(media.url);
    setCurrentMedia(media);
  }, [onSelectBackground]);
  
  const handleFileUpload = useCallback((file: File) => {
    const reader = new FileReader();
    
    reader.onload = (e) => {
      const url = e.target?.result as string;
      const mediaType = file.type.startsWith('video/') ? 'video' : 'image';
      
      const newMedia: MediaItem = {
        id: Date.now().toString(),
        url,
        type: mediaType,
      };
      
      setGallery(prev => [...prev, newMedia]);
      onSelectBackground(url);
      setCurrentMedia(newMedia);
    };
    
    reader.onerror = () => {
      console.error('Error reading file');
    };
    
    reader.readAsDataURL(file);
  }, [onSelectBackground]);
  
  const handleCropComplete = useCallback((croppedArea: any, croppedAreaPixels: any) => {
    // Handle crop completion logic here
    console.log('Crop completed', { croppedArea, croppedAreaPixels });
  }, []);
  
  const handleDeleteMedia = useCallback((mediaId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    setGallery(prev => {
      const newGallery = prev.filter(item => item.id !== mediaId);
      return newGallery;
    });
    
    if (currentMedia?.id === mediaId) {
      onSelectBackground(null);
      setCurrentMedia(null);
    }
  }, [currentMedia, onSelectBackground]);
  
  const handleTabChange = useCallback((_: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  }, []);
  
  const handleAddScreen = useCallback(() => {
    const newScreen: Screen = {
      id: Date.now().toString(),
      name: `Screen ${screens.length + 1}`,
      modules: [],
    };
    setScreens(prev => [...prev, newScreen]);
    setCurrentScreenIndex(screens.length);
  }, [screens.length]);
  
  const handleDeleteScreen = useCallback((index: number, e: React.MouseEvent) => {
    e.stopPropagation();
    if (screens.length <= 1) return;
    
    setScreens(prev => prev.filter((_, i) => i !== index));
    setCurrentScreenIndex(prev => (index === prev ? 0 : prev > index ? prev - 1 : prev));
  }, [screens.length]);
  
  const handleScreenSelect = useCallback((index: number) => {
    setCurrentScreenIndex(index);
  }, []);
  
  const handleCloseCropper = useCallback(() => {
    setIsCropperOpen(false);
  }, []);

  // Load gallery from localStorage on mount
  useEffect(() => {
    const savedGallery = loadGallery();
    if (savedGallery.length > 0) {
      setGallery(savedGallery);
    }
  }, []);

  // Save gallery to localStorage when it changes
  useEffect(() => {
    saveGallery(gallery);
  }, [gallery]);
  
  const handleCloseCropper = () => {
    setIsCropperOpen(false);
  };

  // Media handling functions
  const handleMediaSelect = (media: MediaItem) => {
    setCurrentMedia(media);
    onSelectBackground(media.url);
    
    // Update current screen's media
    setScreens(prev => {
      const newScreens = [...prev];
      if (newScreens[currentScreenIndex]) {
        newScreens[currentScreenIndex] = {
          ...newScreens[currentScreenIndex],
          media: media.url
        };
      }
      return newScreens;
    });
  };

  const handleDeleteMedia = (id: string, e: React.MouseEvent) => {
    e.stopPropagation();
    setGallery(prev => prev.filter(item => item.id !== id));
    if (currentMedia?.id === id) {
      onSelectBackground(null);
      setCurrentMedia(null);
    }
  };

  const handleCropComplete = useCallback<OnCropComplete>((_croppedArea, croppedAreaPixels) => {
    // Implementation for crop completion
    if (!currentMedia) return;
    
    const image = new Image();
    image.src = currentMedia.url;
    
    image.onload = () => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      if (!ctx) return;
      
      const { width, height, x, y } = croppedAreaPixels;
      
      canvas.width = width;
      canvas.height = height;
      
      ctx.drawImage(image, x, y, width, height, 0, 0, width, height);
      
      canvas.toBlob((blob) => {
        if (!blob) return;
        
        const croppedUrl = URL.createObjectURL(blob);
        const updatedMedia = { ...currentMedia, url: croppedUrl };
        
        setGallery(prev => 
          prev.map(item => 
            item.id === currentMedia.id ? updatedMedia : item
          )
        );
        
        setCurrentMedia(updatedMedia);
        onSelectBackground(croppedUrl);
      }, 'image/jpeg', 0.9);
    };
  }, [currentMedia, onSelectBackground]);

  // Screen management functions
  const handleAddScreen = () => {
    const newScreen: Screen = {
      id: Date.now().toString(),
      name: `Screen ${screens.length + 1}`,
      modules: [],
    };
    setScreens(prev => [...prev, newScreen]);
    setCurrentScreenIndex(screens.length);
  };

  const handleDeleteScreen = (index: number) => {
    if (screens.length <= 1) return;
    
    setScreens(prev => {
      const newScreens = prev.filter((_, i) => i !== index);
      if (currentScreenIndex >= index) {
        setCurrentScreenIndex(prev => Math.max(0, prev - 1));
      }
      return newScreens;
    });
  };

  const handleScreenSelect = (index: number) => {
    setCurrentScreenIndex(index);
    const screenMedia = screens[index]?.media;
    if (screenMedia) {
      onSelectBackground(screenMedia);
    } else {
      onSelectBackground(null);
    }
  };

  const handleMediaSelectForScreen = (screenIndex: number, mediaUrl: string) => {
    setScreens(prev => {
      const updatedScreens = [...prev];
      updatedScreens[screenIndex] = {
        ...updatedScreens[screenIndex],
        media: mediaUrl
      };
      return updatedScreens;
    });
  };

  const handleTabChange = (_: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleFileInputChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    const fileType = file.type.startsWith('image/') ? 'image' : 'video';
    const fileUrl = URL.createObjectURL(file);

    const newMediaItem: MediaItem = {
      id: Date.now().toString(),
      url: fileUrl,
      type: fileType,
    };

    if (fileType === 'video') {
      // Generate thumbnail for video
      const video = document.createElement('video');
      const canvas = document.createElement('canvas');
      video.src = fileUrl;
      
      video.onloadeddata = () => {
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        const ctx = canvas.getContext('2d');
        if (ctx) {
          ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
          const thumbnail = canvas.toDataURL('image/jpeg');
          newMediaItem.thumbnail = thumbnail;
          updateMediaItem(newMediaItem);
        }
      };
    } else {
      setGallery(prev => [...prev, newMediaItem]);
      handleMediaSelect(newMediaItem);
      setCurrentMedia(newMediaItem);
      setIsCropperOpen(true);
    }

    if (e.target) {
      e.target.value = '';
    }
  };

  const updateMediaItem = (mediaItem: MediaItem) => {
    setGallery(prev => [...prev, mediaItem]);
    handleMediaSelect(mediaItem);
    setCurrentMedia(mediaItem);
    if (mediaItem.type === 'image') {
      setIsCropperOpen(true);
    }
  };
  
  const handleButtonClick = (action: 'crop' | 'delete') => {
    if (!currentMedia) return;
    
    switch (action) {
      case 'crop':
        if (currentMedia.type === 'image') {
          setIsCropperOpen(true);
        }
        break;
      case 'delete':
        setGallery(prev => prev.filter(m => m.id !== currentMedia.id));
        onSelectBackground(null);
        setCurrentMedia(null);
        break;
    }
  };

  const handleButtonClick = (action: 'crop' | 'delete') => {
    switch (action) {
      case 'crop':
        if (currentMedia?.type === 'image') {
          setIsCropperOpen(true);
        }
        break;
      case 'delete':
        if (currentMedia) {
          setGallery(prev => prev.filter(m => m.id !== currentMedia.id));
          onSelectBackground(null);
          setCurrentMedia(null);
        }
        break;
    }
  }; => {
  // State management
  const [gallery, setGallery] = useState<MediaItem[]>(loadGallery());
  const [isCropperOpen, setIsCropperOpen] = useState(false);
  const [currentMedia, setCurrentMedia] = useState<MediaItem | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [screens, setScreens] = useState<Screen[]>([
    { 
      id: '1', 
      name: 'Screen 1', 
      modules: modules.map(m => m.id),
      media: undefined
    }
  ]);
  const [currentScreenIndex, setCurrentScreenIndex] = useState(0);
  const currentScreen = screens[currentScreenIndex];

  // Save gallery to localStorage when it changes
  useEffect(() => {
    saveGallery(gallery);
  }, [gallery]);

  // Remove unused currentMediaType since we're getting the type from currentMedia

  // Save gallery to localStorage when it changes
  useEffect(() => {
    saveGallery(gallery);
  }, [gallery]);
  
  const generateVideoThumbnail = (file: File): Promise<string> => {
    return new Promise((resolve) => {
      const video = document.createElement('video');
      video.src = URL.createObjectURL(file);
      video.onloadedmetadata = () => {
        video.currentTime = Math.min(1, video.duration * 0.1); // Get a frame at 10% of video duration
      };
      video.onseeked = () => {
        const canvas = document.createElement('canvas');
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        const ctx = canvas.getContext('2d');
        if (ctx) {
          ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
          resolve(canvas.toDataURL('image/jpeg'));
        } else {
          resolve('');
        }
      };
      video.onerror = () => resolve('');
    });
  };

  const handleMediaSelect = useCallback((media: MediaItem) => {
    setCurrentMedia(media);
    onSelectBackground(media.url);
  }, [onSelectBackground]);

  const handleDeleteMedia = useCallback((id: string, e: React.MouseEvent) => {
    e.stopPropagation();
    setGallery(prev => {
      const newGallery = prev.filter(media => media.id !== id);
      if (currentMedia?.id === id) {
        onSelectBackground(null);
        setCurrentMedia(null);
      }
      return newGallery;
    });
  }, [currentMedia, onSelectBackground]);

  const handleCropComplete = useCallback<OnCropComplete>(async (_, croppedAreaPixels) => {
    if (!currentMedia) return;
    
    try {
      const image = new Image();
      image.src = currentMedia.url;
      image.crossOrigin = 'Anonymous';
      
      await new Promise<void>((resolve, reject) => {
        image.onload = () => resolve();
        image.onerror = () => reject(new Error('Failed to load image'));
      });
      
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      if (!ctx) throw new Error('Could not get canvas context');
      
      canvas.width = croppedAreaPixels.width;
      canvas.height = croppedAreaPixels.height;
      
      ctx.drawImage(
        image,
        croppedAreaPixels.x,
        croppedAreaPixels.y,
        croppedAreaPixels.width,
        croppedAreaPixels.height,
        0,
        0,
        croppedAreaPixels.width,
        croppedAreaPixels.height
      );
      
      const croppedImageUrl = canvas.toDataURL('image/jpeg');
      
      const newMediaItem: MediaItem = {
        id: `cropped-${Date.now()}`,
        url: croppedImageUrl,
        type: currentMedia.type,
        thumbnail: croppedImageUrl,
      };

      setGallery(prev => [...prev, newMediaItem]);
      handleMediaSelect(newMediaItem);
      setIsCropperOpen(false);
    } catch (error) {
      console.error('Error during image crop:', error);
    }
  }, [currentMedia, handleMediaSelect]);

  const handleCloseCropper = useCallback(() => {
    setIsCropperOpen(false);
  }, []);

  const handleButtonClick = useCallback((action: string) => {
    switch (action) {
      case 'crop':
        if (currentMedia) {
          setIsCropperOpen(true);
        }
        break;
      case 'delete':
        if (selectedBackground && currentMedia) {
          // Remove the current media from the gallery
          setGallery(prev => prev.filter(m => m.id !== currentMedia.id));
          // Clear the selection
          onSelectBackground(null);
          setCurrentMedia(null);
        }
        break;
      default:
        break;
    }
  }, [currentMedia, onSelectBackground, selectedBackground]);

  const handleAddScreen = () => {
    const newScreen: Screen = {
      id: Date.now().toString(),
      name: `Screen ${screens.length + 1}`,
      modules: [],
      media: undefined
    };
    setScreens(prev => [...prev, newScreen]);
    setCurrentScreenIndex(screens.length);
  };

  const handleDeleteScreen = (index: number) => {
    if (screens.length <= 1) return; // Don't delete the last screen
    
    setScreens(prev => {
      const newScreens = prev.filter((_, i) => i !== index);
      // Adjust current screen index if needed
      if (currentScreenIndex >= index) {
        setCurrentScreenIndex(Math.max(0, currentScreenIndex - 1));
      }
      return newScreens;
    });
  };

  const handleScreenSelect = (index: number) => {
    setCurrentScreenIndex(index);
    const screenMedia = screens[index]?.media;
    if (screenMedia) {
      onSelectBackground(screenMedia);
    }
  };

  const handleMediaSelectForScreen = (screenIndex: number, mediaUrl: string) => {
    setScreens(prev => {
      const updatedScreens = [...prev];
      updatedScreens[screenIndex] = {
        ...updatedScreens[screenIndex],
        media: mediaUrl
      };
      
      if (screenIndex === currentScreenIndex) {
        onSelectBackground(mediaUrl);
      }
      
      return updatedScreens;
    });
  };

  const [tabValue, setTabValue] = useState(0);
  
  const handleTabChange = (_: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleFileInputChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    const fileType = file.type.startsWith('image/') ? 'image' : 'video';
    const fileUrl = URL.createObjectURL(file);

    const newMediaItem: MediaItem = {
      id: Date.now().toString(),
      url: fileUrl,
      type: fileType,
    };

    if (fileType === 'video') {
      const thumbnail = await generateVideoThumbnail(file);
      newMediaItem.thumbnail = thumbnail;
    }

    setGallery(prev => [...prev, newMediaItem]);
    handleMediaSelect(newMediaItem);

    if (fileType === 'image') {
      setCurrentMedia(newMediaItem);
      setIsCropperOpen(true);
    }

    // Reset file input
    if (e.target) {
      e.target.value = '';
    }
  };

      }
    };
    
    reader.onerror = () => {
      console.error('Error reading file');
    };
    
    reader.readAsDataURL(file);
    
    // Reset the file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <Paper elevation={0} sx={{ p: 3, borderRadius: '8px', boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)', mb: 8 }}>
      <Tabs 
        value={tabValue} 
        onChange={handleTabChange}
        sx={{ mb: 2 }}
        aria-label="background selector tabs"
      >
        <Tab label="Media" />
        <Tab label="Screens" />
      </Tabs>
      
      {tabValue === 0 ? (
        <Box sx={{ mb: 4 }}>
          <Typography variant="subtitle2" gutterBottom>
            Media Gallery
          </Typography>
          <Typography variant="body2" color="textSecondary" sx={{ mb: 2 }}>
            Select or upload media for your experience.
          </Typography>
        </Box>
      ) : (
        <Box sx={{ mb: 4 }}>
          <ScreenSelector
            screens={screens}
            currentScreenIndex={currentScreenIndex}
            onScreenSelect={handleScreenSelect}
            onScreenAdd={handleAddScreen}
            onScreenDelete={handleDeleteScreen}
            onMediaSelect={handleMediaSelectForScreen}
          />
        </Box>
      )}
      
      <Box sx={{ mt: 2, p: 2, bgcolor: 'background.paper', borderRadius: 1 }}>
        <input
          type="file"
          accept="image/*,video/*"
          onChange={handleFileInputChange}
          style={{ display: 'none' }}
          ref={fileInputRef}
        />
        <AddMediaButton onClick={() => fileInputRef.current?.click()}>
          <span className="material-icons" style={{ fontSize: '24px' }}>add_photo_alternate</span>
          <Typography variant="caption" sx={{ fontSize: '0.75rem' }}>Add Media</Typography>
        </AddMediaButton>
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mt: 2 }}>
          {gallery.map((media) => (
            <MediaThumbnail
              key={media.id}
              onClick={() => {
                if (tabValue === 0) {
                  onSelectBackground(media.url);
                } else {
                  handleMediaSelectForScreen(currentScreenIndex, media.url);
                }
              }}
              sx={{
                backgroundImage: `url(${media.thumbnail || media.url})`,
                border: (tabValue === 0 ? selectedBackground === media.url : 
                  screens[currentScreenIndex]?.media === media.url) ? 
                  '2px solid #3f51b5' : '1px solid #e0e0e0',
              }}
            >
              {media.type === 'video' && (
                <Box sx={{
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%, -50%)',
                  color: 'white',
                  backgroundColor: 'rgba(0,0,0,0.5)',
                  borderRadius: '50%',
                  width: 40,
                  height: 40,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                  <span className="material-icons">play_arrow</span>
                </Box>
              )}
            </MediaThumbnail>
          ))}
        </Box>
      </Box>

      {currentMedia && (
        <Box sx={{ mt: 2, display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            onClick={() => handleButtonClick('crop')}
            disabled={!currentMedia || currentMedia.type !== 'image'}
            aria-label="Crop image"
          >
            Crop
          </Button>
          <Button
            variant="outlined"
            color="error"
            onClick={() => handleButtonClick('delete')}
            aria-label="Delete media"
          >
            Delete
          </Button>
        </Box>
      )}

      <MediaCropper
        open={isCropperOpen}
        onClose={handleCloseCropper}
        imageUrl={currentMedia?.url || ''}
        onCropComplete={handleCropComplete}
      />
            <MediaThumbnail
              key={media.id}
              isSelected={selectedBackground === media.url}
              onClick={() => handleMediaSelect(media)}
              sx={{
                backgroundImage: `url(${media.thumbnail || media.url})`,
              }}
            }}
          >
            {media.type === 'video' && (
              <Box sx={{
              onClick={() => handleMediaSelect(media)}
              sx={{
                backgroundImage: `url(${media.thumbnail || media.url})`,
                border: selectedBackground === media.url ? '2px solid #3f51b5' : '1px solid #e0e0e0',
              }}
            >
              {media.type === 'video' && (
                <Box sx={{
                  position: 'absolute',
                  inset: 0,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  backgroundColor: 'rgba(0,0,0,0.3)'
                }}>
                  <span className="material-icons" style={{ color: 'white', fontSize: '24px' }}>play_circle</span>
                </Box>
              )}
              <Box className="media-actions" sx={{
                position: 'absolute',
                top: 0,
                right: 0,
                opacity: 0,
                transition: 'opacity 0.2s',
                '&:hover': {
                  opacity: 1,
                },
              }}>
                <IconButton 
                  size="small" 
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDeleteMedia(media.id, e);
                  }}
                  sx={{ 
                    bgcolor: 'rgba(0,0,0,0.5)',
                    color: 'white',
                    '&:hover': { bgcolor: 'rgba(0,0,0,0.7)' },
                    p: 0.5
                  }}
                >
                  <DeleteIcon sx={{ fontSize: 16 }} />
                </IconButton>
              </Box>
            </MediaThumbnail>
          ))}
        </Box>
      </Box>
      
      {/* Action Buttons */}
      <Box sx={{ display: 'flex', gap: '8px', overflowX: 'auto', pb: '8px' }}>
        <IconButton 
          onClick={() => handleButtonClick('crop')} 
          disabled={!selectedBackground}
          sx={{ 
            p: '8px', 
            color: selectedBackground ? '#6b7280' : '#d1d5db',
            '&:hover': { 
              backgroundColor: selectedBackground ? '#f3f4f6' : 'transparent',
              cursor: selectedBackground ? 'pointer' : 'not-allowed'
            }
          }}
        >
          <span className="material-icons">crop</span>
        </IconButton>
        <IconButton 
          onClick={() => handleButtonClick('delete')} 
          disabled={!selectedBackground}
          sx={{ 
            p: '8px', 
            color: selectedBackground ? '#ef4444' : '#fca5a5',
            '&:hover': { 
              backgroundColor: selectedBackground ? '#fee2e2' : 'transparent',
              cursor: selectedBackground ? 'pointer' : 'not-allowed'
            }
          }}
        >
          <span className="material-icons">delete</span>
        </IconButton>
      </Box>
      
      {/* Media Cropper Dialog */}
      {currentMedia && (
        <MediaCropper
          open={isCropperOpen}
          onClose={handleCloseCropper}
          mediaUrl={currentMedia.url}
          mediaType={currentMedia.type}
          onCropComplete={handleCropComplete}
        />
      )}
    </Paper>
  );

};

export default BackgroundSelector;
