[build]
  # Build command to run - force clean install to fix Rollup native dependency issues
  command = "cd experience-builder-ui && rm -rf node_modules package-lock.json && npm install --force && npm run build"
  
  # Directory to publish (relative to base)
  publish = "experience-builder-ui/dist"
  
  # Base directory for the build
  base = "."

[build.environment]
  # Node version
  NODE_VERSION = "18"
  # Ensure optional dependencies are installed (fixes Rollup native binary issues)
  NPM_CONFIG_OPTIONAL = "true"

# Netlify Dev configuration for local development
[dev]
  # Command to start the development server
  command = "cd experience-builder-ui && npm run dev"
  
  # Port that Netlify Dev is accessible from in the browser
  port = 8888
  
  # Port for your application server (Vite dev server)
  targetPort = 5173
  
  # Path to your static content folder (matches build publish)
  publish = "experience-builder-ui/dist"
  
  # Use custom framework since we specify both command and targetPort
  framework = "#custom"
  
  # Auto-launch browser when starting dev server
  autoLaunch = true

# Redirect rules for SPA
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
