import React, { useState, useRef, useCallback, memo } from "react";
import {
  Box,
  Typography,
  TextField,
  Button,
  IconButton,
  List,
  ListItemText,
  Paper,
} from "@mui/material";
import Switch from "./ui/Switch";
import { styled } from "@mui/material/styles";
import DeleteIcon from "@mui/icons-material/Delete";
import AddIcon from "@mui/icons-material/Add";
import UploadFileIcon from "@mui/icons-material/UploadFile";
import {
  Article,
  ShoppingCart,
  ChatCircle,
  Shield,
  Cursor,
  UserPlus,
  Star,
  User,
  Sparkle,
  CircleNotch,
  Receipt,
  ChatText,
} from "phosphor-react";

import type { ScratchCardModuleContent } from "../App";
import ScratchCardEditor from "./modules/ScratchCardEditor";
import SpinWheelEditor from "./modules/SpinWheelEditor";
import type {
  Module,
  SetupGuideContent,
  SetupStep,
  WarrantyContent,
  ShoppingLinksContent,
  ShoppingLink,

  CustomButtonContent,
  RegistrationModuleContent,
  FormSurveyModuleContent,
  DiscountModuleContent,
  DocumentModuleContent,
  LinkModuleContent,
  SpinWheelModuleContent,
  UserDetailsContent,
  UserDetailsFieldSetting, // Added this import
  CustomizableButtonStyles,
  CSATModuleContent,
} from "../App";


import SegmentColorPickerModal from "./modules/SegmentColorPickerModal";

import { useDrag, useDrop } from "react-dnd";

const ModuleContainer = styled(Paper)(({ theme }) => ({
  display: "flex",
  flexDirection: "column",
  padding: theme.spacing(2),
  borderRadius: theme.shape.borderRadius,
  boxShadow: theme.shadows[1],
  marginBottom: theme.spacing(1.5),
  backgroundColor: theme.palette.background.paper,
  overflow: "hidden",
}));



interface ModuleItemProps {
  module: Module;
  onRemoveModule?: (moduleId: string) => void;
  index: number;
  onMoveModule?: (dragIndex: number, hoverIndex: number) => void;
  onUpdateContent: (moduleId: string, newContent: Module["content"]) => void;
}

const ModuleItem: React.FC<ModuleItemProps> = memo(({
  module,
  onRemoveModule,
  index,
  onMoveModule,
  onUpdateContent,
}) => {
  const ref = useRef<HTMLDivElement>(null);
  const [isEditing, setIsEditing] = useState(false);

  const [newStepDescription, setNewStepDescription] = useState("");
  const [newLinkUrl, setNewLinkUrl] = useState("");
  const [newLinkDisplay, setNewLinkDisplay] = useState("");

  const [colorModalOpen, setColorModalOpen] = useState(false);
  const [colorPickerType, setColorPickerType] = useState<'text' | 'background' | 'border'>('text');
  const shoppingMediaInputRef = useRef<HTMLInputElement>(null);

  const handleScratchCardContentChange = useCallback(
    (newContent: ScratchCardModuleContent) => {
      onUpdateContent(module.id, newContent as Module["content"]);
    },
    [module.id, onUpdateContent],
  );

  const [, drop] = useDrop<{ id: string; index: number }, void, unknown>({
    accept: "MODULE",
    hover: (item) => {
      if (!ref.current || !onMoveModule) return;
      const dragIndex = item.index;
      const hoverIndex = index;
      if (dragIndex === hoverIndex) return;
      onMoveModule(dragIndex, hoverIndex);
      item.index = hoverIndex;
    },
  });

  const [{ isDragging }, drag, preview] = useDrag({
    type: "MODULE",
    item: { id: module.id, index },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  drag(drop(preview(ref))); // Apply drag to the preview, and drop to the combined ref

  const handleContentChange = useCallback((field: string, value: any) => {
    onUpdateContent(module.id, { ...module.content, [field]: value });
  }, [module.id, module.content, onUpdateContent]);

  const handleAddStep = () => {
    if (newStepDescription.trim() === "" || module.type !== "SetupGuide")
      return;
    const content = module.content as SetupGuideContent;
    const newStep: SetupStep = {
      id: Date.now().toString(),
      description: newStepDescription.trim(),
    };
    onUpdateContent(module.id, {
      ...content,
      steps: [...(content.steps || []), newStep],
    });
    setNewStepDescription("");
  };

  const handleDeleteStep = (stepIdToDelete: string) => {
    if (module.type !== "SetupGuide") return;
    const content = module.content as SetupGuideContent;
    onUpdateContent(module.id, {
      ...content,
      steps: (content.steps || []).filter((step) => step.id !== stepIdToDelete),
    });
  };

  const handleAddShoppingLink = () => {
    if (
      (newLinkUrl.trim() === "" && newLinkDisplay.trim() === "") ||
      module.type !== "ShoppingLinks"
    )
      return;
    const content = module.content as ShoppingLinksContent;
    const newLink: ShoppingLink = {
      id: Date.now().toString(),
      url: newLinkUrl.trim(),
      displayText: newLinkDisplay.trim(),
    };
    onUpdateContent(module.id, {
      ...content,
      links: [...(content.links || []), newLink],
    });
    setNewLinkUrl("");
    setNewLinkDisplay("");
  };

  const handleDeleteShoppingLink = (linkIdToDelete: string) => {
    if (module.type !== "ShoppingLinks") return;
    const content = module.content as ShoppingLinksContent;
    onUpdateContent(module.id, {
      ...content,
      links: (content.links || []).filter((link) => link.id !== linkIdToDelete),
    });
  };

  const handleShoppingLinkMediaChange = (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    if (module.type !== "ShoppingLinks") return;
    const file = event.target.files?.[0];
    if (!file) return;
    const content = module.content as ShoppingLinksContent;
    const mediaType = file.type.split("/")[1] as
      | "pdf"
      | "jpg"
      | "jpeg"
      | "png"
      | "svg";
    const mediaUrl = URL.createObjectURL(file);
    onUpdateContent(module.id, {
      ...content,
      mediaUrl,
      mediaType,
      mediaFileName: file.name,
    });
    if (event.target) event.target.value = "";
  };

  const handleRemoveShoppingLinkMedia = () => {
    if (module.type !== "ShoppingLinks") return;
    const content = module.content as ShoppingLinksContent;
    if (content.mediaUrl && content.mediaUrl.startsWith("blob:"))
      URL.revokeObjectURL(content.mediaUrl);
    onUpdateContent(module.id, {
      ...content,
      mediaUrl: undefined,
      mediaType: undefined,
      mediaFileName: undefined,
    });
  };



  const renderButtonCustomizationFields = (
    currentContent: Module["content"],
  ) => {
    const contentWithButtonStyles = currentContent as typeof currentContent &
      Partial<CustomizableButtonStyles>;
    return (
      <>
        <Typography
          variant="subtitle2"
          sx={{ mt: 2, mb: 1, pt: 2, borderTop: "1px solid #eee" }}
        >
          Button Customization
        </Typography>
        <TextField
          fullWidth
          label="Button Text"
          variant="outlined"
          size="small"
          value={contentWithButtonStyles.buttonText || ""}
          onChange={(e) => handleContentChange("buttonText", e.target.value)}
          sx={{ mb: 2 }}
          placeholder="Default (module specific)"
        />
        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', mb: 2 }}>
          <Box sx={{ flex: 1 }}>
            <Typography variant="caption" sx={{ mb: 1, display: 'block' }}>Text Color</Typography>
            <Button
              variant="outlined"
              onClick={() => {
                setColorPickerType('text');
                setColorModalOpen(true);
              }}
              sx={{ 
                backgroundColor: contentWithButtonStyles.buttonTextColor || '#FFFFFF', 
                color: 'white',
                minWidth: '100%',
                height: 40,
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                '&:hover': {
                  backgroundColor: contentWithButtonStyles.buttonTextColor || '#FFFFFF',
                  opacity: 0.8
                }
              }}
            >
              {contentWithButtonStyles.buttonTextColor || 'Select'}
            </Button>
          </Box>
          <Box sx={{ flex: 1 }}>
            <Typography variant="caption" sx={{ mb: 1, display: 'block' }}>Background</Typography>
            <Button
              variant="outlined"
              onClick={() => {
                setColorPickerType('background');
                setColorModalOpen(true);
              }}
              sx={{ 
                backgroundColor: contentWithButtonStyles.buttonBackgroundColor || '#007BFF', 
                color: 'white',
                minWidth: '100%',
                height: 40,
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                '&:hover': {
                  backgroundColor: contentWithButtonStyles.buttonBackgroundColor || '#007BFF',
                  opacity: 0.8
                }
              }}
            >
              {contentWithButtonStyles.buttonBackgroundColor || 'Select'}
            </Button>
          </Box>
          <Box sx={{ flex: 1 }}>
            <Typography variant="caption" sx={{ mb: 1, display: 'block' }}>Border</Typography>
            <Button
              variant="outlined"
              onClick={() => {
                setColorPickerType('border');
                setColorModalOpen(true);
              }}
              sx={{ 
                backgroundColor: contentWithButtonStyles.buttonBorderColor || '#007BFF', 
                color: 'white',
                minWidth: '100%',
                height: 40,
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                '&:hover': {
                  backgroundColor: contentWithButtonStyles.buttonBorderColor || '#007BFF',
                  opacity: 0.8
                }
              }}
            >
              {contentWithButtonStyles.buttonBorderColor || 'Select'}
            </Button>
          </Box>
        </Box>
        
        <SegmentColorPickerModal
          open={colorModalOpen}
          value={(() => {
            switch (colorPickerType) {
              case 'text': return contentWithButtonStyles.buttonTextColor || '#FFFFFF';
              case 'background': return contentWithButtonStyles.buttonBackgroundColor || '#007BFF';
              case 'border': return contentWithButtonStyles.buttonBorderColor || '#007BFF';
              default: return '#FFFFFF';
            }
          })()}
          onChange={(color: string) => {
            switch (colorPickerType) {
              case 'text': handleContentChange('buttonTextColor', color); break;
              case 'background': handleContentChange('buttonBackgroundColor', color); break;
              case 'border': handleContentChange('buttonBorderColor', color); break;
            }
            setColorModalOpen(false);
          }}
          onClose={() => setColorModalOpen(false)}
        />
      </>
    );
  };

  const getModuleIcon = (type: Module["type"]) => {
    const iconProps = { size: 20, weight: "duotone" as const };
    switch (type) {
      case "SetupGuide":
        return <Article {...iconProps} color="#10B981" />;
      case "Warranty":
        return <Shield {...iconProps} color="#6366F1" />;
      case "ShoppingLinks":
        return <ShoppingCart {...iconProps} color="#F59E0B" />;
      case "FeedbackSurvey":
        return <ChatCircle {...iconProps} color="#EC4899" />;
      case "CustomButton":
        return <Cursor {...iconProps} color="#8B5CF6" />;
      case "Registration":
        return <UserPlus {...iconProps} color="#3B82F6" />;
      case "ScratchCard":
        return <Sparkle {...iconProps} color="#EF4444" />;
      case "SpinWheel":
        return <CircleNotch {...iconProps} color="#F97316" />;
      case "UserDetails":
        return <User {...iconProps} color="#06B6D4" />;
      case "Discount":
        return <Receipt {...iconProps} color="#F59E0B" />;
      case "Document":
        return <Article {...iconProps} color="#10B981" />;
      case "Link":
        return <Cursor {...iconProps} color="#8B5CF6" />;
      case "FormSurvey":
        return <ChatText {...iconProps} color="#8B5CF6" />;
      default:
        return <Star {...iconProps} color="#F59E0B" />;
    }
  };

  const renderEditorContent = () => {
    switch (module.type) {
      case "SetupGuide": {
        const content = module.content as SetupGuideContent;
        return (
          <>
            <Typography variant="subtitle1" gutterBottom>
              Edit Setup Guide
            </Typography>
            {/* Title for SetupGuide is part of module.title, not module.content.title */}
            <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
              <TextField
                fullWidth
                label="New Step Description"
                variant="outlined"
                size="small"
                multiline
                rows={2}
                value={newStepDescription}
                onChange={(e) => setNewStepDescription(e.target.value)}
                sx={{ mr: 1 }}
              />
              <Button
                variant="contained"
                onClick={handleAddStep}
                startIcon={<AddIcon />}
                size="small"
                sx={{ whiteSpace: 'nowrap' }}
              >
                Add Step
              </Button>
            </Box>
            {(content.steps || []).length > 0 && (
              <List dense>
                {(content.steps || []).map((step) => (
                  <Paper
                    key={step.id}
                    elevation={1}
                    sx={{
                      p: 1.5,
                      mb: 1,
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                    }}
                  >
                    <ListItemText primary={step.description} />
                    <IconButton
                      edge="end"
                      aria-label="delete"
                      onClick={() => handleDeleteStep(step.id)}
                      size="small"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Paper>
                ))}
              </List>
            )}
            {renderButtonCustomizationFields(content)}
          </>
        );
      }
      case "Warranty": {
        const content = module.content as WarrantyContent;
        return (
          <>
            <Typography variant="subtitle1" gutterBottom>
              Edit Warranty Information
            </Typography>
            <TextField
              fullWidth
              label="Product Name"
              variant="outlined"
              size="small"
              value={content.productName || ""}
              onChange={(e) =>
                handleContentChange("productName", e.target.value)
              }
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              label="Product Model"
              variant="outlined"
              size="small"
              value={content.productModel || ""}
              onChange={(e) =>
                handleContentChange("productModel", e.target.value)
              }
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              label="Serial Number"
              variant="outlined"
              size="small"
              value={content.serialNumber || ""}
              onChange={(e) =>
                handleContentChange("serialNumber", e.target.value)
              }
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              label="Purchase Date"
              variant="outlined"
              size="small"
              type="date"
              value={content.purchaseDate || ""}
              InputLabelProps={{ shrink: true }}
              onChange={(e) =>
                handleContentChange("purchaseDate", e.target.value)
              }
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              label="Warranty Length"
              variant="outlined"
              size="small"
              value={content.warrantyLength || ""}
              onChange={(e) =>
                handleContentChange("warrantyLength", e.target.value)
              }
              sx={{ mb: 2 }}
              placeholder="e.g., 1 year, 24 months"
            />
            <TextField
              fullWidth
              label="Customer Name"
              variant="outlined"
              size="small"
              value={content.customerName || ""}
              onChange={(e) =>
                handleContentChange("customerName", e.target.value)
              }
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              label="Email"
              variant="outlined"
              size="small"
              type="email"
              value={content.email || ""}
              onChange={(e) => handleContentChange("email", e.target.value)}
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              label="Phone (Optional)"
              variant="outlined"
              size="small"
              type="tel"
              value={content.phone || ""}
              onChange={(e) => handleContentChange("phone", e.target.value)}
              sx={{ mb: 2 }}
            />
            <Box sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 2 }}>
              <Switch
                checked={content.isTermsAccepted || false}
                onChange={(checked) =>
                  handleContentChange("isTermsAccepted", checked)
                }
                size="medium"
                id="terms-accepted-switch"
              />
              <Typography variant="body2">Customer accepts terms & conditions</Typography>
            </Box>
            {/* No direct button for Warranty, but could be added if needed */}
          </>
        );
      }
      case "ShoppingLinks": {
        const content = module.content as ShoppingLinksContent;
        return (
          <>
            <Typography variant="subtitle1" gutterBottom>
              Edit Shopping Links
            </Typography>
            <TextField
              fullWidth
              label="Section Title (Optional)"
              variant="outlined"
              size="small"
              value={content.title || ""}
              onChange={(e) => handleContentChange("title", e.target.value)}
              sx={{ mb: 2 }}
            />
            <Box sx={{ my: 2 }}>
              <input
                type="file"
                accept=".pdf,.jpg,.jpeg,.png,.svg"
                onChange={handleShoppingLinkMediaChange}
                ref={shoppingMediaInputRef}
                style={{ display: "none" }}
                id={`shopping-media-upload-${module.id}`}
              />
              <Button
                variant="outlined"
                onClick={() => shoppingMediaInputRef.current?.click()}
                size="small"
                fullWidth
                startIcon={<UploadFileIcon />}
              >
                Upload Media (PDF, JPG, PNG, SVG)
              </Button>
              {content.mediaFileName && (
                <Box
                  sx={{
                    mt: 1,
                    p: 1,
                    border: "1px dashed #ccc",
                    borderRadius: "4px",
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    backgroundColor: "#f9f9f9",
                  }}
                >
                  <Typography
                    variant="body2"
                    sx={{ mr: 1, wordBreak: "break-all", flexGrow: 1 }}
                  >
                    {content.mediaFileName}
                  </Typography>
                  <IconButton
                    onClick={handleRemoveShoppingLinkMedia}
                    size="small"
                    aria-label="remove media"
                  >
                    <DeleteIcon fontSize="small" color="error" />
                  </IconButton>
                </Box>
              )}
            </Box>
            <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
              <TextField
                fullWidth
                label="New Link URL"
                variant="outlined"
                size="small"
                value={newLinkUrl}
                onChange={(e) => setNewLinkUrl(e.target.value)}
                sx={{ mr: 1 }}
              />
              <TextField
                fullWidth
                label="New Link Display Text"
                variant="outlined"
                size="small"
                value={newLinkDisplay}
                onChange={(e) => setNewLinkDisplay(e.target.value)}
                placeholder="e.g., Buy Here"
                sx={{ mr: 1, flexGrow: 1 }}
              />
              <Button
                variant="contained"
                onClick={handleAddShoppingLink}
                startIcon={<AddIcon />}
                size="small"
              >
                Add Link
              </Button>
            </Box>
            {(content.links || []).length > 0 && (
              <List dense>
                {(content.links || []).map((link) => (
                  <Paper
                    key={link.id}
                    elevation={1}
                    sx={{
                      p: 1.5,
                      mb: 1,
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                    }}
                  >
                    <ListItemText
                      primary={link.displayText || link.url}
                      secondary={link.displayText ? link.url : ""}
                    />
                    <IconButton
                      edge="end"
                      aria-label="delete"
                      onClick={() => handleDeleteShoppingLink(link.id)}
                      size="small"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Paper>
                ))}
              </List>
            )}
            {renderButtonCustomizationFields(content)}
          </>
        );
      }
      case "FeedbackSurvey": {
        // Now using CSAT module instead of legacy feedback survey
        const content = module.content as CSATModuleContent;
        return (
          <>
            <Typography variant="subtitle1" gutterBottom>
              Edit CSAT Feedback Module
            </Typography>
            <TextField
              fullWidth
              label="Survey Title"
              variant="outlined"
              size="small"
              value={content.title || ""}
              onChange={(e) => handleContentChange("title", e.target.value)}
              sx={{ mb: 2 }}
            />
            
            <Typography variant="subtitle2" gutterBottom sx={{ mt: 2, mb: 1 }}>
              Enable Feedback Types:
            </Typography>
            
            <Box sx={{ mb: 1, display: 'flex', alignItems: 'center', gap: 2 }}>
              <Switch
                checked={content.enableCSAT ?? true}
                onChange={(checked) => handleContentChange("enableCSAT", checked)}
                size="medium"
                id="enable-csat-switch"
              />
              <Typography variant="body2">Emoji Satisfaction Rating</Typography>
            </Box>
            
            <Box sx={{ mb: 1, display: 'flex', alignItems: 'center', gap: 2 }}>
              <Switch
                checked={content.enableNPS ?? true}
                onChange={(checked) => handleContentChange("enableNPS", checked)}
                size="medium"
                id="enable-nps-switch"
              />
              <Typography variant="body2">NPS Score (0-10)</Typography>
            </Box>
            
            <Box sx={{ mb: 1, display: 'flex', alignItems: 'center', gap: 2 }}>
              <Switch
                checked={content.enableStarRating ?? false}
                onChange={(checked) => handleContentChange("enableStarRating", checked)}
                size="medium"
                id="enable-star-rating-switch"
              />
              <Typography variant="body2">Star Rating (1-5)</Typography>
            </Box>
            
            <Box sx={{ mb: 1, display: 'flex', alignItems: 'center', gap: 2 }}>
              <Switch
                checked={content.enableFollowUp ?? false}
                onChange={(checked) => {
                  handleContentChange("enableFollowUp", checked);
                  if (checked && !content.followUpQuestion) {
                    handleContentChange("followUpQuestion", "Please tell us more about your experience:");
                  }
                }}
                size="medium"
                id="enable-follow-up-switch"
              />
              <Typography variant="body2">Open-ended Follow-up Question</Typography>
            </Box>
            
            {content.enableCSAT && (
              <TextField
                fullWidth
                label="CSAT Question"
                variant="outlined"
                size="small"
                value={content.csatQuestion || ""}
                onChange={(e) => handleContentChange("csatQuestion", e.target.value)}
                sx={{ mb: 2 }}
                placeholder="How satisfied are you with our service?"
              />
            )}
            
            {content.enableNPS && (
              <TextField
                fullWidth
                label="NPS Question"
                variant="outlined"
                size="small"
                value={content.npsQuestion || ""}
                onChange={(e) => handleContentChange("npsQuestion", e.target.value)}
                sx={{ mb: 2 }}
                placeholder="How likely are you to recommend us?"
              />
            )}
            
            {content.enableStarRating && (
              <TextField
                fullWidth
                label="Star Rating Question"
                variant="outlined"
                size="small"
                value={content.starRatingQuestion || ""}
                onChange={(e) => handleContentChange("starRatingQuestion", e.target.value)}
                sx={{ mb: 2 }}
                placeholder="Please rate your experience:"
              />
            )}
            
            {content.followUpQuestion && (
              <TextField
                fullWidth
                label="Follow-up Question"
                variant="outlined"
                size="small"
                value={content.followUpQuestion || ""}
                onChange={(e) => handleContentChange("followUpQuestion", e.target.value)}
                sx={{ mb: 2 }}
                placeholder="Please tell us more about your experience:"
              />
            )}
            
            <TextField
              fullWidth
              label="Thank You Message"
              variant="outlined"
              size="small"
              value={content.thankYouMessage || ""}
              onChange={(e) => handleContentChange("thankYouMessage", e.target.value)}
              sx={{ mb: 2 }}
              placeholder="Thank you for your feedback!"
            />
            {renderButtonCustomizationFields(content)}
          </>
        );
      }
      case "CustomButton": {
        const content = module.content as CustomButtonContent;
        return (
          <>
            <Typography variant="subtitle1" gutterBottom>
              Edit Custom Button
            </Typography>
            <TextField
              fullWidth
              label="Button Text (Legacy)"
              variant="outlined"
              size="small"
              value={content.text || ""}
              onChange={(e) => handleContentChange("text", e.target.value)}
              sx={{ mb: 2 }}
              helperText="Use Button Customization below for new buttons"
            />
            <TextField
              fullWidth
              label="Button URL"
              variant="outlined"
              size="small"
              value={content.url || ""}
              onChange={(e) => handleContentChange("url", e.target.value)}
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              label="Button Text Color (Legacy)"
              variant="outlined"
              size="small"
              value={content.textColor || ""}
              onChange={(e) => handleContentChange("textColor", e.target.value)}
              sx={{ mb: 2 }}
              placeholder="e.g., #FFFFFF or white"
            />
            <TextField
              fullWidth
              label="Button Background Color (Legacy)"
              variant="outlined"
              size="small"
              value={content.buttonColor || ""}
              onChange={(e) =>
                handleContentChange("buttonColor", e.target.value)
              }
              sx={{ mb: 2 }}
              placeholder="e.g., #000000 or black"
            />
            <TextField
              fullWidth
              label="Button Border Color (Legacy)"
              variant="outlined"
              size="small"
              value={content.borderColor || ""}
              onChange={(e) =>
                handleContentChange("borderColor", e.target.value)
              }
              sx={{ mb: 2 }}
              placeholder="e.g., #000000 or black"
            />
            {/* The URL is a primary field for CustomButton, not part of general customization, so it's kept separate. */}
            {/* Legacy fields above are for older data. New button styles are handled by renderButtonCustomizationFields. */}
            {renderButtonCustomizationFields(content)}
          </>
        );
      }

      case "Warranty": {
        const content = module.content as WarrantyContent;
        return (
          <>
            <TextField
              fullWidth
              label="Product Name"
              variant="outlined"
              size="small"
              value={content.productName || ""}
              onChange={(e) =>
                onUpdateContent(module.id, {
                  ...content,
                  productName: e.target.value,
                })
              }
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              label="Product Model (Optional)"
              variant="outlined"
              size="small"
              value={content.productModel || ""}
              onChange={(e) =>
                onUpdateContent(module.id, {
                  ...content,
                  productModel: e.target.value,
                })
              }
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              label="Serial Number"
              variant="outlined"
              size="small"
              value={content.serialNumber || ""}
              onChange={(e) =>
                onUpdateContent(module.id, {
                  ...content,
                  serialNumber: e.target.value,
                })
              }
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              label="Purchase Date"
              variant="outlined"
              size="small"
              type="date"
              value={content.purchaseDate || ""}
              InputLabelProps={{ shrink: true }}
              onChange={(e) =>
                onUpdateContent(module.id, {
                  ...content,
                  purchaseDate: e.target.value,
                })
              }
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              label="Warranty Length (e.g., 1 year, 24 months)"
              variant="outlined"
              size="small"
              value={content.warrantyLength || ""}
              onChange={(e) =>
                onUpdateContent(module.id, {
                  ...content,
                  warrantyLength: e.target.value,
                })
              }
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              label="Customer Name"
              variant="outlined"
              size="small"
              value={content.customerName || ""}
              onChange={(e) =>
                onUpdateContent(module.id, {
                  ...content,
                  customerName: e.target.value,
                })
              }
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              label="Email"
              variant="outlined"
              size="small"
              type="email"
              value={content.email || ""}
              onChange={(e) =>
                onUpdateContent(module.id, {
                  ...content,
                  email: e.target.value,
                })
              }
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              label="Phone (Optional)"
              variant="outlined"
              size="small"
              type="tel"
              value={content.phone || ""}
              onChange={(e) =>
                onUpdateContent(module.id, {
                  ...content,
                  phone: e.target.value,
                })
              }
              sx={{ mb: 2 }}
            />
            <Box sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 2 }}>
              <Switch
                checked={content.isTermsAccepted || false}
                onChange={(checked) =>
                  onUpdateContent(module.id, {
                    ...content,
                    isTermsAccepted: checked,
                  })
                }
                size="medium"
                id="warranty-terms-switch"
              />
              <Typography variant="body2">Customer accepts terms & conditions</Typography>
            </Box>
          </>
        );
      }
      case "Registration": {
        const content = module.content as RegistrationModuleContent;
        return (
          <>
            <Box sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 2 }}>
              <Switch
                checked={content.collectPurchaseDetails || false}
                onChange={(checked) => {
                  onUpdateContent(module.id, {
                    ...content,
                    collectPurchaseDetails: checked,
                  });
                }}
                size="medium"
                id="collect-purchase-details-switch"
              />
              <Typography variant="body2">Collect Purchase Details</Typography>
            </Box>
            <TextField
              fullWidth
              label="Require Pre-Survey ID (Optional)"
              variant="outlined"
              size="small"
              value={content.requirePreSurveyId || ""}
              onChange={(e) =>
                onUpdateContent(module.id, {
                  ...content,
                  requirePreSurveyId: e.target.value || undefined,
                })
              }
              sx={{ mb: 2 }}
            />
            {renderButtonCustomizationFields(content)}
          </>
        );
      }
      case "FormSurvey": {
        const content = module.content as FormSurveyModuleContent;
        return (
          <>
            <TextField
              fullWidth
              label="Survey Title"
              variant="outlined"
              size="small"
              value={content.title || ""}
              onChange={(e) =>
                onUpdateContent(module.id, {
                  ...content,
                  title: e.target.value,
                })
              }
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              label="Survey Definition URL (Optional)"
              variant="outlined"
              size="small"
              value={content.surveyDefinitionUrl || ""}
              onChange={(e) =>
                onUpdateContent(module.id, {
                  ...content,
                  surveyDefinitionUrl: e.target.value,
                })
              }
              sx={{ mb: 2 }}
              placeholder="Link to external survey or definition"
            />
            {renderButtonCustomizationFields(content)}
          </>
        );
      }
      case "Discount": {
        const content = module.content as DiscountModuleContent;
        return (
          <>
            <TextField
              fullWidth
              label="Shopify Discount Code"
              variant="outlined"
              size="small"
              value={content.shopifyDiscountCode || ""}
              onChange={(e) =>
                onUpdateContent(module.id, {
                  ...content,
                  shopifyDiscountCode: e.target.value,
                })
              }
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              label="Destination Page URL"
              variant="outlined"
              size="small"
              value={content.destinationPageUrl || ""}
              onChange={(e) =>
                onUpdateContent(module.id, {
                  ...content,
                  destinationPageUrl: e.target.value,
                })
              }
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              label="Product Info (Optional)"
              variant="outlined"
              size="small"
              multiline
              rows={2}
              value={content.productInfo || ""}
              onChange={(e) =>
                onUpdateContent(module.id, {
                  ...content,
                  productInfo: e.target.value,
                })
              }
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              label="Discount Terms (Optional)"
              variant="outlined"
              size="small"
              multiline
              rows={2}
              value={content.discountTerms || ""}
              onChange={(e) =>
                onUpdateContent(module.id, {
                  ...content,
                  discountTerms: e.target.value,
                })
              }
              sx={{ mb: 2 }}
            />
            {renderButtonCustomizationFields(content)}
          </>
        );
      }
      case "Document": {
        const content = module.content as DocumentModuleContent;
        return (
          <>
            <TextField
              fullWidth
              label="Document Name"
              variant="outlined"
              size="small"
              value={content.documentName || ""}
              onChange={(e) =>
                onUpdateContent(module.id, {
                  ...content,
                  documentName: e.target.value,
                })
              }
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              label="Document URL"
              variant="outlined"
              size="small"
              value={content.documentUrl || ""}
              onChange={(e) =>
                onUpdateContent(module.id, {
                  ...content,
                  documentUrl: e.target.value,
                })
              }
              sx={{ mb: 2 }}
            />
            {renderButtonCustomizationFields(content)}
          </>
        );
      }
      case "Link": {
        const content = module.content as LinkModuleContent;
        return (
          <>
            <TextField
              fullWidth
              label="Destination URL"
              variant="outlined"
              size="small"
              value={content.destinationUrl || ""}
              onChange={(e) =>
                onUpdateContent(module.id, {
                  ...content,
                  destinationUrl: e.target.value,
                })
              }
              sx={{ mb: 2 }}
            />
            {/* Link text is part of button customization now */}
            {renderButtonCustomizationFields(content)}
          </>
        );
      }
      case "ScratchCard": {
        const content = module.content as ScratchCardModuleContent;
        // Now use the handleScratchCardContentChange defined at the top level
        return (
          <>
            <ScratchCardEditor
              initialContent={content as Partial<ScratchCardModuleContent>}
              onContentChange={handleScratchCardContentChange}
            />
            {renderButtonCustomizationFields(content)}
          </>
        );
      }
      case "SpinWheel": {
        const content = module.content as SpinWheelModuleContent;
        return (
          <>
            <SpinWheelEditor
              initialContent={content}
              onContentChange={(newContent) => onUpdateContent(module.id, newContent)}
            />
            {renderButtonCustomizationFields(content)}
          </>
        );
      }
      case "UserDetails": {
        const content = module.content as UserDetailsContent;

        const handleFieldSettingChange = (
          fieldName: keyof UserDetailsContent["fieldSettings"],
          property: keyof UserDetailsFieldSetting,
          value: string | boolean,
        ) => {
          const newFieldSettings = {
            ...content.fieldSettings,
            [fieldName]: {
              ...content.fieldSettings[fieldName],
              [property]: value,
            },
          };
          onUpdateContent(module.id, {
            ...content,
            fieldSettings: newFieldSettings,
          });
        };

        return (
          <>
            <Typography variant="subtitle1" gutterBottom sx={{ mb: 2 }}>
              Edit User Details Form Fields
            </Typography>

            {(
              Object.keys(content.fieldSettings) as Array<
                keyof UserDetailsContent["fieldSettings"]
              >
            ).map((key) => {
              const setting = content.fieldSettings[key];
              // Ensure setting is not undefined, though with current structure it shouldn't be
              if (!setting) return null;

              const fieldLabel = key.charAt(0).toUpperCase() + key.slice(1);

              return (
                <Box
                  key={key}
                  sx={{
                    mb: 2,
                    p: 2,
                    border: "1px solid #e0e0e0",
                    borderRadius: "4px",
                    backgroundColor: "#f9f9f9",
                  }}
                >
                  <Box sx={{ display: "flex", alignItems: "center", gap: 1, mb: setting.visible ? 1 : 0 }}>
                    <Switch
                      checked={setting.visible}
                      onChange={(checked) =>
                        handleFieldSettingChange(
                          key,
                          "visible",
                          checked,
                        )
                      }
                      size="small"
                      id={`field-${key}-visible-switch`}
                    />
                    <Typography variant="body2">{`Show ${fieldLabel} Field`}</Typography>
                  </Box>
                  {setting.visible && (
                    <TextField
                      fullWidth
                      label={`${fieldLabel} Field Label`}
                      variant="outlined"
                      size="small"
                      value={setting.label}
                      onChange={(e) =>
                        handleFieldSettingChange(key, "label", e.target.value)
                      }
                      helperText={`Label for the '${key}' input field in the form.`}
                    />
                  )}
                </Box>
              );
            })}

            <Typography variant="subtitle2" gutterBottom sx={{ mt: 3, mb: 1 }}>
              Button Customization
            </Typography>
            {renderButtonCustomizationFields(content)}
          </>
        );
      }
      default:
        return (
          <Typography>No editor available for this module type.</Typography>
        );
    }
  };

  return (
    <ModuleContainer ref={ref} style={{ opacity: isDragging ? 0.5 : 1 }}>
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          cursor: "move",
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center", gap: 1.5 }}>
          <Box sx={{ display: "flex", alignItems: "center" }}>
            {getModuleIcon(module.type)}
          </Box>
          <Typography variant="h6" sx={{ fontWeight: 500, fontSize: "16px" }}>
            {module.title}
          </Typography>
        </Box>
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          <Button
            onClick={() => setIsEditing(!isEditing)}
            size="small"
            sx={{
              textTransform: "none",
              fontWeight: 500,
              color: "#6366f1",
              "&:hover": { backgroundColor: "#f1f5f9" },
            }}
          >
            {isEditing ? "Done" : "Edit"}
          </Button>
          {onRemoveModule && (
            <IconButton onClick={() => onRemoveModule(module.id)} size="small">
              <DeleteIcon />
            </IconButton>
          )}
        </Box>
      </Box>
      {isEditing && (
        <Box
          sx={{
            mt: 2,
            pt: 2,
            borderTop: "1px solid #e5e7eb",
            backgroundColor: "#fafbfc",
            borderRadius: "8px",
            p: 2,
            border: "1px solid #f1f5f9",
          }}
        >
          {renderEditorContent()}
        </Box>
      )}
    </ModuleContainer>
  );
});

export default ModuleItem;
