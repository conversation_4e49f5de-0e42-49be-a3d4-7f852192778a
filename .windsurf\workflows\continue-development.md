---
description: This workflow tells the Agent to continue the development process after the user confirms the Task List. The Agent now no longer needs to confirm with a prompt after each task on the list is completed.
---

# Agent Workflow: Auto-Continue After Task List Confirmation

## id: agent.auto_continue_post_tasklist
## description: >
  This workflow begins after the user confirms the task list. The Agent will now continue executing all tasks sequentially, without asking for confirmation after each step. The Agent only pauses if it lacks required info or encounters an error.

## tags: [agent, automation, workflow, cascade, no-confirm, windsurf]
## version: 1.0.0

---

## when:
- event: tasklist.confirmed
- condition: agent.context.task_list_confirmed == true

---

## context:
set:
  agent.context.auto_continue: true
  agent.context.current_task_index: 0

---

## then:

### 🔁 Loop Through Tasks
- loop:
    for: agent.context.task_list
    as: task
    index-as: i

    steps:

    - log: 🛠 Starting Task {{i + 1}}: {{task.title}}

    - try:
        run: agent.execute(task)

      catch:
        - log: ❌ Task {{task.title}} failed: {{error.message}}

        - if: agent.can_continue_without(task)
          then:
            - log: ⚠️ Continuing despite failure of Task {{i + 1}}

        - else:
            - log: ⛔ Stopping. Manual input required.
            - trigger: agent.pause(reason="manual_input_required")

    - log: ✅ Completed Task {{i + 1}}: {{task.title}}

---

## finally:

- log: 🎉 All tasks completed!
- run: agent.summarize_results()
- suggest:
    - text: "Would you like to deploy the results?"
    - options:
        - "Yes, deploy"
        - "No, review changes"
        - "Add more tasks"
