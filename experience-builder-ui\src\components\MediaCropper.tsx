import React, { useState, useCallback, useRef } from 'react';
import <PERSON><PERSON>per from 'react-easy-crop';
import { Box, Dialog, DialogTitle, DialogContent, DialogActions, Button, Slider } from '@mui/material';

type Point = { x: number; y: number };
type PixelCrop = { x: number; y: number; width: number; height: number };

export interface MediaCropperProps {
  open: boolean;
  onClose: () => void;
  mediaUrl: string;
  mediaType: 'image' | 'video';
  onCropComplete: (croppedAreaPixels: PixelCrop) => void;
  aspect?: number;
  initialCrop?: PixelCrop;
}

const MediaCropper: React.FC<MediaCropperProps> = ({
  open,
  onClose,
  mediaUrl,
  mediaType,
  onCropComplete,
  aspect = 9 / 16,
  initialCrop,
}) => {
  const [crop, setCrop] = useState<Point>(initialCrop ? { x: initialCrop.x, y: initialCrop.y } : { x: 0, y: 0 });
  const [zoom, setZoom] = useState<number>(1);
  const [rotation, setRotation] = useState<number>(0);
  const videoRef = useRef<HTMLVideoElement>(null);
  const [croppedAreaPixels, setCroppedAreaPixels] = useState<PixelCrop | null>(null);

  const handleInnerCropComplete = useCallback(
    (_croppedArea: Point & { width: number; height: number }, pixels: PixelCrop) => {
      setCroppedAreaPixels(pixels);
    },
    []
  );

  const handleApply = useCallback(() => {
    if (mediaType === 'image' && croppedAreaPixels) {
      onCropComplete(croppedAreaPixels);
    }
    onClose();
  }, [mediaType, croppedAreaPixels, onCropComplete, onClose]);

  const handleCropChange = useCallback((location: Point) => {
    setCrop(location);
  }, []);

  const handleZoomChange = (_event: Event, value: number | number[]) => {
    if (typeof value === 'number') {
      setZoom(value);
    }
  };

  const handleRotationChange = (_event: Event, value: number | number[]) => {
    if (typeof value === 'number') {
      setRotation(value);
    }
  };



  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>Crop {mediaType}</DialogTitle>
      <DialogContent>
        <Box sx={{ position: 'relative', width: '100%', height: 400 }}>
          {mediaType === 'image' ? (
            <Cropper
              image={mediaUrl}
              crop={crop}
              zoom={zoom}
              rotation={rotation}
              aspect={aspect}
              onCropChange={handleCropChange}
              onCropComplete={handleInnerCropComplete}
              initialCroppedAreaPixels={initialCrop}
              onZoomChange={setZoom}
              onRotationChange={setRotation}
            />
          ) : (
            <Box sx={{ position: 'relative', width: '100%', height: '100%' }}>
              <video
                ref={videoRef}
                src={mediaUrl}
                style={{
                  display: 'block',
                  width: '100%',
                  height: '100%',
                  objectFit: 'contain',
                }}
                controls
              />
            </Box>
          )}
        </Box>
        
        <Box sx={{ mt: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, p: 2 }}>
            <span>Zoom</span>
            <Slider
              value={zoom}
              min={1}
              max={3}
              step={0.1}
              aria-labelledby="Zoom"
              onChange={handleZoomChange}
              sx={{ flex: 1 }}
            />
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, p: 2 }}>
            <span>Rotation</span>
            <Slider
              value={rotation}
              min={0}
              max={360}
              step={1}
              aria-labelledby="Rotation"
              onChange={handleRotationChange}
              sx={{ flex: 1 }}
            />
          </Box>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button onClick={handleApply} variant="contained" color="primary">
          Apply
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default MediaCropper;
