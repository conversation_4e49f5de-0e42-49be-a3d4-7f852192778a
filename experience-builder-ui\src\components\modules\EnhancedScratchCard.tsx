import React, { useRef, useEffect, useState, useCallback } from 'react';
import { Box, Typography, Button, Paper } from '@mui/material';
import { styled, keyframes } from '@mui/material/styles';
import type { ScratchAreaData } from '../phone/Screen';

// Metallic shine animation
const shine = keyframes`
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: 200px 0;
  }
`;

// Prize tier animations
const goldGlow = keyframes`
  0%, 100% {
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.8);
  }
  50% {
    box-shadow: 0 0 40px rgba(255, 215, 0, 1);
  }
`;

const silverGlow = keyframes`
  0%, 100% {
    box-shadow: 0 0 20px rgba(192, 192, 192, 0.8);
  }
  50% {
    box-shadow: 0 0 40px rgba(192, 192, 192, 1);
  }
`;

const bronzeGlow = keyframes`
  0%, 100% {
    box-shadow: 0 0 20px rgba(205, 127, 50, 0.8);
  }
  50% {
    box-shadow: 0 0 40px rgba(205, 127, 50, 1);
  }
`;

const ScratchContainer = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  margin: theme.spacing(2, 0),
  borderRadius: theme.spacing(2),
  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  color: 'white',
  textAlign: 'center',
}));

const ScratchCanvas = styled('canvas')<{ 
  revealed?: boolean; 
  metallicEffect?: 'gold' | 'silver' | 'bronze' | 'none';
}>(({ theme, revealed, metallicEffect }) => ({
  border: '3px solid #fff',
  borderRadius: theme.spacing(2),
  cursor: 'pointer',
  transition: 'all 0.3s ease',
  ...(revealed && metallicEffect === 'gold' && {
    background: 'linear-gradient(45deg, #FFD700, #FFA500, #FFD700)',
    backgroundSize: '200px 100%',
    animation: `${shine} 2s infinite linear, ${goldGlow} 2s infinite ease-in-out`,
  }),
  ...(revealed && metallicEffect === 'silver' && {
    background: 'linear-gradient(45deg, #C0C0C0, #A8A8A8, #C0C0C0)',
    backgroundSize: '200px 100%',
    animation: `${shine} 2s infinite linear, ${silverGlow} 2s infinite ease-in-out`,
  }),
  ...(revealed && metallicEffect === 'bronze' && {
    background: 'linear-gradient(45deg, #CD7F32, #B8860B, #CD7F32)',
    backgroundSize: '200px 100%',
    animation: `${shine} 2s infinite linear, ${bronzeGlow} 2s infinite ease-in-out`,
  }),
}));

const PrizeReveal = styled(Box)<{ 
  metallicEffect?: 'gold' | 'silver' | 'bronze' | 'none';
}>(({ theme, metallicEffect }) => ({
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  textAlign: 'center',
  pointerEvents: 'none',
  zIndex: 1,
  padding: theme.spacing(2),
  borderRadius: theme.spacing(1),
  ...(metallicEffect === 'gold' && {
    background: 'linear-gradient(45deg, rgba(255, 215, 0, 0.9), rgba(255, 165, 0, 0.9))',
    color: '#000',
    fontWeight: 'bold',
    textShadow: '1px 1px 2px rgba(255, 255, 255, 0.8)',
  }),
  ...(metallicEffect === 'silver' && {
    background: 'linear-gradient(45deg, rgba(192, 192, 192, 0.9), rgba(169, 169, 169, 0.9))',
    color: '#000',
    fontWeight: 'bold',
    textShadow: '1px 1px 2px rgba(255, 255, 255, 0.8)',
  }),
  ...(metallicEffect === 'bronze' && {
    background: 'linear-gradient(45deg, rgba(205, 127, 50, 0.9), rgba(184, 134, 11, 0.9))',
    color: '#fff',
    fontWeight: 'bold',
    textShadow: '1px 1px 2px rgba(0, 0, 0, 0.8)',
  }),
}));

const PrizeTierBadge = styled(Box)<{ 
  prizeTier?: 1 | 2 | 3;
}>(({ prizeTier }) => ({
  position: 'absolute',
  top: -10,
  right: -10,
  width: 40,
  height: 40,
  borderRadius: '50%',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  fontSize: '14px',
  fontWeight: 'bold',
  zIndex: 2,
  ...(prizeTier === 1 && {
    background: 'linear-gradient(45deg, #FFD700, #FFA500)',
    color: '#000',
    border: '2px solid #FFD700',
  }),
  ...(prizeTier === 2 && {
    background: 'linear-gradient(45deg, #C0C0C0, #A8A8A8)',
    color: '#000',
    border: '2px solid #C0C0C0',
  }),
  ...(prizeTier === 3 && {
    background: 'linear-gradient(45deg, #CD7F32, #B8860B)',
    color: '#fff',
    border: '2px solid #CD7F32',
  }),
}));

interface EnhancedScratchCardProps {
  scratchArea: ScratchAreaData;
  onScratchComplete?: (scratchArea: ScratchAreaData) => void;
  onScratchProgress?: (progress: number) => void;
}

const EnhancedScratchCard: React.FC<EnhancedScratchCardProps> = ({
  scratchArea,
  onScratchComplete,
  onScratchProgress,
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isScratching, setIsScratching] = useState(false);
  const [scratchProgress, setScratchProgress] = useState(scratchArea.scratchProgress || 0);
  const [isRevealed, setIsRevealed] = useState(scratchArea.isRevealed || false);

  const initCanvas = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    canvas.width = 300;
    canvas.height = 200;

    // Create scratch surface with metallic effect based on prize tier
    const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
    
    if (scratchArea.metallicEffect === 'gold') {
      gradient.addColorStop(0, '#FFD700');
      gradient.addColorStop(0.5, '#FFA500');
      gradient.addColorStop(1, '#FFD700');
    } else if (scratchArea.metallicEffect === 'silver') {
      gradient.addColorStop(0, '#C0C0C0');
      gradient.addColorStop(0.5, '#A8A8A8');
      gradient.addColorStop(1, '#C0C0C0');
    } else if (scratchArea.metallicEffect === 'bronze') {
      gradient.addColorStop(0, '#CD7F32');
      gradient.addColorStop(0.5, '#B8860B');
      gradient.addColorStop(1, '#CD7F32');
    } else {
      gradient.addColorStop(0, '#888');
      gradient.addColorStop(0.5, '#666');
      gradient.addColorStop(1, '#888');
    }

    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Add scratch text
    ctx.fillStyle = '#fff';
    ctx.font = 'bold 24px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(scratchArea.scratchText || 'Scratch Here!', canvas.width / 2, canvas.height / 2);

    // Set composite operation for scratching
    ctx.globalCompositeOperation = 'destination-out';
  }, [scratchArea]);

  useEffect(() => {
    if (!isRevealed) {
      initCanvas();
    }
  }, [initCanvas, isRevealed]);

  const scratch = useCallback((x: number, y: number) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const rect = canvas.getBoundingClientRect();
    const scaleX = canvas.width / rect.width;
    const scaleY = canvas.height / rect.height;

    const canvasX = (x - rect.left) * scaleX;
    const canvasY = (y - rect.top) * scaleY;

    ctx.beginPath();
    ctx.arc(canvasX, canvasY, 20, 0, 2 * Math.PI);
    ctx.fill();

    // Calculate scratch progress
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const pixels = imageData.data;
    let transparentPixels = 0;

    for (let i = 3; i < pixels.length; i += 4) {
      if (pixels[i] === 0) {
        transparentPixels++;
      }
    }

    const progress = (transparentPixels / (canvas.width * canvas.height)) * 100;
    setScratchProgress(progress);
    onScratchProgress?.(progress);

    // Auto-reveal when 50% scratched
    if (progress > 50 && !isRevealed) {
      setIsRevealed(true);
      onScratchComplete?.(scratchArea);
    }
  }, [scratchArea, isRevealed, onScratchComplete, onScratchProgress]);

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsScratching(true);
    scratch(e.clientX, e.clientY);
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (isScratching) {
      scratch(e.clientX, e.clientY);
    }
  };

  const handleMouseUp = () => {
    setIsScratching(false);
  };

  const handleTouchStart = (e: React.TouchEvent) => {
    e.preventDefault();
    setIsScratching(true);
    const touch = e.touches[0];
    scratch(touch.clientX, touch.clientY);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    e.preventDefault();
    if (isScratching) {
      const touch = e.touches[0];
      scratch(touch.clientX, touch.clientY);
    }
  };

  const handleTouchEnd = (e: React.TouchEvent) => {
    e.preventDefault();
    setIsScratching(false);
  };

  const getPrizeTierText = () => {
    if (scratchArea.prizeTier === 1) return '🥇 1ST';
    if (scratchArea.prizeTier === 2) return '🥈 2ND';
    if (scratchArea.prizeTier === 3) return '🥉 3RD';
    return '';
  };

  return (
    <ScratchContainer>
      <Box position="relative" display="inline-block">
        {scratchArea.prizeTier && (
          <PrizeTierBadge prizeTier={scratchArea.prizeTier}>
            {getPrizeTierText()}
          </PrizeTierBadge>
        )}
        
        <ScratchCanvas
          ref={canvasRef}
          revealed={isRevealed}
          metallicEffect={scratchArea.metallicEffect}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
          style={{
            display: isRevealed ? 'none' : 'block',
          }}
        />

        {isRevealed && (
          <PrizeReveal metallicEffect={scratchArea.metallicEffect}>
            <Typography variant="h4" component="div" gutterBottom>
              {scratchArea.revealedTextLine1}
            </Typography>
            <Typography variant="h6" component="div">
              {scratchArea.revealedTextLine2}
            </Typography>
            {scratchArea.prizeRank && (
              <Typography variant="subtitle1" component="div" sx={{ mt: 1, fontWeight: 'bold' }}>
                {scratchArea.prizeRank}
              </Typography>
            )}
          </PrizeReveal>
        )}
      </Box>

      {isRevealed && scratchArea.rewardType === 'prize' && (
        <Box mt={2}>
          <Button
            variant="contained"
            size="large"
            sx={{
              backgroundColor: 'rgba(255, 255, 255, 0.2)',
              color: 'white',
              '&:hover': {
                backgroundColor: 'rgba(255, 255, 255, 0.3)',
              },
            }}
          >
            Claim Your Prize!
          </Button>
        </Box>
      )}

      <Box mt={2}>
        <Typography variant="body2" sx={{ opacity: 0.8 }}>
          Scratch Progress: {Math.round(scratchProgress)}%
        </Typography>
      </Box>
    </ScratchContainer>
  );
};

export default EnhancedScratchCard;
