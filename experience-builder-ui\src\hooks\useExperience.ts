import { useState, useEffect } from 'react';
import { getExperience, supabase } from '../utils/supabaseClient'; // Adjusted path
import type { Experience, ExperienceResponse } from '../types/experience.types'; // Adjusted path

export const useExperience = (experienceId: string | undefined): ExperienceResponse => {
  const [data, setData] = useState<Experience | null>(null);
  const [error, setError] = useState<Error | null>(null);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    if (!experienceId) {
      console.log('No experience ID provided');
      setLoading(false);
      return;
    }

    const fetchExperience = async () => {
      try {
        console.log('Fetching experience with ID:', experienceId);
        setLoading(true);
        const experience = await getExperience(experienceId);
        console.log('Experience data received:', experience);
        // Don't cast to Experience interface to preserve all database fields including header config
        setData(experience as any);
        setError(null);
      } catch (err) {
        console.error('Error fetching experience:', err);
        setError(err instanceof Error ? err : new Error('Failed to load experience'));
      } finally {
        setLoading(false);
      }
    };

    fetchExperience();
    fetchExperience();

    // Set up Supabase Realtime subscription for experiences table
    const experienceChannel = supabase
      .channel(`experience_hook_${experienceId}`) // Unique channel name
      .on(
        'postgres_changes',
        { 
          event: 'UPDATE', 
          schema: 'public', 
          table: 'experiences', 
          filter: `experience_id=eq.${experienceId}` 
        },
        (payload) => {
          console.log('Realtime update in useExperience hook for experiences:', payload);
          fetchExperience(); // Re-fetch data on update
        }
      )
      .subscribe();

    // Set up Supabase Realtime subscription for modules table
    const modulesChannel = supabase
      .channel(`modules_hook_${experienceId}`)
      .on(
        'postgres_changes',
        {
          event: '*', // Listen for INSERT, UPDATE, DELETE
          schema: 'public',
          table: 'modules',
          filter: `experience_id=eq.${experienceId}`
        },
        (payload) => {
          console.log('Realtime update in useExperience hook for modules:', payload);
          fetchExperience(); // Re-fetch all data to get latest modules
        }
      )
      .subscribe();

    // Set up Supabase Realtime subscription for screens table
    const screensChannel = supabase
      .channel(`screens_hook_${experienceId}`)
      .on(
        'postgres_changes',
        {
          event: '*', // Listen for INSERT, UPDATE, DELETE
          schema: 'public',
          table: 'screens',
          filter: `experience_id=eq.${experienceId}`
        },
        (payload) => {
          console.log('Realtime update in useExperience hook for screens:', payload);
          fetchExperience(); // Re-fetch all data to get latest screens
        }
      )
      .subscribe();

    // Cleanup function
    return () => {
      supabase.removeChannel(experienceChannel);
      supabase.removeChannel(modulesChannel);
      supabase.removeChannel(screensChannel);
    };

  }, [experienceId]);

  return { data, error, loading };
};

export default useExperience;
