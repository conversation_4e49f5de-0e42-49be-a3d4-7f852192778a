import React, { useState, useCallback } from 'react';
import { useParams } from 'react-router-dom';
import { Box, Typography, CircularProgress } from '@mui/material'; // MUI components for loading/error
import { useExperience } from '../hooks/useExperience';
import { Screen, type Module as ScreenModule, type MediaItem } from '../components/phone/Screen';
import { resolveMediaUrl } from '../utils/media';

interface ApiModuleData {
  module_id: string;
  experience_id?: string;
  type: string;
  title: string;
  display_order: number;
  content?: string | Record<string, any>;
  config?: string | Record<string, any>;
  screen_index?: number;
}

const MobileExperiencePage: React.FC = () => {
  const { experienceId } = useParams<{ experienceId: string }>();
  const { data: experience, loading, error } = useExperience(experienceId);

  // console.log('[MobileExperiencePage] Hook data:', { experienceId, experience, loading, error });

  const [currentScreenIdx, setCurrentScreenIdx] = useState(0);

  const totalScreensForCallback = (experience as any)?.screens?.length > 0 ? (experience as any).screens.length : 1;

  const handleScreenChange = useCallback((idx: number) => {
    if (idx >= 0 && idx < totalScreensForCallback) {
      setCurrentScreenIdx(idx);
    }
  }, [totalScreensForCallback, setCurrentScreenIdx]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', height: '100%' }}>
          <CircularProgress />
          <Typography sx={{ mt: 2 }} className="text-gray-600">Loading Experience...</Typography>
        </Box>
      </div>
    );
  }

  if (error || !experience) {
    console.error('Error loading experience:', error);
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Box sx={{ textAlign: 'center', padding: 2 }} className="p-6 max-w-md mx-auto mt-10 bg-red-50 rounded-lg shadow">
          <Typography variant="h5" color="error" className="text-xl font-bold text-red-700 mb-2">
            Failed to load experience
          </Typography>
          <Typography className="text-red-600">
            We couldn't load the requested experience. Please try again later.
          </Typography>
          {error && (
            <Box sx={{ mt: 2, p: 1, backgroundColor: 'rgba(255,0,0,0.1)', borderRadius: 1}} className="mt-4 p-3 bg-red-100 rounded text-sm">
              <Typography variant="caption" display="block" className="font-semibold">Error details:</Typography>
              <Typography variant="caption" className="font-mono text-xs mt-1 overflow-x-auto">{error.message}</Typography>
            </Box>
          )}
        </Box>
      </div>
    );
  }

  const screens: any[] = (experience as any).screens ?? [];
  const totalScreens = screens.length || 1;

  const mapMedia = (screenData: any): MediaItem | null => {
    const storagePath = screenData?.media?.path ?? null;
    console.log('🔍 DEBUG mapMedia - screenData:', screenData);
    console.log('🔍 DEBUG mapMedia - storagePath:', storagePath);
    
    if (!storagePath) {
      console.log('❌ DEBUG mapMedia - No storage path found');
      return null;
    }
    
    const resolvedUrl = resolveMediaUrl(storagePath);
    console.log('🔍 DEBUG mapMedia - resolvedUrl:', resolvedUrl);
    
    return {
      id: screenData.screen_id,
      url: resolvedUrl,
      type: screenData.media.mime_type?.startsWith('video/') ? 'video' : 'image',
    } as MediaItem;
  };

  // Debug the current screen and background media resolution
  console.log('🔍 DEBUG - screens array:', screens);
  console.log('🔍 DEBUG - currentScreenIdx:', currentScreenIdx);
  console.log('🔍 DEBUG - screens[currentScreenIdx]:', screens[currentScreenIdx]);
  console.log('🔍 DEBUG - experience.background_image:', experience.background_image);
  
  const backgroundMedia = mapMedia(screens[currentScreenIdx]) ?? (experience.background_image ? {
    id: 'bg',
    url: resolveMediaUrl(experience.background_image) ?? experience.background_image,
    type: 'image' as const,
  } : null);
  
  console.log('🔍 DEBUG - Final backgroundMedia:', backgroundMedia);

  // console.log('[MobileExperiencePage] RAW experience.modules:', (experience as any).modules);
  console.log('[MobileExperiencePage] RAW experience.modules from hook:', (experience as any).modules);
  const modules: ScreenModule[] = ((experience as any).modules || [])
    .sort((a: ApiModuleData, b: ApiModuleData) => a.display_order - b.display_order)
    .map((apiModule: ApiModuleData) => { // Explicitly type apiModule here
      const rawContent = apiModule.content ?? apiModule.config;
      const content = typeof rawContent === 'string' ? JSON.parse(rawContent) : rawContent ?? {};

      return {
        id: apiModule.module_id,
        title: apiModule.title,
        type: apiModule.type, // ScreenModule['type'] includes UserDetails and others
        content: content,
        screen: apiModule.screen_index ?? 0
      };
    });

  // console.log('[MobileExperiencePage] Transformed backgroundMedia:', backgroundMedia);
  // console.log('[MobileExperiencePage] Transformed modules:', modules);

// Add a style element to the document head for mobile styling with more aggressive overrides
  React.useEffect(() => {
    // COMPREHENSIVE Mobile viewport height fix - multiple strategies
    const setVH = () => {
      const vh = window.innerHeight * 0.01;
      const svh = window.innerHeight * 0.01; // Small viewport height
      const dvh = window.innerHeight * 0.01; // Dynamic viewport height
      
      // Set multiple CSS custom properties for maximum compatibility
      document.documentElement.style.setProperty('--vh', `${vh}px`);
      document.documentElement.style.setProperty('--svh', `${svh}px`);
      document.documentElement.style.setProperty('--dvh', `${dvh}px`);
      document.documentElement.style.setProperty('--safe-area-inset-bottom', '0px');
      
      // Force immediate recalculation
      document.documentElement.style.setProperty('--actual-height', `${window.innerHeight}px`);
    };
    
    // Set initial value immediately
    setVH();
    
    // Add multiple event listeners for comprehensive coverage
    window.addEventListener('resize', setVH);
    window.addEventListener('orientationchange', setVH);
    window.addEventListener('scroll', setVH);
    window.addEventListener('touchstart', setVH);
    window.addEventListener('touchend', setVH);
    
    // Force recalculation after a short delay (for mobile browser UI changes)
    setTimeout(setVH, 100);
    setTimeout(setVH, 500);
    setTimeout(setVH, 1000);
    
    // Create a style element
    const styleElement = document.createElement('style');
    
    // Add aggressive CSS overrides for mobile experience that EXACTLY match the Screen component
    styleElement.textContent = `
      /* Force no scrolling on the page itself */
      html, body, #root {
        height: 100% !important;
        width: 100% !important;
        margin: 0 !important;
        padding: 0 !important;
        overflow: hidden !important;
      }
      
      /* Match the modal structure exactly from Screen component but OVERRIDE the 50vh limitation */
      .absolute.inset-x-0.bottom-0.bg-white.rounded-t-\[16px\].shadow-lg.p-6.flex.flex-col.z-10 {
        /* Override the 50vh limitation with !important */
        max-height: 75vh !important; /* Override the calc(50vh) limitation */
        min-height: 400px !important;
        width: 100% !important;
        position: fixed !important;
        bottom: 0 !important;
        left: 0 !important;
        right: 0 !important;
        display: flex !important;
        flex-direction: column !important;
        z-index: 50 !important;
        overflow: visible !important;
      }
      
      /* Ensure the drag handle is visible */
      .w-12.h-1.bg-gray-300.rounded-full.mx-auto.mb-4.flex-shrink-0 {
        display: block !important;
      }
      
      /* Ensure the header stays at the top */
      .flex.justify-between.items-start.mb-4.flex-shrink-0 {
        display: flex !important;
        justify-content: space-between !important;
        margin-bottom: 16px !important;
      }
      
      /* Make sure the scrollable container for buttons works correctly */
      .flex-grow.flex-shrink.min-h-0.overflow-y-auto.space-y-3.scrollbar-hide {
        flex-grow: 1 !important;
        flex-shrink: 1 !important;
        min-height: 0 !important;
        overflow-y: auto !important;
        display: flex !important;
        flex-direction: column !important;
        gap: 12px !important;
      }
      
      /* When a button is clicked, expand the modal */
      .absolute.inset-x-0.bottom-0.bg-white.rounded-t-\[16px\].shadow-lg.p-6.flex.flex-col.z-10.expanded,
      .absolute.inset-x-0.bottom-0.bg-white.rounded-t-\[16px\].shadow-lg.p-6.flex.flex-col.z-10:has(button:active),
      .absolute.inset-x-0.bottom-0.bg-white.rounded-t-\[16px\].shadow-lg.p-6.flex.flex-col.z-10:has(a:active) {
        max-height: 500px !important;
        min-height: 500px !important;
      }
      
      /* Limit background image height and ensure it doesn't push modal content down */
      .absolute.inset-0.z-0 {
        height: 25vh !important;
        max-height: 25vh !important;
        overflow: hidden !important;
        position: absolute !important;
        top: 0 !important;
        width: 100% !important;
        z-index: 1 !important;
      }
      
      .absolute.inset-0.z-0 img,
      .absolute.inset-0.z-0 video {
        height: 100% !important;
        object-fit: cover !important;
        object-position: center !important;
        width: 100% !important;
      }
      
      /* COMPREHENSIVE Mobile viewport fix - multiple strategies for guaranteed bottom content visibility */
      .w-screen.h-screen.overflow-hidden {
        display: flex !important;
        flex-direction: column !important;
        justify-content: flex-end !important;
        
        /* Multiple height strategies for maximum compatibility */
        height: 100vh !important;
        height: 100svh !important;
        height: 100dvh !important;
        height: calc(var(--vh, 1vh) * 100) !important;
        height: calc(var(--actual-height, 100vh)) !important;
        
        /* Aggressive positioning to ensure full viewport coverage */
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        width: 100vw !important;
        width: 100% !important;
        
        /* Prevent any scrolling or overflow */
        overflow: hidden !important;
        overscroll-behavior: none !important;
        
        /* Mobile-specific fixes */
        -webkit-overflow-scrolling: touch !important;
        touch-action: manipulation !important;
      }
      
      /* Allow proper touch interaction for input fields */
       .w-screen.h-screen.overflow-hidden input,
       .w-screen.h-screen.overflow-hidden textarea,
       .w-screen.h-screen.overflow-hidden select {
         touch-action: auto !important;
         pointer-events: auto !important;
         -webkit-user-select: text !important;
         user-select: text !important;
         -webkit-appearance: none !important;
         appearance: none !important;
         background-color: white !important;
         color: #000000 !important;
         border: 1px solid #d1d5db !important;
         border-radius: 8px !important;
         padding: 12px !important;
         font-size: 16px !important;
         line-height: 1.5 !important;
         width: 100% !important;
         box-sizing: border-box !important;
         outline: none !important;
         -webkit-text-fill-color: #000000 !important;
       }
       
       /* Focus states for input fields */
       .w-screen.h-screen.overflow-hidden input:focus,
       .w-screen.h-screen.overflow-hidden textarea:focus,
       .w-screen.h-screen.overflow-hidden select:focus {
         border-color: #3b82f6 !important;
         box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
       }
        
        /* Ensure content fits within safe areas */
        padding-bottom: env(safe-area-inset-bottom, 0px) !important;
        padding-bottom: var(--safe-area-inset-bottom, 0px) !important;
        
        /* Force hardware acceleration for better performance */
        transform: translateZ(0) !important;
        will-change: transform !important;
      }
      
      /* Make sure all content inside the modal is scrollable */
      .absolute.inset-x-0.bottom-0.bg-white.rounded-t-\[16px\].shadow-lg.p-6.flex.flex-col.z-10 > div:not(.flex-shrink-0) {
        overflow-y: auto !important;
        flex: 1 !important;
      }
      
      /* Make sure the modal header stays at the top */
      .absolute.inset-x-0.bottom-0.bg-white.rounded-t-\[16px\].shadow-lg.p-6.flex.flex-col.z-10 > div.flex.justify-between.items-start.mb-4.flex-shrink-0 {
        flex-shrink: 0 !important;
      }
      
      /* Make the drag handle visible */
      .w-12.h-1.bg-gray-300.rounded-full.mx-auto.mb-4.flex-shrink-0 {
        display: block !important;
        margin-top: -10px !important;
        margin-bottom: 15px !important;
        /* Increase text size for better readability */
        .absolute.inset-x-0.bottom-0.bg-white h2 {
          font-size: 1.5rem !important;
          font-weight: 600 !important;
          margin-bottom: 0.5rem !important;
        }
        
        .absolute.inset-x-0.bottom-0.bg-white p {
          font-size: 1.125rem !important;
          line-height: 1.5 !important;
          margin-bottom: 0.5rem !important;
        }
        
        /* Make buttons larger and more prominent */
        .absolute.inset-x-0.bottom-0.bg-white button,
        .absolute.inset-x-0.bottom-0.bg-white a.block {
          font-size: 1.25rem !important;
          font-weight: 600 !important;
          padding: 16px !important;
          margin-bottom: 16px !important;
          border-radius: 9999px !important; /* Full rounded */
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
          transition: all 0.2s ease-in-out !important;
        }
        
        /* Enhance button hover/active states */
        .absolute.inset-x-0.bottom-0.bg-white button:hover,
        .absolute.inset-x-0.bottom-0.bg-white a.block:hover,
        .absolute.inset-x-0.bottom-0.bg-white button:active,
        .absolute.inset-x-0.bottom-0.bg-white a.block:active {
          transform: translateY(-2px) !important;
          box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
        }
        
        /* Limit background image height and ensure it doesn't push modal content down */
        .absolute.inset-0.z-0 img,
        .absolute.inset-0.z-0 video {
          max-height: 25vh !important;
          object-fit: cover !important;
          position: absolute !important;
          top: 0 !important;
          width: 100% !important;
          z-index: 1 !important;
        }
        
        /* Mobile viewport fix using industry best practices */
        .w-screen.h-screen.overflow-hidden {
          display: flex !important;
          flex-direction: column !important;
          justify-content: flex-end !important;
          height: 100vh !important;
          height: 100svh !important;
          height: calc(var(--vh, 1vh) * 100) !important;
          position: fixed !important;
          top: 0 !important;
          left: 0 !important;
          right: 0 !important;
          bottom: 0 !important;
        }
      }
    `;
    document.head.appendChild(styleElement);
    
    // Force a reflow to ensure styles are applied
    document.body.offsetHeight;
    
    return () => {
      document.head.removeChild(styleElement);
      window.removeEventListener('resize', setVH);
      window.removeEventListener('orientationchange', setVH);
      window.removeEventListener('scroll', setVH);
      window.removeEventListener('touchstart', setVH);
      window.removeEventListener('touchend', setVH);
    };
  }, []);

  // Use header config from database, with robust fallbacks
  console.log('DEBUG: Full experience object:', experience);
  console.log('DEBUG: app_name from experience:', (experience as any)?.app_name);
  console.log('DEBUG: app_subtitle from experience:', (experience as any)?.app_subtitle);
  console.log('DEBUG: get_started_button_text from experience:', (experience as any)?.get_started_button_text);
  
  const headerConfig = {
    appName: (experience as any)?.app_name || (experience as any)?.name || 'Experience',
    appSubtitle: (experience as any)?.app_subtitle || 'Interactive Experience',
    getStartedButtonText: (experience as any)?.get_started_button_text || 'Get Started'
  };
  
  console.log('DEBUG: Final headerConfig with fallbacks:', headerConfig);

  return (
    <div className="w-screen overflow-hidden" style={{ height: '100svh' }}>
      <Screen
        backgroundMedia={backgroundMedia}
        modules={modules}
        currentScreen={currentScreenIdx}
        totalScreens={totalScreens}
        onScreenChange={handleScreenChange}
        isFullScreenView={true}
        headerConfig={headerConfig}
      />
    </div>
  );

};

export default MobileExperiencePage;