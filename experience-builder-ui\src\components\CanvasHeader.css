/* CanvasHeader.css */
.launch-button {
  background: linear-gradient(to bottom, #00d4ff 0%, #090979 79%, #020024 100%) !important;
  color: white !important;
  padding: 10px 20px !important;
  border-radius: 20px !important;
  border: none !important;
  cursor: pointer !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  font-size: 1rem !important;
  box-shadow: 0 4px 14px 0 rgba(0, 0, 0, 0.1) !important;
  transition: all 0.3s ease !important;
  font-family: inherit !important;
  line-height: 1.5 !important;
  margin: 0 !important;
  -webkit-appearance: button !important;
  appearance: button !important;
  text-transform: none !important;
}

.launch-button:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 20px 0 rgba(0, 0, 0, 0.15) !important;
}

.launch-button .material-icons {
  font-size: 1.2rem !important;
}
