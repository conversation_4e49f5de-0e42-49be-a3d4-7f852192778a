import React from 'react';
import { resolveMediaUrl } from '../utils/media';
import type { Module } from '../types/experience.types'; // Import global Module type
import ScratchCardModule from './modules/ScratchCardModule';
import SpinWheelModule from './modules/SpinWheelModule';

type ButtonVariant = 'primary' | 'secondary' | 'outline';

interface ModuleRendererProps {
  module: Module; // Use imported Module type
}

const ModuleRenderer: React.FC<ModuleRendererProps> = ({ module }) => {
  // Safely extract and normalize module data
  const m = module as any; // loose access for runtime fields
  // Support both builder-side (module_type, config) and runtime-side (type, content)
  const moduleType = (m.module_type ?? m.type ?? '').toString().toLowerCase();

  let moduleConfig: Record<string, any> = {};
  if (m.config && typeof m.config === 'object') {
    moduleConfig = module.config as Record<string, any>;
  } else if (typeof m.content === 'string') {
    try {
      moduleConfig = JSON.parse(m.content);
    } catch (err) {
      console.warn('Failed to parse module.content JSON', err);
    }
  }

    
  console.log('Rendering module:', module.module_id, 'Type:', moduleType, 'Config:', moduleConfig);
  
  // Add a default case for unknown module types
  if (!moduleType) {
    console.warn('Module has no type:', module);
    return (
      <div className="p-4 bg-yellow-50 border-l-4 border-yellow-400 mb-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <p className="text-sm text-yellow-700">
              Unknown module type: {module.module_type || 'undefined'}
              <div className="mt-2 text-xs text-yellow-600">
                Module ID: {module.module_id}
              </div>
            </p>
          </div>
        </div>
      </div>
    );
  }

  console.log('Rendering module:', module.module_id, 'Type:', moduleType, 'Config:', moduleConfig);

  const renderModule = () => {
    switch (moduleType) {
      case 'warranty':
        // Warranty module
        const warrantyTitle = moduleConfig.title || 'Warranty Registration';
        const warrantyDescription = moduleConfig.description || 'Register your product for warranty';
        const warrantyButtonText = moduleConfig.buttonText || 'Register Now';
        
        return (
          <div className="p-6 bg-white rounded-lg shadow-md">
            <div className="flex items-center mb-4">
              <div className="p-2 bg-blue-100 rounded-full mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-800">{warrantyTitle}</h3>
            </div>
            <p className="text-gray-600 mb-4">{warrantyDescription}</p>
            <button className="w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-md transition-colors">
              {warrantyButtonText}
            </button>
          </div>
        );
        
      case 'setupguide':
        // Setup Guide module
        const setupTitle = moduleConfig.title || 'Setup Guide';
        const setupSteps = Array.isArray(moduleConfig.steps) 
          ? moduleConfig.steps 
          : [
              'Unbox your product',
              'Charge the device',
              'Download the app',
              'Follow the in-app instructions'
            ];
            
        return (
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="bg-blue-600 p-4">
              <h3 className="text-white text-lg font-semibold">{setupTitle}</h3>
            </div>
            <ul className="divide-y divide-gray-200">
              {setupSteps.map((step: string, index: number) => (
                <li key={index} className="p-4 flex items-center">
                  <div className="bg-blue-100 text-blue-600 rounded-full h-6 w-6 flex items-center justify-center mr-3 font-semibold text-sm">
                    {index + 1}
                  </div>
                  <span className="text-gray-700">{step}</span>
                </li>
              ))}
            </ul>
          </div>
        );

      case 'faq':
        // FAQ module
        const faqTitle = moduleConfig.title || 'Frequently Asked Questions';
        const faqItems = Array.isArray(moduleConfig.items) 
          ? moduleConfig.items 
          : [
              {
                question: 'What is this product?',
                answer: 'This is a high-quality product designed to meet your needs.'
              },
              {
                question: 'How do I use it?',
                answer: 'Refer to the setup guide and user manual for instructions.'
              }
            ];

        return (
          <div className="bg-white rounded-lg shadow-md">
            <div className="bg-gray-100 p-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-800">{faqTitle}</h3>
            </div>
            <div className="divide-y divide-gray-200">
              {faqItems.map((item: { question: string; answer: string }, index: number) => (
                <details key={index} className="p-4 group">
                  <summary className="flex justify-between items-center cursor-pointer list-none">
                    <span className="font-medium text-gray-700 group-hover:text-blue-600">{item.question}</span>
                    <span className="text-gray-500 group-open:rotate-90 transform transition-transform duration-200">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </span>
                  </summary>
                  <p className="text-gray-600 mt-2 text-sm">{item.answer}</p>
                </details>
              ))}
            </div>
          </div>
        );

      case 'text':
        // Text module
        const textTitle = moduleConfig.title || '';
        const textContent = moduleConfig.content || '';
        
        return (
          <div className="p-4 bg-white rounded-lg shadow-md">
            <h3 className="text-lg font-semibold mb-2">{textTitle}</h3>
            <div className="prose max-w-none">
              {typeof textContent === 'string' ? (
                <p className="text-gray-700 whitespace-pre-line">{textContent}</p>
              ) : (
                <p className="text-gray-700">Invalid content format</p>
              )}
            </div>
          </div>
        );
        
      case 'image':
        // Handle image module with safe defaults
        const imageUrl = resolveMediaUrl(moduleConfig.imageUrl || moduleConfig.url || '');
        const altText = moduleConfig.altText || moduleConfig.caption || 'Module image';
        
        return (
          <div className="mb-4 overflow-hidden rounded-lg shadow">
            {imageUrl ? (
              <div className="relative">
                <img 
                  src={imageUrl} 
                  alt={altText} 
                  className="w-full h-auto max-h-96 object-cover"
                  onError={(e) => {
                    // Handle broken images
                    const target = e.target as HTMLImageElement;
                    target.onerror = null;
                    target.src = 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiYjMDA3O0I4Q0UmIzIyOyIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJsdWNpZGUgbHVjaWRlLWltYWdlLW9mZiI+PHBhdGggZD0iTTE5LjMgMTljLjQuNCAuNyAxIC43IDEuN3YxLjVhMiAyIDAgMCAxLTIgMkg1YTIgMiAwIDAgMS0yLTJWNWEyIDIgMCAwIDEgMi0yaDIiLz48cGF0aCBkPSJtMSAxMSAxLjYxLTEuNjFhMi4xIDIuMSAwIDAgMSAyLjkyIDIuOThMMTAgMTM0Ii8+PHBhdGggZD0ibTIgMTkgMy42NC0zLjY0Ii8+PHBhdGggZD0iTTE3IDguNjQgNS42NCAyMEg1di0xLjY0TDE3IDdoMS42NUwyMCA1LjY2VjhoLTMuNnoiLz48cGF0aCBkPSJtMTQgMTkgMTItMTIiLz48cGF0aCBkPSJNMjIgMmwtMTkuMiAxOS4yIi8+PC9zdmc+';
                  }}
                />
                {moduleConfig.caption && (
                  <p className="text-sm text-gray-500 text-center mt-1 px-2">
                    {moduleConfig.caption}
                  </p>
                )}
              </div>
            ) : (
              <div className="bg-gray-100 p-8 text-center text-gray-500 flex items-center justify-center h-48">
                <span>No image available</span>
              </div>
            )}
          </div>
        );
        
      case 'feedbacksurvey':
        const surveyTitle = moduleConfig.title || 'Feedback Survey';
        const questions: string[] = Array.isArray(moduleConfig.questions) ? moduleConfig.questions : [];
        return (
          <div className="p-6 bg-white rounded-lg shadow-md">
            <h3 className="text-lg font-semibold mb-4">{surveyTitle}</h3>
            {questions.length > 0 ? (
              <form className="space-y-4">
                {questions.map((q, idx) => (
                  <div key={idx}>
                    <label className="block text-sm font-medium text-gray-700 mb-1">{q}</label>
                    <input type="text" className="w-full px-3 py-2 border rounded-md" />
                  </div>
                ))}
                <button type="submit" className="w-full bg-blue-500 hover:bg-blue-600 text-white py-2 rounded-md">Submit</button>
              </form>
            ) : (
              <p className="text-gray-500">Survey questions not configured.</p>
            )}
          </div>
        );
      case 'registration':
        const regTitle = moduleConfig.title || 'Registration Form';
        const collectPurchase = !!moduleConfig.collectPurchaseDetails;
        return (
          <div className="p-6 bg-white rounded-lg shadow-md">
            <h3 className="text-lg font-semibold mb-4">{regTitle}</h3>
            <form className="space-y-4">
              <input placeholder="Name" className="w-full px-3 py-2 border rounded-md" />
              <input placeholder="Email" type="email" className="w-full px-3 py-2 border rounded-md" />
              {collectPurchase && (
                <input placeholder="Purchase Details" className="w-full px-3 py-2 border rounded-md" />
              )}
              <button type="submit" className="w-full bg-blue-500 hover:bg-blue-600 text-white py-2 rounded-md">Register</button>
            </form>
          </div>
        );
      case 'scratchcard':
        // Use the same ScratchCardModule as the iPhone preview
        return <ScratchCardModule {...(moduleConfig as any)} />;
        
      case 'spinwheel':
        // Use the same SpinWheelModule as the iPhone preview  
        return <SpinWheelModule />;

      case 'shoppinglinks':
        const links: Array<{ label: string; url: string }> = Array.isArray(moduleConfig.links) ? moduleConfig.links : [];
        return (
          <div className="p-4 bg-white rounded-lg shadow-md">
            <h3 className="text-lg font-semibold mb-4">{moduleConfig.title || 'Shop Now'}</h3>
            <ul className="space-y-2">
              {links.length > 0 ? (
                links.map((l, idx) => (
                  <li key={idx}>
                    <a href={l.url} target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">
                      {l.label}
                    </a>
                  </li>
                ))
              ) : (
                <li className="text-gray-500">No links configured.</li>
              )}
            </ul>
          </div>
        );
      case 'button':
        // Handle button module with safe defaults
        const buttonLabel = moduleConfig.label || 'Click Me';
        const buttonUrl = moduleConfig.url || '#';
        const buttonVariant: ButtonVariant = ['primary', 'secondary', 'outline'].includes(moduleConfig.variant) 
          ? moduleConfig.variant as ButtonVariant 
          : 'primary';
        
        const buttonClasses = {
          primary: 'bg-blue-500 hover:bg-blue-600 text-white',
          secondary: 'bg-gray-200 hover:bg-gray-300 text-gray-800',
          outline: 'border border-blue-500 text-blue-500 hover:bg-blue-50',
        }[buttonVariant];
        
        return (
          <div className="p-4 mb-4 text-center">
            <a 
              href={buttonUrl} 
              target="_blank" 
              rel="noopener noreferrer"
              className={`inline-block px-6 py-2 rounded-md transition-colors ${buttonClasses}`}
            >
              {buttonLabel}
            </a>
          </div>
        );
      default:
        return (
          <div className="p-4 mb-4 bg-yellow-50 border-l-4 border-yellow-400">
            <p className="text-yellow-700">
              Module type "{moduleType}" is not supported yet.
            </p>
            <pre className="mt-2 p-2 bg-white text-xs overflow-auto">
              {JSON.stringify(module, null, 2)}
            </pre>
          </div>
        );
    }
  };

  return (
    <div className="module-container">
      {renderModule()}
    </div>
  );
};

export default ModuleRenderer;
