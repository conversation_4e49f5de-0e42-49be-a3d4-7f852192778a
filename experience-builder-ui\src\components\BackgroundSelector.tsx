import { useState, useRef, useCallback, useEffect } from "react";
import getCroppedImg from '../utils/cropImage';
import type { Area as PixelCrop } from 'react-easy-crop';
import { uploadFile } from "../utils/media";
import {
  Box,
  Button,
  Typography,
  Paper,
  IconButton,
  styled,
  Tabs,
  Tab,
  TextField,
} from "@mui/material";
import DeleteIcon from "@mui/icons-material/Delete";
import EditIcon from "@mui/icons-material/Edit";
import AddPhotoAlternateIcon from "@mui/icons-material/AddPhotoAlternate";
import VideocamIcon from "@mui/icons-material/Videocam";
import MediaCropper from "./MediaCropper";

type MediaType = "image" | "video";

export interface MediaItem {
  id: string;
  url: string;
  type: MediaType;
  crop?: { x: number; y: number; width: number; height: number };
}

interface Screen {
  id: string;
  name: string;
  mediaUrl?: string | null;
}

export interface HeaderConfig {
  appName: string;
  appSubtitle: string;
  getStartedButtonText: string;
}

interface BackgroundSelectorProps {
  selectedMedia: MediaItem | null;
  onSelectMedia: (media: MediaItem | null) => void;
  onScreenChange: (index: number, total: number) => void;
  headerConfig?: HeaderConfig;
  onHeaderConfigChange?: (config: HeaderConfig) => void;
}

const GalleryContainer = styled(Box)(({ theme }) => ({
  display: "grid",
  gridTemplateColumns: "repeat(auto-fill, minmax(120px, 1fr))",
  gap: theme.spacing(2),
  marginTop: theme.spacing(2),
}));

const MediaThumbnail = styled(Box, {
  shouldForwardProp: (prop) => prop !== "isSelected",
})<{ isSelected: boolean }>(({ theme, isSelected }) => ({
  position: "relative",
  width: "120px",
  height: "120px",
  minWidth: "120px",
  minHeight: "120px",
  borderRadius: theme.shape.borderRadius,
  overflow: "hidden",
  cursor: "pointer",
  border: `2px solid ${isSelected ? theme.palette.primary.main : "transparent"}`,
  "&:hover": {
    borderColor: theme.palette.primary.main,
  },
  "& img, & video": {
    position: "absolute",
    top: 0,
    left: 0,
    width: "100%",
    height: "100%",
    objectFit: "contain",
  },
}));

const ScreenButton = styled(Button, {
  shouldForwardProp: (prop) => prop !== "isActive",
})<{ isActive?: boolean }>(({ theme, isActive }) => ({
  textTransform: "none",
  justifyContent: "flex-start",
  textAlign: "left",
  borderColor: isActive ? theme.palette.primary.main : "transparent",
  "&:hover": {
    borderColor: theme.palette.primary.main,
  },
}));

const loadGallery = (): MediaItem[] => {
  try {
    const saved = localStorage.getItem("mediaGallery");
    return saved ? JSON.parse(saved) : [];
  } catch (error) {
    console.error("Failed to load gallery", error);
    return [];
  }
};

const saveGallery = (gallery: MediaItem[]): void => {
  try {
    localStorage.setItem("mediaGallery", JSON.stringify(gallery));
  } catch (error) {
    console.error("Failed to save gallery", error);
  }
};

// Optimize media handling
// Remove these broken lines (around line 90-110):
// const BackgroundSelector: React.FC<BackgroundSelectorProps> = memo(({ 
// const debouncedFileUpload = useMemo(
// const galleryItems = useMemo(() => 

// Replace with:
const BackgroundSelector: React.FC<BackgroundSelectorProps> = ({ 
  selectedMedia, 
  onSelectMedia, 
  onScreenChange, 
  headerConfig, 
  onHeaderConfigChange 
}) => {
  const [gallery, setGallery] = useState<MediaItem[]>(loadGallery());
  const [isCropperOpen, setIsCropperOpen] = useState(false);
  const [cropTarget, setCropTarget] = useState<MediaItem | null>(null);

  const handleCropComplete = async (croppedAreaPixels: PixelCrop) => {
    if (!cropTarget) return;

    const newGallery = gallery.map((item) =>
      item.id === cropTarget.id ? { ...item, crop: croppedAreaPixels } : item,
    );
    setGallery(newGallery);

    // Also update the selected media if it's the one being cropped
    if (selectedMedia?.id === cropTarget.id) {
      const newMediaItem = { ...cropTarget, crop: croppedAreaPixels };
      onSelectMedia(newMediaItem);
      // Immediately update the screen with the new cropped image
      const croppedImageUrl = await getCroppedImg(newMediaItem.url, croppedAreaPixels);
      if (croppedImageUrl) {
        const updatedScreens = [...screens];
        if (updatedScreens[currentScreenIndex]) {
          updatedScreens[currentScreenIndex].mediaUrl = croppedImageUrl;
        }
        setScreens(updatedScreens);
      }
    }

    setIsCropperOpen(false);
  };

  const [screens, setScreens] = useState<Screen[]>([
    { id: "1", name: "Screen 1", mediaUrl: null },
  ]);
  const [currentScreenIndex, setCurrentScreenIndex] = useState(0);
  const [tabValue, setTabValue] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const currentScreen = screens[currentScreenIndex] || screens[0];

  // Save gallery to localStorage whenever it changes
  useEffect(() => {
    saveGallery(gallery);
  }, [gallery]);

  // Clean up object URLs only when component unmounts
  useEffect(() => {
    return () => {
      // Clean up all blob URLs to prevent memory leaks when the component is no longer in use
      const currentGallery = loadGallery(); // Load the latest gallery state directly
      currentGallery.forEach((item) => {
        if (item.url && item.url.startsWith("blob:")) {
          URL.revokeObjectURL(item.url);
        }
      });
    };
  }, []); // Empty dependency array means this runs only on mount and unmount

  const handleMediaSelect = useCallback(
    async (media: MediaItem | null) => {
      onSelectMedia(media);
      let finalMediaUrl: string | null = null;
      if (media) {
        if (media.crop && media.type === 'image') {
          finalMediaUrl = await getCroppedImg(media.url, media.crop);
        } else {
          finalMediaUrl = media.url;
        }
      }

      const updatedScreens = [...screens];
      if (updatedScreens[currentScreenIndex]) {
        updatedScreens[currentScreenIndex].mediaUrl = finalMediaUrl;
      }
      setScreens(updatedScreens);
    },
    [onSelectMedia, currentScreenIndex, screens],
  );

  // Custom notification system for better user feedback
  const showNotification = useCallback((message: string, isError = false) => {
    // Remove any existing notifications
    const existing = document.querySelectorAll(".media-upload-notification");
    existing.forEach((el) => el.remove());

    const notification = document.createElement("div");
    notification.className = "media-upload-notification";
    notification.style.position = "fixed";
    notification.style.top = "20px";
    notification.style.left = "50%";
    notification.style.transform = "translateX(-50%)";
    notification.style.padding = "12px 24px";
    notification.style.background = isError ? "#f44336" : "#4CAF50";
    notification.style.color = "white";
    notification.style.borderRadius = "4px";
    notification.style.zIndex = "9999";
    notification.style.boxShadow = "0 2px 10px rgba(0,0,0,0.2)";
    notification.style.transition = "opacity 0.3s";
    notification.style.display = "flex";
    notification.style.alignItems = "center";
    notification.style.gap = "10px";
    notification.textContent = message;

    // Add icon based on message type
    const icon = document.createElement("span");
    icon.textContent = isError ? "⚠️" : "✓";
    notification.prepend(icon);

    document.body.appendChild(notification);

    // Auto-remove after delay
    setTimeout(
      () => {
        notification.style.opacity = "0";
        setTimeout(() => notification.remove(), 300);
      },
      isError ? 5000 : 3000,
    );
  }, []);

  const handleFileUpload = useCallback(
    async (file: File) => {
      // NO LIMITS - As requested by user.
      try {
        showNotification("Uploading media…");
        let uploadResponse;
        try {
          uploadResponse = await uploadFile(file);
        } catch (err) {
          console.error("Upload failed:", err);
          showNotification("Upload failed", true);
          return;
        }
        // Success
        // const fileUrl = URL.createObjectURL(file);
        const newMedia: MediaItem = {
          id: uploadResponse.mediaId,
          url: uploadResponse.url,
          type: file.type.startsWith("video/") ? "video" : "image",
        };
        setGallery((prev) => [...prev, newMedia]);
        showNotification("Media uploaded");
        handleMediaSelect(newMedia);
      } catch (error) {
        console.error("Error processing file:", error);
        showNotification(
          "Error processing file. Please try another file.",
          true,
        );
      }
    },
    [handleMediaSelect, showNotification],
  );

  const handleDeleteMedia = useCallback(
    (mediaId: string, e: React.MouseEvent) => {
      e.stopPropagation();
      const itemToDelete = gallery.find((item) => item.id === mediaId);
      if (itemToDelete && itemToDelete.url.startsWith("blob:")) {
        URL.revokeObjectURL(itemToDelete.url);
      }
      setGallery((prev) => prev.filter((item) => item.id !== mediaId));
      if (selectedMedia?.id === mediaId) {
        onSelectMedia(null);
      }
    },
    [gallery, selectedMedia, onSelectMedia],
  );

  const handleAddScreen = useCallback(() => {
    const newScreen: Screen = {
      id: Date.now().toString(),
      name: `Screen ${screens.length + 1}`,
      mediaUrl: null,
    };
    setScreens((prev) => [...prev, newScreen]);
    setCurrentScreenIndex(screens.length);
  }, [screens.length]);

  const handleDeleteScreen = useCallback(
    (index: number, e: React.MouseEvent) => {
      e.stopPropagation();
      if (screens.length <= 1) return;
      setScreens((prev) => prev.filter((_, i) => i !== index));
      setCurrentScreenIndex((prev) =>
        index === prev ? 0 : prev > index ? prev - 1 : prev,
      );
    },
    [screens.length],
  );

  const handleScreenSelect = useCallback(
    (index: number) => {
      setCurrentScreenIndex(index);
      const screenMediaUrl = screens[index]?.mediaUrl;
      const media = gallery.find((g) => g.url === screenMediaUrl) || null;
      onSelectMedia(media);
    },
    [screens, gallery, onSelectMedia],
  );

  useEffect(() => {
    onScreenChange(currentScreenIndex, screens.length);
  }, [currentScreenIndex, screens.length, onScreenChange]);

  const handleTabChange = useCallback(
    (_: React.SyntheticEvent, newValue: number) => {
      setTabValue(newValue);
    },
    [],
  );

  const handleFileInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const file = e.target.files?.[0];
      if (file) {
        handleFileUpload(file);
      }
      // Reset input to allow re-uploading the same file
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    },
    [handleFileUpload],
  );

  const renderGallery = (isScreenContext: boolean) => (
    <GalleryContainer>
      {gallery.map((media) => {
        const isSelected = isScreenContext
          ? currentScreen.mediaUrl === media.url
          : selectedMedia?.url === media.url;
        return (
          <Box key={media.id} sx={{ display: "flex", flexDirection: "column", justifyContent: "flex-start", alignItems: "center" }}>
            <MediaThumbnail
              isSelected={isSelected}
              onClick={() => handleMediaSelect(media)}
            >
              {media.type === "image" ? (
                <img src={media.url} alt="Media thumbnail" />
              ) : (
                <video src={media.url} autoPlay loop muted playsInline />
              )}
            </MediaThumbnail>
            <Box
              sx={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                mt: 1,
                gap: 2,
              }}
            >
              <IconButton
                size="small"
                onClick={() => {
                  setCropTarget(media);
                  setIsCropperOpen(true);
                }}
                aria-label={`edit ${media.url}`}
              >
                <EditIcon sx={{ color: "primary.main" }} />
              </IconButton>
              <IconButton
                size="small"
                onClick={(e) => handleDeleteMedia(media.id, e)}
                aria-label={`delete ${media.url}`}
              >
                <DeleteIcon sx={{ color: "error.main" }} />
              </IconButton>
            </Box>
          </Box>
        );
      })}
    </GalleryContainer>
  );

  const handleHeaderConfigChange = (field: keyof HeaderConfig, value: string) => {
    if (headerConfig && onHeaderConfigChange) {
      onHeaderConfigChange({
        ...headerConfig,
        [field]: value,
      });
    }
  };

  return (
    <Paper elevation={0} sx={{ p: 3, borderRadius: 1, overflow: "hidden" }}>
      <Tabs value={tabValue} onChange={handleTabChange} sx={{ mb: 2 }}>
        <Tab label="Media Library" />
        <Tab label="Screens" />
        <Tab label="Header Settings" />
      </Tabs>

      {tabValue === 0 ? (
        <>
          <Box sx={{ mb: 2 }}>
            <input
              type="file"
              ref={fileInputRef}
              onChange={handleFileInputChange}
              accept="image/*,video/*"
              style={{ display: "none" }}
            />
            <Button
              variant="outlined"
              startIcon={<AddPhotoAlternateIcon />}
              onClick={() => fileInputRef.current?.click()}
              sx={{ mr: 2, borderRadius: 1, overflow: "hidden" }}
            >
              Add Media
            </Button>
            <Button
              variant="outlined"
              startIcon={<VideocamIcon />}
              onClick={() => fileInputRef.current?.click()}
              sx={{ borderRadius: 1, overflow: "hidden" }}
            >
              Add Video
            </Button>
          </Box>
          <Typography variant="subtitle2" gutterBottom>
            Gallery
          </Typography>
          {renderGallery(false)}
        </>
      ) : tabValue === 1 ? (
        <Box>
          <Box sx={{ display: "flex", gap: 1, mb: 2 }}>
            {screens.map((screen, index) => (
              <ScreenButton
                key={screen.id}
                variant="outlined"
                isActive={index === currentScreenIndex}
                onClick={() => handleScreenSelect(index)}
                endIcon={
                  screens.length > 1 ? (
                    <IconButton
                      size="small"
                      onClick={(e) => handleDeleteScreen(index, e)}
                    >
                      <DeleteIcon fontSize="small" />
                    </IconButton>
                  ) : null
                }
              >
                {screen.name}
              </ScreenButton>
            ))}
            <Button variant="outlined" onClick={handleAddScreen}>
              + Add Screen
            </Button>
          </Box>
          <Typography variant="subtitle2" gutterBottom>
            Screen {currentScreenIndex + 1} Media
          </Typography>
          {renderGallery(true)}
        </Box>
      ) : (
        <Box>
          <Typography variant="subtitle2" gutterBottom>
            Header Configuration
          </Typography>
          <Box sx={{ display: "flex", flexDirection: "column", gap: 2, mt: 2 }}>
            <TextField
              label="Experience Heading"
              value={headerConfig?.appName || ""}
              onChange={(e) => handleHeaderConfigChange("appName", e.target.value)}
              variant="outlined"
              size="small"
              fullWidth
              placeholder="Enter experience heading"
            />
            <TextField
              label="Experience Header Subtext"
              value={headerConfig?.appSubtitle || ""}
              onChange={(e) => handleHeaderConfigChange("appSubtitle", e.target.value)}
              variant="outlined"
              size="small"
              fullWidth
              placeholder="Enter experience header subtext"
            />
            
            <TextField
              label="Experience Heading 2"
              value={headerConfig?.getStartedButtonText || ""}
              onChange={(e) => handleHeaderConfigChange("getStartedButtonText", e.target.value)}
              variant="outlined"
              size="small"
              fullWidth
              placeholder="Enter experience heading 2"
            />
          </Box>
        </Box>
      )}

      {cropTarget && (
        <MediaCropper
          open={isCropperOpen}
          mediaUrl={cropTarget.url}
          mediaType={cropTarget.type}
          onClose={() => setIsCropperOpen(false)}
          onCropComplete={handleCropComplete}
        />
      )}
    </Paper>
  );
};

export default BackgroundSelector;
