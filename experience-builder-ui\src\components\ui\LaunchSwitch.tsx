import React, { useState } from 'react';

interface LaunchSwitchProps {
  onLaunch: () => Promise<boolean> | boolean;
}

const LaunchSwitch: React.FC<LaunchSwitchProps> = ({ onLaunch }) => {
  const [isLaunched, setIsLaunched] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleClick = async () => {
    if (isLaunched || isLoading) return;
    
    setIsLoading(true);
    
    try {
      const success = await onLaunch();
      if (success) {
        setIsLaunched(true);
      }
    } catch (error) {
      console.error('Launch failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <style>{`
        .button-64 {
          align-items: center;
          background-image: linear-gradient(144deg,#AF40FF, #5B42F3 50%,#00DDEB);
          border: 0;
          border-radius: 6px;
          box-shadow: rgba(151, 65, 252, 0.2) 0 8px 16px -3px;
          box-sizing: border-box;
          color: #FFFFFF;
          display: flex;
          font-family: Phantomsans, sans-serif;
          font-size: 14px;
          justify-content: center;
          line-height: 1em;
          max-width: 100%;
          min-width: 100px;
          padding: 2px;
          text-decoration: none;
          user-select: none;
          -webkit-user-select: none;
          touch-action: manipulation;
          white-space: nowrap;
          cursor: pointer;
        }
        
        .button-64:active,
        .button-64:hover {
          outline: 0;
        }
        
        .button-64 span {
          background-color: rgb(5, 6, 45);
          padding: 10px 16px;
          border-radius: 4px;
          width: 100%;
          height: 100%;
          transition: 300ms;
        }
        
        .button-64:hover span {
          background: none;
        }
        
        .button-64:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }
        
        .button-64.launched {
          background-image: linear-gradient(144deg, #10b981, #059669 50%, #047857);
        }
        
        @media (min-width: 768px) {
           .button-64 {
             font-size: 16px;
             min-width: 120px;
           }
         }
      `}</style>
      <button 
        className={`button-64 ${isLaunched ? 'launched' : ''}`}
        onClick={handleClick} 
        disabled={isLoading}
        role="button"
      >
        <span className="text">
          {isLoading ? 'Launching...' : isLaunched ? 'Launched ✓' : 'Launch'}
        </span>
      </button>
    </>
  );
};



export default LaunchSwitch;