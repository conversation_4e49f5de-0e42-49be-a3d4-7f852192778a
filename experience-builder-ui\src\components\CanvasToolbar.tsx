import React from 'react';
import { Box, IconButton, Tooltip } from '@mui/material';
import { styled } from '@mui/material/styles';

const ToolButton = styled(IconButton)({
  color: '#1f2937', // text-gray-900 for icons
  '&:hover': {
    backgroundColor: 'transparent', // No background on hover
  },
});

interface CanvasToolbarProps {
  onToolSelect: (tool: string) => void;
}

const CanvasToolbar: React.FC<CanvasToolbarProps> = ({ onToolSelect }) => {
  const tools = [
    { icon: 'refresh', label: 'Refresh' },
    { icon: 'palette', label: 'Color' },
    { icon: 'text_fields', label: 'Text' },
    { icon: 'draw', label: 'Draw' },
    { icon: 'content_copy', label: 'Copy' },
    { icon: 'link', label: 'Link' },
    { icon: 'image', label: 'Image' },
    { icon: 'delete', label: 'Delete' },
  ];

  return (
    <Box sx={{
      display: 'flex',
      gap: 0.5,
      backgroundColor: 'white',
      borderRadius: '8px',
      padding: '4px',
      boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
      border: '1px solid #e5e7eb', // border-gray-200
    }}>
      {tools.map((tool) => (
        <Tooltip key={tool.icon} title={tool.label}>
          <ToolButton onClick={() => onToolSelect(tool.icon)}>
            <span className="material-icons">{tool.icon}</span>
          </ToolButton>
        </Tooltip>
      ))}
    </Box>
  );
};

export default CanvasToolbar;
