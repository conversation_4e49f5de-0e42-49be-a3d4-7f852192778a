# Global ignores for Web-app-experience monorepo
# Ignore all node build artefacts and IDE noise

# Node.js dependencies
node_modules/

# Build outputs
build/
dist/
dist-ssr/
*.log

# Package lock files (larger, auto-generated)
package-lock.json
yarn.lock
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# IDE & Editor folders
.vscode/
.idea/

# OS files
.DS_Store
Thumbs.db

# Environment files
.env*
!experience-builder-ui/.env
!mobile-runtime/.env

# Misc
*.zip
*.svg

# Exclude large mock resources added earlier
experience-builder-ui/public/iphone16mock-main.zip
experience-builder-ui/public/figma iphone resource

# Environment files
*.local

# OS & Editor files
.DS_Store
.vscode/
!.vscode/extensions.json
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
>>>>>>> kieron_dev
