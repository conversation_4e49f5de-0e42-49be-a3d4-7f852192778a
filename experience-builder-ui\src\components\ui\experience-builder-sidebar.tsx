"use client";
import React, { useState } from "react";
import { Sidebar, SidebarBody, useSidebar } from "@/components/ui/sidebar";
import {
  IconSettings,
  IconBuildingStore,
  IconQrcode,
} from "@tabler/icons-react";
import { motion } from "motion/react";
import { cn } from "@/lib/utils";
import {
  Paper,
  Typography,
  ToggleButtonGroup,
  ToggleButton,
} from "@mui/material";
import BuildExperience from "../BuildExperience";
import QrCodeGenerator from "../QrCodeGenerator";


interface ExperienceBuilderSidebarProps {
  experienceType: "Registration" | "Rebates" | "Sweepstakes";
  onExperienceTypeChange: (event: React.MouseEvent<HTMLElement>, newType: "Registration" | "Rebates" | "Sweepstakes" | null) => void;
  addModule: (moduleType: string, title: string) => void;
  experienceId: string | null;
  experienceName: string;
  children: React.ReactNode;
}

export default function ExperienceBuilderSidebar({
  experienceType,
  onExperienceTypeChange,
  addModule,
  experienceId,
  experienceName,
  children,
}: ExperienceBuilderSidebarProps) {
  const [open, setOpen] = useState(false);
  const [activeSection, setActiveSection] = useState<"experience" | "build" | "qr">("build");

  const links = [
    {
      label: "Experience Type",
      href: "#",
      icon: <IconSettings className="h-6 w-6 shrink-0" />,
      onClick: () => setActiveSection("experience"),
    },
    {
      label: "Build Experience",
      href: "#",
      icon: <IconBuildingStore className="h-6 w-6 shrink-0" />,
      onClick: () => setActiveSection("build"),
    },
    {
      label: "QR Code",
      href: "#",
      icon: <IconQrcode className="h-6 w-6 shrink-0" />,
      onClick: () => setActiveSection("qr"),
    },
  ];

  const renderSidebarContent = () => {
    switch (activeSection) {
      case "experience":
        return (
          <Paper
            elevation={1}
            sx={{ p: 2, borderRadius: "8px", border: "1px solid #f1f5f9", mt: 2 }}
          >
            <Typography
              variant="h6"
              sx={{ fontWeight: 600, mb: 2, fontSize: "16px" }}
            >
              Experience Type
            </Typography>
            <ToggleButtonGroup
              exclusive
              value={experienceType}
              onChange={onExperienceTypeChange}
              orientation="vertical"
              sx={{
                width: "100%",
                "& .MuiToggleButton-root": {
                  "&:first-of-type": {
                    borderTopLeftRadius: "8px",
                    borderTopRightRadius: "8px",
                  },
                  "&:last-of-type": {
                    borderBottomLeftRadius: "8px",
                    borderBottomRightRadius: "8px",
                  },
                },
              }}
            >
              <ToggleButton value="Registration">Registration</ToggleButton>
              <ToggleButton value="Rebates">Rebates</ToggleButton>
              <ToggleButton value="Sweepstakes">Sweepstakes</ToggleButton>
            </ToggleButtonGroup>
          </Paper>
        );
      case "build":
        return (
          <div className="mt-4">
            <BuildExperience
              addModule={addModule}
              experienceType={experienceType}
            />
          </div>
        );
      case "qr":
        return (
          <Paper
            elevation={1}
            sx={{ p: 2, borderRadius: "8px", border: "1px solid #f1f5f9", mt: 2 }}
          >
            <Typography
              variant="h6"
              sx={{ fontWeight: 600, mb: 2, fontSize: "16px" }}
            >
              QR Code
            </Typography>
            <QrCodeGenerator
              experienceId={experienceId}
              experienceName={experienceName}
            />
          </Paper>
        );
      default:
        return null;
    }
  };

  return (
    <div className="flex h-screen max-h-screen overflow-hidden">
      {/* Sidebar Container */}
      <div className="flex-shrink-0">
        <Sidebar open={open} setOpen={setOpen} animate={true}>
          <SidebarBody className="justify-between gap-10 bg-[#e6e6e6] bg-gradient-to-br from-[#e6e6e6] to-[#ffffff]">
            <div className="flex flex-1 flex-col overflow-x-hidden overflow-y-auto">
              {/* Logo - centered when collapsed */}
              <div className={cn("mb-6", !open && "flex justify-center")}>
                <Logo />
              </div>
              
              {/* Navigation Links */}
              <div className="flex flex-col gap-3">
                {links.map((link, idx) => {
                  const isActive = activeSection === (idx === 0 ? "experience" : idx === 1 ? "build" : "qr");
                  return (
                    <div
                      key={idx}
                      className={cn(
                        "cursor-pointer transition-all duration-200 ease-in-out rounded-xl border",
                        open 
                          ? "p-4 mx-0" 
                          : "p-3 mx-auto w-12 h-12 flex items-center justify-center",
                        isActive
                          ? "bg-[#6366F1] border-[#6366F1] shadow-lg text-white"
                          : "bg-transparent border-transparent hover:bg-white/10 hover:border-white/20 text-black"
                      )}
                      onClick={(e: React.MouseEvent) => {
                        e.preventDefault();
                        link.onClick();
                      }}
                    >
                      {open ? (
                        <div className="flex items-center justify-start gap-3 w-full">
                          <div className={cn(
                            "text-2xl transition-colors duration-200",
                            isActive ? "text-white" : "text-black"
                          )}>
                            {link.icon}
                          </div>
                          <span className={cn(
                            "font-semibold text-lg transition-colors duration-200",
                            isActive ? "text-white" : "text-black"
                          )}>
                            {link.label}
                          </span>
                        </div>
                      ) : (
                        <div className={cn(
                          "flex items-center justify-center w-full h-full text-2xl transition-colors duration-200",
                          isActive ? "text-white" : "text-black"
                        )}>
                          {link.icon}
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
              
              {/* Sidebar Content - only show when expanded */}
              {open && (
                <div className="mt-4 flex-1 overflow-y-auto hide-scrollbar">
                  <div className="bg-white/10 backdrop-blur-sm rounded-xl p-2 border border-white/20">
                    <div className="text-white">
                      {renderSidebarContent()}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </SidebarBody>
        </Sidebar>
      </div>
      
      {/* Main Content Area with proper spacing */}
      <div className="flex flex-1 min-h-0 overflow-hidden">
        <div className="flex flex-1 p-6">
          {children}
        </div>
      </div>
    </div>
  );
}

export const Logo = () => {
  const { open } = useSidebar();
  
  return (
    <a
      href="#"
      className={cn(
        "relative z-20 flex items-center text-xl font-bold text-white",
        open ? "space-x-4 py-3 px-2" : "justify-center py-2"
      )}
    >
      {/* Konnekt Logo */}
      <div className="h-12 w-12 shrink-0 relative">
        <img
          src="/konnekt-logo.svg"
          alt="Konnekt - Connecting Offline & Online Retail"
          className="w-full h-full object-contain"
          onError={(e) => {
            // Fallback to other logo files if konnekt-logo.svg fails to load
            const target = e.target as HTMLImageElement;
            target.src = "/Missing Link 2.png";
            target.onerror = () => {
              target.src = "/Missing Link.svg";
              target.onerror = () => {
                target.src = "/Missing Link Sphere.png";
              };
            };
          }}
        />
      </div>
      {open && (
        <motion.span
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="font-bold whitespace-nowrap text-black text-xl"
        >
          Konnekt
        </motion.span>
      )}
    </a>
  );
};
