import React, { useState, useEffect } from "react";
import ScratchCardModule from "@components/modules/ScratchCardModule";
import SpinWheelModule from "@components/modules/SpinWheelModule";
import CSATFeedbackModule from "@components/modules/CSATFeedbackModule";
import getCroppedImg from "../../utils/cropImage";
import { type Module, type SurveyQuestion } from "../../App";
import Switch from "../ui/Switch";

// Re-export Module type for use in other components
export type { Module };

// --- Copied from App.tsx --- //

// Copied from App.tsx - UserDetailsFieldSetting
export interface UserDetailsFieldSetting {
  visible: boolean;
  label: string;
  required?: boolean; // Optional: for future use
}

// Updated UserDetailsContent to match App.tsx
export interface UserDetailsContent extends CustomizableButtonStyles {
  fieldSettings: {
    name: UserDetailsFieldSetting;
    surname: User<PERSON>eta<PERSON>FieldSetting;
    email: UserDetailsFieldSetting;
    phone: UserDetailsFieldSetting;
    message: UserDetailsFieldSetting;
  };
  formData: {
    name: string;
    surname: string;
    email: string;
    phone: string;
    message: string;
  };
}

export interface CustomizableButtonStyles {
  buttonText?: string;
  buttonTextColor?: string;
  buttonBackgroundColor?: string;
  buttonBorderColor?: string;
}

export interface SetupStep {
  id: string;
  description: string;
}

export interface SetupGuideContent extends CustomizableButtonStyles {
  steps: SetupStep[];
}

export interface WarrantyContent extends CustomizableButtonStyles {
  productName: string;
  productModel?: string;
  serialNumber: string;
  purchaseDate: string;
  warrantyLength: string;
  customerName: string;
  email: string;
  phone?: string;
  retailerName?: string;
  orderNumber?: string;
  receiptFile?: File | null;
  receiptPreviewUrl?: string;
  isTermsAccepted: boolean;
  registrationDate?: string;
  warrantyCode?: string;
  notes?: string;
  isSerialNumberValid?: boolean;
  isSubmitting?: boolean;
  submitError?: string;
}

export interface ShoppingLink {
  id: string;
  url: string;
  displayText: string;
}

export interface ShoppingLinksContent extends CustomizableButtonStyles {
  title?: string;
  links: ShoppingLink[];
  mediaUrl?: string;
  mediaType?: "pdf" | "jpg" | "jpeg" | "png" | "svg";
  mediaFileName?: string;
}



export interface CSATRating {
  value: number;
  label: string;
  emoji?: string;
}

export interface FeedbackSurveyContent extends CustomizableButtonStyles {
  title?: string;
  csatType?: 'feedback' | 'satisfaction' | 'nps' | 'custom';
  questions: SurveyQuestion[];
  enableCSAT?: boolean;
  csatQuestion?: string;
  csatRatings?: CSATRating[];
  enableNPS?: boolean;
  npsQuestion?: string;
  enableStarRating?: boolean;
  starRatingQuestion?: string;
  enableFollowUp?: boolean;
  followUpQuestion?: string;
  thankYouMessage?: string;
}

export interface CustomButtonContent {
  text: string;
  url: string;
  textColor: string;
  buttonColor: string;
  borderColor: string;
}

export interface RegistrationModuleContent extends CustomizableButtonStyles {
  collectPurchaseDetails: boolean;
  purchaseFields?: { [key: string]: { required?: boolean } };
  requireReceiptApproval?: boolean;
  disableCompleteProfileScreen?: boolean;
  enableMultipleRegistrations?: boolean;
  formData?: Record<string, any>;
}

export interface FormSurveyModuleContent extends CustomizableButtonStyles {
  surveyDefinitionUrl?: string;
  title: string;
}

export interface DiscountModuleContent extends CustomizableButtonStyles {
  shopifyDiscountCode: string;
  destinationPageUrl: string;
  productInfo?: string;
  discountTerms?: string;
}

export interface IframeModuleContent {
  embedType: "url" | "html";
  source: string;
  customAttributes?: string;
}

export interface DocumentModuleContent extends CustomizableButtonStyles {
  documentName: string;
  documentUrl: string;
}

export interface LinkModuleContent extends CustomizableButtonStyles {
  destinationUrl: string;
  linkText?: string;
}

export interface VideoModuleContent {
  sourceType: "url" | "embed" | "upload";
  source: string;
  autoplay?: boolean;
}

export interface ScratchAreaData {
  id: string;
  scratchText: string;
  isRevealed: boolean;
  scratchProgress: number;
  rewardType: "prize" | "discount" | "none" | "failure";
  rewardValue: string;
  revealedTextLine1: string;
  revealedTextLine2: string;
  prizeTier?: 1 | 2 | 3; // 1st, 2nd, 3rd place
  metallicEffect?: "gold" | "silver" | "bronze" | "none";
  prizeRank?: string; // "1st Prize", "2nd Prize", "3rd Prize"
}

export interface ScratchCardModuleContent extends CustomizableButtonStyles {
  headingText: string;
  subTextContent: string;
  initialScratchAreas: ScratchAreaData[];
  whatsappNumber?: string;
  countryCode?: string; // Add this line
}

export interface SpinWheelPrize {
  id: string;
  text: string;
  color: string; // Hex color for the segment
  imageSrc?: string; // Optional: If prizes can be images on the segments
}

export interface SpinWheelModuleContent extends CustomizableButtonStyles {
  headingText: string;
  subTextContent: string;
  prizes: SpinWheelPrize[];
  wheelBackgroundImageSrc?: string; // Optional: For the main wheel background
  centerPinImageSrc?: string; // Optional: For an image at the center/pin of the wheel
  whatsappNumber?: string;
  countryCode?: string;
}

export interface MediaItem {
  id: string;
  url: string;
  type: "image" | "video";
  crop?: { x: number; y: number; width: number; height: number };
}

export interface HeaderConfig {
  appName: string;
  appSubtitle: string;
  getStartedButtonText: string;
}

interface ScreenProps {
  backgroundMedia?: MediaItem | null;
  modules?: Module[];
  currentScreen?: number;
  totalScreens?: number;
  onScreenChange?: (screenIndex: number) => void;
  isFullScreenView?: boolean;
  onUpdateModuleContent?: (
    moduleId: string,
    newContent: Partial<Module["content"]>,
  ) => void;
  headerConfig?: HeaderConfig;
}



const moduleButtonLabel: Record<string, string> = {
  ScratchCard: "SCRATCH TO WIN!",
  SpinWheel: "SPIN THE WHEEL!",
  Warranty: "ACTIVATE WARRANTY",
  "Setup Guide": "QUICK START GUIDE",
  Registration: "REGISTER PRODUCT",
  FeedbackSurvey: "FEEDBACK",
  ShoppingLinks: "SHOP ACCESSORIES",
  CustomButton: "CUSTOM BUTTON",
  UserDetails: "SUBMIT DETAILS",
};

// Separate components for forms to fix React hooks error
const RegistrationForm: React.FC<{ module: Module }> = ({ module }) => {
  const content = module.content as RegistrationModuleContent;
  const [formData, setFormData] = React.useState({
    customerName: '',
    email: '',
    phone: '',
    purchaseDate: '',
    quantity: 1,
    placeOfPurchase: '',
    serialNumber: '',
    receiptUrl: '',
    receiptFile: null as File | null
  });
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [submitMessage, setSubmitMessage] = React.useState('');

  const handleInputChange = (field: string, value: string | number | File | null) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    console.log(`Field: ${field}, Value: ${value}`);
  };

  const handleSubmit = async () => {
    if (!formData.customerName || !formData.email) {
      setSubmitMessage('Please fill in all required fields.');
      return;
    }

    setIsSubmitting(true);
    try {
      const registrationData = {
        ...formData,
        collect_purchase_details: content.collectPurchaseDetails,
        registration_date: new Date().toISOString(),
        registration_code: `REG-${Date.now()}`,
        module_type: 'registration'
      };

      const existingData = JSON.parse(localStorage.getItem('product_registrations') || '[]');
      existingData.push(registrationData);
      localStorage.setItem('product_registrations', JSON.stringify(existingData));
      
      console.log('Registration saved:', registrationData);
      setSubmitMessage('Registration completed successfully!');
    } catch (error) {
      console.error('Error saving registration:', error);
      setSubmitMessage('Error completing registration. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="p-4 bg-white rounded-lg shadow-md" style={{
      background: 'radial-gradient(circle, rgba(255, 255, 255, 1) 0%, rgba(222, 222, 222, 1) 50%, rgba(255, 255, 255, 1) 100%)',
      borderRadius: '12px',
      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
    }}>
      <h4 className="font-semibold text-sm mb-3 text-white">
        {module.title || "Product Registration"}
      </h4>
      
      <div className="space-y-3">
        <div>
          <label className="block text-xs font-medium text-white mb-1">Customer Name *</label>
          <input
            type="text"
            value={formData.customerName}
            onChange={(e) => handleInputChange('customerName', e.target.value)}
            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-black"
            style={{
              minHeight: '40px',
              fontSize: '16px',
              pointerEvents: 'auto',
              touchAction: 'auto',
              zIndex: 10,
              WebkitUserSelect: 'text',
              userSelect: 'text',
              color: '#000000 !important'
            }}
            placeholder="Enter your name"
          />
        </div>
        
        <div>
          <label className="block text-xs font-medium text-white mb-1">Email *</label>
          <input
            type="email"
            value={formData.email}
            onChange={(e) => handleInputChange('email', e.target.value)}
            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-black"
            style={{
              minHeight: '40px',
              fontSize: '16px',
              pointerEvents: 'auto',
              touchAction: 'auto',
              WebkitUserSelect: 'text',
              userSelect: 'text',
              zIndex: 10,
              color: '#000000 !important'
            }}
            placeholder="Enter your email"
          />
        </div>
        
        <div>
          <label className="block text-xs font-medium text-white mb-1">Phone</label>
          <input
            type="tel"
            value={formData.phone}
            onChange={(e) => handleInputChange('phone', e.target.value)}
            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-black"
            style={{
              minHeight: '40px',
              fontSize: '16px',
              pointerEvents: 'auto',
              touchAction: 'auto',
              zIndex: 10,
              WebkitUserSelect: 'text',
              userSelect: 'text',
              color: '#000000 !important'
            }}
            placeholder="Enter your phone"
          />
        </div>
        
        {content.collectPurchaseDetails && (
          <>
            <div>
              <label className="block text-xs font-medium text-white mb-1">Purchase Date</label>
              <input
                type="date"
                value={formData.purchaseDate}
                onChange={(e) => handleInputChange('purchaseDate', e.target.value)}
                className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-black"
            style={{
              minHeight: '40px',
              fontSize: '16px',
              pointerEvents: 'auto',
              touchAction: 'auto',
              zIndex: 10,
              WebkitUserSelect: 'text',
              userSelect: 'text',
              color: '#000000 !important'
            }}
              />
            </div>
            
            <div>
              <label className="block text-xs font-medium text-white mb-1">Place of Purchase</label>
              <input
                type="text"
                value={formData.placeOfPurchase}
                onChange={(e) => handleInputChange('placeOfPurchase', e.target.value)}
                className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-black"
            style={{
              minHeight: '40px',
              fontSize: '16px',
              pointerEvents: 'auto',
              touchAction: 'auto',
              zIndex: 10,
              WebkitUserSelect: 'text',
              userSelect: 'text',
              color: '#000000 !important'
            }}
                placeholder="Where did you buy this?"
              />
            </div>
            
            <div>
              <label className="block text-xs font-medium text-white mb-1">Serial Number</label>
              <input
                type="text"
                value={formData.serialNumber}
                onChange={(e) => handleInputChange('serialNumber', e.target.value)}
                className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-black"
            style={{
              minHeight: '40px',
              fontSize: '16px',
              pointerEvents: 'auto',
              touchAction: 'auto',
              zIndex: 10,
              WebkitUserSelect: 'text',
              userSelect: 'text',
              color: '#000000 !important'
            }}
                placeholder="Enter serial number"
              />
            </div>
          </>
        )}
        
        <div>
          <label className="block text-xs font-medium text-white mb-1">Receipt Upload (Optional)</label>
          <input
            type="file"
            accept="image/*"
            capture="environment"
            onChange={(e) => {
              const file = e.target.files?.[0] || null;
              handleInputChange('receiptFile', file);
            }}
            className="w-full px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 bg-white"
          />
          <p className="text-xs text-gray-200 mt-1">Take a photo or upload your purchase receipt</p>
        </div>
        
        <button
          onClick={handleSubmit}
          disabled={isSubmitting}
          className="w-full py-3 px-6 bg-white text-purple-600 rounded-xl font-semibold text-sm hover:bg-gray-100 transition-all duration-200 disabled:opacity-50 shadow-md hover:shadow-lg transform hover:scale-[1.02] active:scale-[0.98]"
          style={{
            border: '2px solid #6366F1',
            boxShadow: '0 4px 12px rgba(124, 58, 237, 0.15)'
          }}
        >
          {isSubmitting ? 'Registering...' : 'Complete Registration'}
        </button>
        
        {submitMessage && (
          <p className={`text-xs mt-2 ${submitMessage.includes('Error') ? 'text-red-200' : 'text-green-200'}`}>
            {submitMessage}
          </p>
        )}
      </div>
    </div>
  );
};

const WarrantyForm: React.FC<{ module: Module }> = ({ module }) => {
  const content = module.content as WarrantyContent;
  const [formData, setFormData] = React.useState({
    productName: content.productName || '',
    productModel: content.productModel || '',
    serialNumber: content.serialNumber || '',
    purchaseDate: content.purchaseDate || '',
    customerName: content.customerName || '',
    email: content.email || '',
    phone: content.phone || '',
    isTermsAccepted: content.isTermsAccepted || false,
    receiptFile: null as File | null
  });
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [submitMessage, setSubmitMessage] = React.useState('');

  const handleInputChange = (field: string, value: string | boolean | File | null) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async () => {
    if (!formData.productName || !formData.serialNumber || !formData.customerName || !formData.email || !formData.isTermsAccepted) {
      setSubmitMessage('Please fill in all required fields and accept terms.');
      return;
    }

    setIsSubmitting(true);
    try {
      const warrantyData = {
        ...formData,
        warranty_length: content.warrantyLength,
        registration_date: new Date().toISOString(),
        warranty_code: `WR-${Date.now()}`,
        module_type: 'warranty'
      };

      // Save to localStorage for now (can be enhanced to use proper database later)
      const existingData = JSON.parse(localStorage.getItem('warranty_registrations') || '[]');
      existingData.push(warrantyData);
      localStorage.setItem('warranty_registrations', JSON.stringify(existingData));
      
      console.log('Warranty registration saved:', warrantyData);
      setSubmitMessage('Warranty registered successfully!');
    } catch (error) {
      console.error('Error saving warranty:', error);
      setSubmitMessage('Error registering warranty. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="p-4 bg-white rounded-lg shadow-md" style={{
      background: 'radial-gradient(circle, rgba(255, 255, 255, 1) 0%, rgba(222, 222, 222, 1) 50%, rgba(255, 255, 255, 1) 100%)',
      borderRadius: '12px',
      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
    }}>
      <h4 className="font-semibold text-sm mb-3 text-white">
        {module.title || "Warranty Registration"}
      </h4>
      
      <div className="space-y-3">
        <div>
          <label className="block text-xs font-medium text-white mb-1">Product Name *</label>
          <input
            type="text"
            value={formData.productName}
            onChange={(e) => handleInputChange('productName', e.target.value)}
            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-black"
            style={{
              minHeight: '40px',
              fontSize: '16px',
              pointerEvents: 'auto',
              touchAction: 'auto',
              zIndex: 10,
              WebkitUserSelect: 'text',
              userSelect: 'text',
              color: '#000000 !important'
            }}
            placeholder="Enter product name"
          />
        </div>
        
        <div>
          <label className="block text-xs font-medium text-white mb-1">Product Model</label>
          <input
            type="text"
            value={formData.productModel}
            onChange={(e) => handleInputChange('productModel', e.target.value)}
            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-black"
            style={{
              minHeight: '40px',
              fontSize: '16px',
              pointerEvents: 'auto',
              touchAction: 'auto',
              zIndex: 10,
              WebkitUserSelect: 'text',
              userSelect: 'text',
              color: '#000000 !important'
            }}
            placeholder="Enter product model"
          />
        </div>
        
        <div>
          <label className="block text-xs font-medium text-white mb-1">Serial Number *</label>
          <input
            type="text"
            value={formData.serialNumber}
            onChange={(e) => handleInputChange('serialNumber', e.target.value)}
            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-black"
            style={{
              minHeight: '40px',
              fontSize: '16px',
              pointerEvents: 'auto',
              touchAction: 'auto',
              zIndex: 10,
              WebkitUserSelect: 'text',
              userSelect: 'text',
              color: '#000000 !important'
            }}
            placeholder="Enter serial number"
          />
        </div>
        
        <div>
          <label className="block text-xs font-medium text-white mb-1">Purchase Date</label>
          <input
            type="date"
            value={formData.purchaseDate}
            onChange={(e) => handleInputChange('purchaseDate', e.target.value)}
            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-black"
            style={{
              minHeight: '40px',
              fontSize: '16px',
              pointerEvents: 'auto',
              touchAction: 'auto',
              zIndex: 10,
              WebkitUserSelect: 'text',
              userSelect: 'text',
              color: '#000000 !important'
            }}
          />
        </div>
        
        <div>
          <label className="block text-xs font-medium text-white mb-1">Customer Name *</label>
          <input
            type="text"
            value={formData.customerName}
            onChange={(e) => handleInputChange('customerName', e.target.value)}
            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-black"
            style={{
              minHeight: '40px',
              fontSize: '16px',
              pointerEvents: 'auto',
              touchAction: 'auto',
              zIndex: 10,
              WebkitUserSelect: 'text',
              userSelect: 'text',
              color: '#000000 !important'
            }}
            placeholder="Enter your name"
          />
        </div>
        
        <div>
          <label className="block text-xs font-medium text-white mb-1">Email *</label>
          <input
            type="email"
            value={formData.email}
            onChange={(e) => handleInputChange('email', e.target.value)}
            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-black"
            style={{
              minHeight: '40px',
              fontSize: '16px',
              pointerEvents: 'auto',
              touchAction: 'auto',
              zIndex: 10,
              WebkitUserSelect: 'text',
              userSelect: 'text',
              color: '#000000 !important'
            }}
            placeholder="Enter your email"
          />
        </div>
        
        <div>
          <label className="block text-xs font-medium text-white mb-1">Phone (Optional)</label>
          <input
            type="tel"
            value={formData.phone}
            onChange={(e) => handleInputChange('phone', e.target.value)}
            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-black"
            style={{
              minHeight: '40px',
              fontSize: '16px',
              pointerEvents: 'auto',
              touchAction: 'auto',
              zIndex: 10,
              WebkitUserSelect: 'text',
              userSelect: 'text',
              color: '#000000 !important'
            }}
            placeholder="Enter your phone"
          />
        </div>
        
        <div>
          <label className="block text-xs font-medium text-white mb-1">Receipt Upload (Optional)</label>
          <input
            type="file"
            accept="image/*"
            capture="environment"
            onChange={(e) => {
              const file = e.target.files?.[0] || null;
              handleInputChange('receiptFile', file);
            }}
            className="w-full px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 bg-white"
          />
          <p className="text-xs text-gray-200 mt-1">Take a photo or upload your purchase receipt</p>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            type="button"
            role="switch"
            aria-checked={formData.isTermsAccepted}
            onClick={() => handleInputChange('isTermsAccepted', !formData.isTermsAccepted)}
            className={`
              w-8 h-5 relative inline-flex items-center rounded-full
              transition-colors duration-200 ease-in-out
              focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
              ${formData.isTermsAccepted ? 'bg-blue-500 shadow-inner' : 'bg-gray-300 shadow-inner'}
              cursor-pointer
            `}
          >
            <span
              className={`
                w-3 h-3 inline-block rounded-full bg-white shadow-lg
                transform transition-transform duration-200 ease-in-out
                ${formData.isTermsAccepted ? 'translate-x-4' : 'translate-x-1'}
              `}
            />
          </button>
          <label className="text-xs text-white cursor-pointer">
            I accept the terms and conditions for warranty registration *
          </label>
        </div>
        
        <button
          onClick={handleSubmit}
          disabled={isSubmitting}
          className="w-full py-3 px-6 bg-white text-purple-600 rounded-xl font-semibold text-sm hover:bg-gray-100 transition-all duration-200 disabled:opacity-50 shadow-md hover:shadow-lg transform hover:scale-[1.02] active:scale-[0.98]"
          style={{
            border: '2px solid #6366F1',
            boxShadow: '0 4px 12px rgba(124, 58, 237, 0.15)'
          }}
        >
          {isSubmitting ? 'Registering...' : 'Register Warranty'}
        </button>
        
        {submitMessage && (
          <p className={`text-xs mt-2 ${submitMessage.includes('Error') ? 'text-red-200' : 'text-green-200'}`}>
            {submitMessage}
          </p>
        )}
      </div>
    </div>
  );
};

const renderModuleContent = (module: Module, props: ScreenProps) => {
  switch (module.type) {
    case "Warranty": {
      return <WarrantyForm module={module} />;
    }
    case "Registration": {
      return <RegistrationForm module={module} />;
    }
    case "SetupGuide": {
      const content = module.content as SetupGuideContent;
      return (
        <div className="p-3 bg-white rounded-lg shadow">
          <h4 className="font-semibold text-sm mb-2 text-gray-800">
            {module.title || "Setup Guide"}
          </h4>
          <ul className="list-disc list-inside text-xs text-gray-600 space-y-1 mb-3">
            {content.steps.slice(0, 3).map((step) => (
              <li key={step.id}>{step.description}</li>
            ))}
            {content.steps.length > 3 && (
              <li className="italic">...and more</li>
            )}
          </ul>
          {content.buttonText && (
            <button
              style={{
                backgroundColor: content.buttonBackgroundColor || "#1976d2",
                color: content.buttonTextColor || "white",
                borderColor:
                  content.buttonBorderColor ||
                  content.buttonBackgroundColor ||
                  "#1976d2",
                borderWidth: "1px",
                borderStyle: "solid",
                width: "100%",
                padding: "8px 16px",
                borderRadius: "6px",
                fontSize: "0.875rem",
                fontWeight: 500,
                textAlign: "center",
                cursor: "pointer",
              }}
              className="hover:opacity-90 transition-opacity"
            >
              {content.buttonText}
            </button>
          )}
        </div>
      );
    }
    case "ShoppingLinks": {
      console.log("[Screen] ShoppingLinks content:", module.content);
      const content = module.content as ShoppingLinksContent;
      return (
        <div className="p-3 bg-white rounded-lg shadow">
          {content.mediaUrl && (
            <div style={{ marginBottom: "12px" }}>
              {content.mediaType === "pdf" ? (
                <a
                  href={content.mediaUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-xs text-blue-600 underline hover:text-blue-800 block text-center p-2 border border-blue-300 rounded bg-blue-50"
                >
                  View: {content.mediaFileName || "Document (PDF)"}
                </a>
              ) : content.mediaType === "jpg" ||
                content.mediaType === "jpeg" ||
                content.mediaType === "png" ||
                content.mediaType === "svg" ? (
                <img
                  src={content.mediaUrl}
                  alt={content.mediaFileName || "Uploaded media"}
                  style={{
                    width: "100%",
                    height: "auto",
                    objectFit: "cover",
                    borderRadius: "4px",
                    border: "1px solid #eee",
                  }}
                />
              ) : null}
            </div>
          )}
          <h4 className="font-semibold text-sm mb-2 text-gray-800">
            {content.title || module.title || "Shop Now"}
          </h4>
          <ul className="list-disc list-inside text-xs text-blue-600 space-y-1 mb-3">
            {content.links.map((link) => (
              <li key={link.id}>
                <a
                  href={link.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="underline hover:text-blue-800"
                >
                  {link.displayText}
                </a>
              </li>
            ))}
          </ul>
          {content.buttonText && (
            <button
              style={{
                backgroundColor: content.buttonBackgroundColor || "#1976d2",
                color: content.buttonTextColor || "white",
                borderColor:
                  content.buttonBorderColor ||
                  content.buttonBackgroundColor ||
                  "#1976d2",
                borderWidth: "1px",
                borderStyle: "solid",
                width: "100%",
                padding: "8px 16px",
                borderRadius: "6px",
                fontSize: "0.875rem",
                fontWeight: 500,
                textAlign: "center",
                cursor: "pointer",
              }}
              className="hover:opacity-90 transition-opacity"
            >
              {content.buttonText}
            </button>
          )}
        </div>
      );
    }
    case "ScratchCard":
      const scratchCardContent = module.content as ScratchCardModuleContent;
      return <ScratchCardModule {...scratchCardContent} />;
    case "SpinWheel":
      return (
        <SpinWheelModule {...(module.content as SpinWheelModuleContent)} />
      );
    case "UserDetails": {
      const content = module.content as UserDetailsContent;

      // Initialize formData if it doesn't exist
      if (!content.formData) {
        content.formData = {
          name: "",
          surname: "",
          email: "",
          phone: "",
          message: "",
        };
      }

      const handleInputChange = (
        fieldName: keyof UserDetailsContent["formData"],
        value: string,
      ) => {
        if (props.onUpdateModuleContent) {
          const updatedFormData = { ...content.formData, [fieldName]: value };
          props.onUpdateModuleContent(module.id, {
            ...content,
            formData: updatedFormData,
          });
        }
      };

      return (
        <div className="p-4 bg-white rounded-lg shadow-md">
          <h4 className="font-semibold text-lg mb-4 text-gray-700">
            {module.title || "User Details"}
          </h4>
          <form className="space-y-4">
            {(
              Object.keys(content.fieldSettings) as Array<
                keyof UserDetailsContent["fieldSettings"]
              >
            ).map((key) => {
              const setting = content.fieldSettings[key];
              if (!setting.visible) return null;

              const fieldType =
                key === "email" ? "email" : key === "phone" ? "tel" : "text";
              const commonInputClasses =
                "w-full p-3 border border-gray-300 rounded-md text-sm shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-black";

              return (
                <div key={key}>
                  <label
                    htmlFor={`${module.id}-${key}`}
                    className="block text-sm font-medium text-gray-600 mb-1"
                  >
                    {setting.label}
                  </label>
                  {key === "message" ? (
                    <textarea
                      id={`${module.id}-${key}`}
                      rows={4}
                      className={commonInputClasses}
                      value={content.formData[key] || ""}
                      onChange={(e) => handleInputChange(key, e.target.value)}
                      placeholder={`Enter ${setting.label.toLowerCase()}`}
                      style={{ color: '#000000 !important' }}
                    />
                  ) : (
                    <input
                      type={fieldType}
                      id={`${module.id}-${key}`}
                      className={commonInputClasses}
                      value={content.formData[key] || ""}
                      onChange={(e) => handleInputChange(key, e.target.value)}
                      placeholder={`Enter ${setting.label.toLowerCase()}`}
                      style={{ color: '#000000 !important' }}
                    />
                  )}
                </div>
              );
            })}
            {content.buttonText && (
              <button
                type="button" // Prevent default form submission, handle via JS if needed
                style={{
                  backgroundColor: content.buttonBackgroundColor || "#1976d2",
                  color: content.buttonTextColor || "white",
                  borderColor:
                    content.buttonBorderColor ||
                    content.buttonBackgroundColor ||
                    "#1976d2",
                  borderWidth: "1px",
                  borderStyle: "solid",
                  width: "100%",
                  padding: "10px 16px",
                  borderRadius: "6px",
                  fontSize: "1rem",
                  fontWeight: 500,
                  textAlign: "center",
                  cursor: "pointer",
                  marginTop: "16px",
                }}
                className="hover:opacity-90 transition-opacity"
              >
                {content.buttonText}
              </button>
            )}
          </form>
        </div>
      );
    }
    case "FeedbackSurvey": {
      const content = module.content as FeedbackSurveyContent;
      return (
        <CSATFeedbackModule
          content={{
            title: content.title || "Customer Satisfaction Survey",
            csatType: content.csatType || "feedback",
            questions: content.questions || [],
            thankYouMessage: content.thankYouMessage || "Thank you for your feedback!",
            enableCSAT: content.enableCSAT ?? true,
            enableNPS: content.enableNPS ?? true,
            enableStarRating: content.enableStarRating ?? false,
            csatQuestion: content.csatQuestion || "How satisfied are you with our service?",
            npsQuestion: content.npsQuestion || "How likely are you to recommend us?",
            starRatingQuestion: content.starRatingQuestion || "Please rate your experience:",
            enableFollowUp: content.enableFollowUp ?? false,
            followUpQuestion: content.followUpQuestion || "Please tell us more about your experience:",
            buttonText: content.buttonText || "Submit Feedback",
            buttonTextColor: content.buttonTextColor || "#FFFFFF",
            buttonBackgroundColor: content.buttonBackgroundColor || "#007BFF",
            buttonBorderColor: content.buttonBorderColor || "#007BFF"
          }}
          onSubmit={(responses) => {
            console.log('CSAT Feedback submitted:', responses);
            // Get the current experience ID and module ID from the URL or context
            const urlParams = new URLSearchParams(window.location.search);
            const experienceId = urlParams.get('experienceId') || 'preview';
            
            // Only save to Supabase if we're not in preview mode
            if (experienceId !== 'preview' && module?.id) {
              import('../../utils/supabaseClient').then(({ saveCSATResponse }) => {
                saveCSATResponse(
                  experienceId,
                  module.id,
                  responses
                ).then(() => {
                  console.log('CSAT response saved to Supabase');
                }).catch(error => {
                  console.error('Failed to save CSAT response:', error);
                });
              }).catch(error => {
                console.error('Failed to import saveCSATResponse:', error);
              });
            }
          }}
          onContentChange={() => {
            // Content changes handled in module editor
          }}
        />
      );
    }
    case "FormSurvey": {
      const content = module.content as FormSurveyModuleContent;
      const [selectedRating, setSelectedRating] = React.useState<number | null>(null);
      const [selectedEmoji, setSelectedEmoji] = React.useState<string | null>(null);
      const [selectedThumb, setSelectedThumb] = React.useState<'up' | 'down' | null>(null);
      const [feedback, setFeedback] = React.useState<string>('');
      const [submitted, setSubmitted] = React.useState(false);

      const handleSubmitFeedback = () => {
        // Here you would typically send the feedback to your backend
        console.log('Feedback submitted:', {
          rating: selectedRating,
          emoji: selectedEmoji,
          thumb: selectedThumb,
          feedback: feedback
        });
        setSubmitted(true);
      };

      if (submitted) {
        return (
          <div className="p-4 bg-green-50 rounded-lg shadow border border-green-200">
            <div className="text-center">
              <div className="text-2xl mb-2">✅</div>
              <h4 className="font-semibold text-sm text-green-800 mb-1">
                Thank you for your feedback!
              </h4>
              <p className="text-xs text-green-600">
                Your response helps us improve our service.
              </p>
            </div>
          </div>
        );
      }

      return (
        <div className="p-4 bg-white rounded-lg shadow">
          <h4 className="font-semibold text-sm mb-3 text-gray-800 text-center">
            {content.title || module.title || "How satisfied are you?"}
          </h4>
          
          {/* Emoji Rating */}
          <div className="mb-4">
            <p className="text-xs text-gray-600 mb-2 text-center">Rate your experience</p>
            <div className="flex justify-center space-x-2">
              {[
                { emoji: '😡', label: 'Very Unsatisfied', value: 1 },
                { emoji: '😕', label: 'Unsatisfied', value: 2 },
                { emoji: '😐', label: 'Neutral', value: 3 },
                { emoji: '😊', label: 'Satisfied', value: 4 },
                { emoji: '😍', label: 'Very Satisfied', value: 5 }
              ].map((item) => (
                <button
                  key={item.value}
                  onClick={() => {
                    setSelectedEmoji(item.emoji);
                    setSelectedRating(item.value);
                  }}
                  className={`text-2xl p-2 rounded-full transition-all duration-200 hover:scale-110 ${
                    selectedEmoji === item.emoji 
                      ? 'bg-blue-100 ring-2 ring-blue-400 scale-110' 
                      : 'hover:bg-gray-100'
                  }`}
                  title={item.label}
                >
                  {item.emoji}
                </button>
              ))}
            </div>
          </div>

          {/* Star Rating */}
          <div className="mb-4">
            <p className="text-xs text-gray-600 mb-2 text-center">Or rate with stars</p>
            <div className="flex justify-center space-x-1">
              {[1, 2, 3, 4, 5].map((star) => (
                <button
                  key={star}
                  onClick={() => setSelectedRating(star)}
                  className={`text-xl transition-colors duration-200 ${
                    selectedRating && selectedRating >= star
                      ? 'text-yellow-400'
                      : 'text-gray-300 hover:text-yellow-300'
                  }`}
                >
                  ⭐
                </button>
              ))}
            </div>
          </div>

          {/* Thumbs Up/Down */}
          <div className="mb-4">
            <p className="text-xs text-gray-600 mb-2 text-center">Quick feedback</p>
            <div className="flex justify-center space-x-4">
              <button
                onClick={() => {
                  setSelectedThumb('up');
                  setSelectedRating(5);
                }}
                className={`text-2xl p-3 rounded-full transition-all duration-200 hover:scale-110 ${
                  selectedThumb === 'up'
                    ? 'bg-green-100 ring-2 ring-green-400 scale-110'
                    : 'hover:bg-gray-100'
                }`}
                title="Satisfied"
              >
                👍
              </button>
              <button
                onClick={() => {
                  setSelectedThumb('down');
                  setSelectedRating(1);
                }}
                className={`text-2xl p-3 rounded-full transition-all duration-200 hover:scale-110 ${
                  selectedThumb === 'down'
                    ? 'bg-red-100 ring-2 ring-red-400 scale-110'
                    : 'hover:bg-gray-100'
                }`}
                title="Unsatisfied"
              >
                👎
              </button>
            </div>
          </div>

          {/* Optional Text Feedback */}
          {selectedRating && (
            <div className="mb-4">
              <p className="text-xs text-gray-600 mb-2">Tell us more (optional)</p>
              <textarea
                value={feedback}
                onChange={(e) => setFeedback(e.target.value)}
                placeholder="What could we improve?"
                className="w-full p-2 text-xs border border-gray-300 rounded-md resize-none focus:ring-2 focus:ring-blue-400 focus:border-transparent"
                rows={3}
              />
            </div>
          )}

          {/* Submit Button */}
          {selectedRating && (
            <button
              onClick={handleSubmitFeedback}
              style={{
                backgroundColor: content.buttonBackgroundColor || "#1976d2",
                color: content.buttonTextColor || "white",
              }}
              className="w-full py-2 px-4 rounded-md text-sm font-medium transition-all duration-200 hover:opacity-90 hover:scale-105"
            >
              {content.buttonText || "Submit Feedback"}
            </button>
          )}

          {/* External Survey Link */}
          {content.surveyDefinitionUrl && (
            <div className="mt-3 pt-3 border-t border-gray-200">
              <a
                href={content.surveyDefinitionUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="text-xs text-blue-600 underline hover:text-blue-800 block text-center"
              >
                Take Detailed Survey
              </a>
            </div>
          )}
        </div>
      );
    }
    case "Discount": {
      const content = module.content as DiscountModuleContent;
      return (
        <div className="p-3 bg-white rounded-lg shadow">
          <h4 className="font-semibold text-sm mb-2 text-gray-800">
            {module.title || "Special Discount"}
          </h4>
          <p className="text-xs text-gray-700 font-medium mb-1">
            Discount Code:{" "}
            <span className="text-red-600 font-bold">
              {content.shopifyDiscountCode || "N/A"}
            </span>
          </p>
          {content.productInfo && (
            <p className="text-xs text-gray-600 mb-1">{content.productInfo}</p>
          )}
          {content.discountTerms && (
            <p className="text-xs text-gray-500 italic mb-3">
              Terms: {content.discountTerms}
            </p>
          )}
          {content.buttonText && (
            <button
              onClick={() =>
                content.destinationPageUrl &&
                window.open(content.destinationPageUrl, "_blank")
              }
              style={{
                backgroundColor: content.buttonBackgroundColor || "#1976d2",
                color: content.buttonTextColor || "white",
                borderColor:
                  content.buttonBorderColor ||
                  content.buttonBackgroundColor ||
                  "#1976d2",
                borderWidth: "1px",
                borderStyle: "solid",
                width: "100%",
                padding: "8px 16px",
                borderRadius: "6px",
                fontSize: "0.875rem",
                fontWeight: 500,
                textAlign: "center",
                cursor: content.destinationPageUrl ? "pointer" : "default",
                opacity: content.destinationPageUrl ? 1 : 0.7,
              }}
              className="hover:opacity-90 transition-opacity"
              disabled={!content.destinationPageUrl}
            >
              {content.buttonText}
            </button>
          )}
        </div>
      );
    }
    case "Document": {
      const content = module.content as DocumentModuleContent;
      return (
        <div className="p-3 bg-white rounded-lg shadow">
          <h4 className="font-semibold text-sm mb-2 text-gray-800">
            {content.documentName || module.title || "Document"}
          </h4>
          <a
            href={content.documentUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="text-xs text-blue-600 underline hover:text-blue-800 block mb-3"
          >
            View: {content.documentName || "Document"}
          </a>
          {content.buttonText && (
            <button
              onClick={() =>
                content.documentUrl &&
                window.open(content.documentUrl, "_blank")
              }
              style={{
                backgroundColor: content.buttonBackgroundColor || "#1976d2",
                color: content.buttonTextColor || "white",
                borderColor:
                  content.buttonBorderColor ||
                  content.buttonBackgroundColor ||
                  "#1976d2",
                borderWidth: "1px",
                borderStyle: "solid",
                width: "100%",
                padding: "8px 16px",
                borderRadius: "6px",
                fontSize: "0.875rem",
                fontWeight: 500,
                textAlign: "center",
                cursor: content.documentUrl ? "pointer" : "default",
                opacity: content.documentUrl ? 1 : 0.7,
              }}
              className="hover:opacity-90 transition-opacity"
              disabled={!content.documentUrl}
            >
              {content.buttonText}
            </button>
          )}
        </div>
      );
    }
    case "Link": {
      const content = module.content as LinkModuleContent;
      return (
        <div className="p-3 bg-white rounded-lg shadow">
          <h4 className="font-semibold text-sm mb-2 text-gray-800">
            {content.linkText || module.title || "Link"}
          </h4>
          <a
            href={content.destinationUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="text-xs text-blue-600 underline hover:text-blue-800 block mb-3"
          >
            {content.linkText || content.destinationUrl || "Visit Link"}
          </a>
          {content.buttonText && (
            <button
              onClick={() =>
                content.destinationUrl &&
                window.open(content.destinationUrl, "_blank")
              }
              style={{
                backgroundColor: content.buttonBackgroundColor || "#1976d2",
                color: content.buttonTextColor || "white",
                borderColor:
                  content.buttonBorderColor ||
                  content.buttonBackgroundColor ||
                  "#1976d2",
                borderWidth: "1px",
                borderStyle: "solid",
                width: "100%",
                padding: "8px 16px",
                borderRadius: "6px",
                fontSize: "0.875rem",
                fontWeight: 500,
                textAlign: "center",
                cursor: content.destinationUrl ? "pointer" : "default",
                opacity: content.destinationUrl ? 1 : 0.7,
              }}
              className="hover:opacity-90 transition-opacity"
              disabled={!content.destinationUrl}
            >
              {content.buttonText}
            </button>
          )}
        </div>
      );
    }
    case "UserDetails": {
      const content = module.content as UserDetailsContent;
      const [formData, setFormData] = React.useState({
        name: content.formData?.name || '',
        surname: content.formData?.surname || '',
        email: content.formData?.email || '',
        phone: content.formData?.phone || '',
        message: content.formData?.message || ''
      });
      const [isSubmitting, setIsSubmitting] = React.useState(false);
      const [submitMessage, setSubmitMessage] = React.useState('');
      const [acceptMarketing, setAcceptMarketing] = React.useState(false);
      const [acceptTerms, setAcceptTerms] = React.useState(false);

      const handleInputChange = (field: string, value: string) => {
        setFormData(prev => ({ ...prev, [field]: value }));
      };

      const handleSubmit = async () => {
        // Validate required fields
        const requiredFields = [];
        if (content.fieldSettings.name.visible && content.fieldSettings.name.required && !formData.name) {
          requiredFields.push('Name');
        }
        if (content.fieldSettings.surname.visible && content.fieldSettings.surname.required && !formData.surname) {
          requiredFields.push('Surname');
        }
        if (content.fieldSettings.email.visible && content.fieldSettings.email.required && !formData.email) {
          requiredFields.push('Email');
        }
        if (content.fieldSettings.phone.visible && content.fieldSettings.phone.required && !formData.phone) {
          requiredFields.push('Phone');
        }
        if (content.fieldSettings.message.visible && content.fieldSettings.message.required && !formData.message) {
          requiredFields.push('Message');
        }

        if (requiredFields.length > 0) {
          setSubmitMessage(`Please fill in required fields: ${requiredFields.join(', ')}`);
          return;
        }

        setIsSubmitting(true);
        try {
          const userDetailsData = {
            ...formData,
            acceptMarketing,
            acceptTerms,
            submission_date: new Date().toISOString(),
            submission_code: `UD-${Date.now()}`,
            module_type: 'user_details'
          };

          // Save to localStorage for now (can be enhanced to use proper database later)
          const existingData = JSON.parse(localStorage.getItem('user_details_submissions') || '[]');
          existingData.push(userDetailsData);
          localStorage.setItem('user_details_submissions', JSON.stringify(existingData));
          
          console.log('User details saved:', userDetailsData);
          setSubmitMessage('Details submitted successfully!');
        } catch (error) {
          console.error('Error saving user details:', error);
          setSubmitMessage('Error submitting details. Please try again.');
        } finally {
          setIsSubmitting(false);
        }
      };

      return (
        <div className="p-4 bg-white rounded-lg shadow-md" style={{
          background: 'radial-gradient(circle, rgba(255, 255, 255, 1) 0%, rgba(222, 222, 222, 1) 50%, rgba(255, 255, 255, 1) 100%)',
          borderRadius: '12px',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
        }}>
          <h4 className="font-semibold text-sm mb-3 text-white">
            {module.title || "User Details Form"}
          </h4>
          
          <div className="space-y-3">
            {content.fieldSettings.name.visible && (
              <div>
                <label className="block text-xs font-medium text-white mb-1">
                  {content.fieldSettings.name.label} {content.fieldSettings.name.required && '*'}
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-black"
            style={{
              minHeight: '40px',
              fontSize: '16px',
              pointerEvents: 'auto',
              touchAction: 'auto',
              zIndex: 10,
              WebkitUserSelect: 'text',
              userSelect: 'text',
              color: '#000000 !important'
            }}
                  placeholder={`Enter ${content.fieldSettings.name.label.toLowerCase()}`}
                />
              </div>
            )}
            
            {content.fieldSettings.surname.visible && (
              <div>
                <label className="block text-xs font-medium text-white mb-1">
                  {content.fieldSettings.surname.label} {content.fieldSettings.surname.required && '*'}
                </label>
                <input
                  type="text"
                  value={formData.surname}
                  onChange={(e) => handleInputChange('surname', e.target.value)}
                  className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-black"
            style={{
              minHeight: '40px',
              fontSize: '16px',
              pointerEvents: 'auto',
              touchAction: 'auto',
              zIndex: 10,
              WebkitUserSelect: 'text',
              userSelect: 'text',
              color: '#000000 !important'
            }}
                  placeholder={`Enter ${content.fieldSettings.surname.label.toLowerCase()}`}
                />
              </div>
            )}
            
            {content.fieldSettings.email.visible && (
              <div>
                <label className="block text-xs font-medium text-white mb-1">
                  {content.fieldSettings.email.label} {content.fieldSettings.email.required && '*'}
                </label>
                <input
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-black"
            style={{
              minHeight: '40px',
              fontSize: '16px',
              pointerEvents: 'auto',
              touchAction: 'auto',
              zIndex: 10,
              WebkitUserSelect: 'text',
              userSelect: 'text',
              color: '#000000 !important'
            }}
                  placeholder={`Enter ${content.fieldSettings.email.label.toLowerCase()}`}
                />
              </div>
            )}
            
            {content.fieldSettings.phone.visible && (
              <div>
                <label className="block text-xs font-medium text-white mb-1">
                  {content.fieldSettings.phone.label} {content.fieldSettings.phone.required && '*'}
                </label>
                <input
                  type="tel"
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-black"
            style={{
              minHeight: '40px',
              fontSize: '16px',
              pointerEvents: 'auto',
              touchAction: 'auto',
              zIndex: 10,
              WebkitUserSelect: 'text',
              userSelect: 'text',
              color: '#000000 !important'
            }}
                  placeholder={`Enter ${content.fieldSettings.phone.label.toLowerCase()}`}
                />
              </div>
            )}
            
            {content.fieldSettings.message.visible && (
              <div>
                <label className="block text-xs font-medium text-white mb-1">
                  {content.fieldSettings.message.label} {content.fieldSettings.message.required && '*'}
                </label>
                <textarea
                  value={formData.message}
                  onChange={(e) => handleInputChange('message', e.target.value)}
                  className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-black"
            style={{
              minHeight: '40px',
              fontSize: '16px',
              pointerEvents: 'auto',
              touchAction: 'auto',
              zIndex: 10,
              WebkitUserSelect: 'text',
              userSelect: 'text',
              color: '#000000 !important'
            }}
                  placeholder={`Enter ${content.fieldSettings.message.label.toLowerCase()}`}
                  rows={3}
                />
              </div>
            )}
            
            <div className="flex items-center space-x-3">
              <Switch
                checked={acceptMarketing}
                onChange={setAcceptMarketing}
                size="small"
                id="marketing-switch"
              />
              <label htmlFor="marketing-switch" className="text-xs text-white cursor-pointer">
                I would like to receive marketing communications
              </label>
            </div>
            
            <div className="flex items-center space-x-3">
              <Switch
                checked={acceptTerms}
                onChange={setAcceptTerms}
                size="small"
                id="terms-switch"
              />
              <label htmlFor="terms-switch" className="text-xs text-white cursor-pointer">
                I accept the terms and conditions *
              </label>
            </div>
            
            {submitMessage && (
              <p className={`text-xs ${submitMessage.includes('successfully') ? 'text-green-600' : 'text-red-600'}`}>
                {submitMessage}
              </p>
            )}
            
            <button
              onClick={handleSubmit}
              disabled={isSubmitting || !acceptTerms}
              style={{
                backgroundColor: content.buttonBackgroundColor || "#1976d2",
                color: content.buttonTextColor || "white",
                borderColor: content.buttonBorderColor || content.buttonBackgroundColor || "#1976d2",
                borderWidth: "1px",
                borderStyle: "solid",
                width: "100%",
                padding: "8px 16px",
                borderRadius: "6px",
                fontSize: "0.875rem",
                fontWeight: 500,
                textAlign: "center",
                cursor: (isSubmitting || !acceptTerms) ? "not-allowed" : "pointer",
                opacity: (isSubmitting || !acceptTerms) ? 0.7 : 1
              }}
              className="hover:opacity-90 transition-opacity"
            >
              {isSubmitting ? 'Submitting...' : (content.buttonText || 'Submit Details')}
            </button>
          </div>
        </div>
      );
    }
    default:
      console.warn(`Unknown module type: ${module.type}`);
      return <div>Unknown module: {module.type}</div>;
  }
};

export const Screen: React.FC<ScreenProps> = ({
  backgroundMedia,
  modules = [],
  currentScreen = 0,
  totalScreens = 1,
  onScreenChange,
  isFullScreenView = false, // Default to false for preview mode
  onUpdateModuleContent, // Added prop
  headerConfig,
}) => {
  console.log(
    "🔍 SCREEN DEBUG - backgroundMedia received:",
    JSON.stringify(backgroundMedia, null, 2),
  ); // Enhanced logging
  
  // Debug the background media processing
  const debugDisplaySrc = backgroundMedia?.url;
  const debugMediaType = backgroundMedia?.type || 'image';
  
  console.log('🔍 SCREEN DEBUG - displaySrc:', debugDisplaySrc);
  console.log('🔍 SCREEN DEBUG - mediaType:', debugMediaType);
  console.log('🔍 SCREEN DEBUG - backgroundMedia exists:', !!backgroundMedia);
  
  if (!backgroundMedia) {
    console.log('❌ SCREEN DEBUG - No backgroundMedia provided!');
  }
  if (!debugDisplaySrc) {
    console.log('❌ SCREEN DEBUG - No displaySrc from backgroundMedia!');
  }
  const [expandedModuleId, setExpandedModuleId] = useState<string | null>(null);

  // Add CSS for smooth modal animations
  React.useEffect(() => {
    const styleEl = document.createElement("style");
    styleEl.textContent = `
      .modal-container {
        transition: max-height 300ms cubic-bezier(0.4, 0, 0.2, 1), height 300ms cubic-bezier(0.4, 0, 0.2, 1) !important;
        max-height: 75vh;
      }
      .modal-container.expanded {
        max-height: 76vh !important;
      }
    `;
    document.head.appendChild(styleEl);

    return () => {
      document.head.removeChild(styleEl);
    };
  }, []);

  React.useEffect(() => {
    const handleButtonClick = () => {
      const modalContainer = document.querySelector(
        '[class*="absolute"][class*="inset-x-0"][class*="bottom-0"][class*="bg-white"]',
      );
      if (modalContainer) {
        modalContainer.classList.add("modal-container");
        // Use setTimeout to ensure the class is applied after the modal-container class
        setTimeout(() => {
          modalContainer.classList.add("expanded");
        }, 10);
      }
    };

    // Add event listeners to all buttons and links in the modal using a simpler selector
    const buttons = document.querySelectorAll(
      '[class*="absolute"][class*="bottom-0"] button, [class*="absolute"][class*="bottom-0"] a.block',
    );
    buttons.forEach((button) => {
      button.addEventListener("click", handleButtonClick);
    });

    return () => {
      // Clean up event listeners
      buttons.forEach((button) => {
        button.removeEventListener("click", handleButtonClick);
      });
    };
  }, [modules]); // Re-run when modules change

  const mediaType = backgroundMedia?.type;
  const [displaySrc, setDisplaySrc] = useState(backgroundMedia?.url || '');

  console.log('🔍 SCREEN RENDER DEBUG - Initial state:');
  console.log('  - mediaType:', mediaType);
  console.log('  - initial displaySrc:', backgroundMedia?.url);
  console.log('  - backgroundMedia.crop:', backgroundMedia?.crop);

  useEffect(() => {
    const updateDisplaySrc = async () => {
      console.log('🔍 SCREEN RENDER DEBUG - updateDisplaySrc called');
      console.log('  - backgroundMedia?.type:', backgroundMedia?.type);
      console.log('  - backgroundMedia?.url:', backgroundMedia?.url);
      console.log('  - backgroundMedia?.crop:', backgroundMedia?.crop);
      
      if (backgroundMedia?.type === "image" && backgroundMedia.crop) {
        console.log('🔍 SCREEN RENDER DEBUG - Processing cropped image');
        const croppedUrl = await getCroppedImg(backgroundMedia.url, backgroundMedia.crop);
        if (croppedUrl) {
          console.log('✅ SCREEN RENDER DEBUG - Setting cropped URL:', croppedUrl);
          setDisplaySrc(croppedUrl);
        } else {
          console.log('⚠️ SCREEN RENDER DEBUG - Cropping failed, using original URL');
          setDisplaySrc(backgroundMedia?.url || '');
        }
      } else {
        console.log('🔍 SCREEN RENDER DEBUG - Using original URL (no crop)');
        setDisplaySrc(backgroundMedia?.url || '');
      }
    };
    updateDisplaySrc();
  }, [backgroundMedia]);

  console.log("✅ SCREEN RENDER DEBUG - Final values:", {
    mediaType,
    displaySrc,
    hasBackgroundMedia: !!backgroundMedia,
    backgroundMediaUrl: backgroundMedia?.url
  });

  const handleModuleToggle = (moduleId: string) => {
    setExpandedModuleId((prevId) => (prevId === moduleId ? null : moduleId));
  };

  const handleScreenAdvance = () => {
    if (onScreenChange && currentScreen < totalScreens - 1) {
      onScreenChange(currentScreen + 1);
    }
  };

  const handleBackClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onScreenChange && currentScreen > 0) {
      onScreenChange(currentScreen - 1);
    }
  };

  const isFirstScreen = currentScreen === 0;

  return (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        minHeight: "100%",
      }}
    >
      {!isFirstScreen && (
        <button
          onClick={handleBackClick}
          className="absolute top-4 left-4 z-20 w-10 h-10 flex items-center justify-center bg-white bg-opacity-80 rounded-full shadow-md hover:bg-opacity-100 transition-all"
        >
          <span className="text-black text-xl">‹</span>
        </button>
      )}

      <div className="absolute inset-0 w-full h-full overflow-hidden">
  {mediaType === "video" ? (
    <video
      key={displaySrc}
      src={displaySrc}
      autoPlay
      loop
      muted
      playsInline
      className="w-full h-full object-cover"
      onError={(e) => {
        const videoElement = e.target as HTMLVideoElement;
        const error = videoElement.error;
        console.error("Video playback error:", e);
        if (error) {
          console.error(
            `MediaError code: ${error.code}, message: ${error.message}`,
          );
        } else {
          console.error(
            "Video playback error: No MediaError object available on target.",
          );
        }
      }}
    />
  ) : (
    displaySrc ? (
      <img
        src={displaySrc}
        alt=""
        className="w-full h-full object-cover"
        crossOrigin="anonymous"
        onError={(e) => {
          console.error("Image loading error:", e);
          console.error("Failed to load image from URL:", displaySrc);
          // Optionally set a fallback src
          (e.target as HTMLImageElement).src = '';
        }}
        onLoad={() => {
          console.log("Image loaded successfully from URL:", displaySrc);
        }}
      />
    ) : null
  )}
</div>

      <div
        className="absolute inset-x-0 bottom-0 bg-white rounded-t-[16px] shadow-lg p-6 flex flex-col z-10 modal-container" // Added modal-container class
        style={{
          width: "100%",
          minHeight: "200px",
          maxHeight: isFullScreenView ? "none" : "70vh", // Limit to 70vh in preview mode only
          overflowY: isFullScreenView ? "visible" : "auto", // Allow scrolling in preview mode
          // CSS transitions now handled by the injected stylesheet for better performance
        }}
      >
        <div className="w-12 h-1 bg-gray-300 rounded-full mx-auto mb-4 flex-shrink-0" />{" "}
        {/* Added mb-4, flex-shrink-0 */}
        <div className="flex justify-between items-start mb-4 flex-shrink-0">
          {" "}
          {/* Added mb-4, flex-shrink-0 */}
          <div>
            <h2 className="text-sm font-medium text-gray-900">
              {headerConfig?.appName || 'Experience'}
            </h2>
            <p className="text-xs text-gray-500">
              {headerConfig?.appSubtitle || 'Interactive Experience'}
            </p>
          </div>
          <div className="text-right cursor-pointer" onClick={handleScreenAdvance}>
            <p className="text-sm font-medium text-gray-900">
              {isFirstScreen 
                ? (headerConfig?.getStartedButtonText || 'Get Started')
                : `Screen ${currentScreen + 1} of ${totalScreens}`
              }
            </p>
          </div>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-1.5 mb-4 flex-shrink-0">
          {" "}
          {/* Added mb-4, flex-shrink-0 */}
          <div
            className="bg-black h-1.5 rounded-full transition-all duration-300"
            style={{ width: `${((currentScreen + 1) / totalScreens) * 100}%` }}
          />
        </div>
        {/* New scrollable container for buttons and expanded content */}
        <div
          className="flex-grow flex-shrink min-h-0 overflow-y-auto space-y-3 scrollbar-hide"
          style={{ maxHeight: "calc(75vh - 120px)" }}
        >
          {modules.map((m) => {
            if (m.type === "CustomButton") {
              const { text, url, textColor, buttonColor, borderColor } =
                m.content;
              return (
                <a
                  key={m.id}
                  href={url || "#"}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="block w-full text-sm font-semibold py-3 rounded-full transition-colors text-center no-underline"
                  style={{
                    color: textColor,
                    backgroundColor: buttonColor,
                    border: `1px solid ${borderColor}`,
                  }}
                  onClick={(e) => {
                    // Find the modal container and add the expanded class
                    const modalContainer = e.currentTarget.closest(
                      ".absolute.inset-x-0.bottom-0.bg-white.rounded-t-\[16px\].shadow-lg.p-6.flex.flex-col.z-10",
                    );
                    if (modalContainer) {
                      modalContainer.classList.add("expanded");
                    }
                  }}
                >
                  {text}
                </a>
              );
            }

            const content = m.content as typeof m.content &
              Partial<CustomizableButtonStyles>;
            const buttonTextToDisplay =
              content.buttonText ||
              moduleButtonLabel[m.type] ||
              m.title ||
              "Learn More";

            return (
              <React.Fragment key={m.id}>
                <button
                  onClick={() => handleModuleToggle(m.id)}
                  className="block w-full text-sm font-semibold py-3 rounded-full transition-colors text-center no-underline"
                  style={{
                    backgroundColor: content.buttonBackgroundColor || "#000000",
                    color: content.buttonTextColor || "#ffffff",
                    border: `1px solid ${content.buttonBorderColor || content.buttonBackgroundColor || "#000000"}`,
                  }}
                >
                  {buttonTextToDisplay}
                </button>
                {expandedModuleId === m.id && (
                  <div className="mt-2 bg-white rounded-md text-xs relative">
                    {/* Exit/Close button in top-right corner */}
                    <button
                      onClick={() => setExpandedModuleId(null)}
                      className="absolute top-2 right-2 z-20 w-6 h-6 bg-gray-100 hover:bg-gray-200 rounded-full flex items-center justify-center transition-colors"
                      style={{
                        fontSize: '14px',
                        lineHeight: '1',
                        color: '#666',
                      }}
                      aria-label="Close module"
                    >
                      ×
                    </button>
                    {renderModuleContent(m, {
                      backgroundMedia,
                      modules,
                      currentScreen,
                      totalScreens,
                      onScreenChange,
                      isFullScreenView,
                      onUpdateModuleContent,
                    })}
                  </div>
                )}
              </React.Fragment>
            );
          })}
        </div>{" "}
        {/* End of new scrollable container */}
      </div>
    </div>
  );
};
