import { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { getExperience, supabase } from '../utils/supabaseClient';
import { Container, Typography, Box, CircularProgress } from '@mui/material';

// Import mobile preview component
import { MobileScreenOnly } from '../components/phone/MobileScreenOnly';
import RenderErrorBoundary from '../components/common/RenderErrorBoundary';
import type { MediaItem } from '../components/phone/Screen';  // Import MediaItem type
import { resolveMediaUrl } from '../utils/media'; // Import resolveMediaUrl function

// --- Type Definitions copied from components/phone/Screen.tsx --- //

export interface UserDetailsFieldSetting {
  visible: boolean;
  label: string;
  required?: boolean;
}

export interface CustomizableButtonStyles {
  buttonText?: string;
  buttonTextColor?: string;
  buttonBackgroundColor?: string;
  buttonBorderColor?: string;
}

export interface UserDetailsContent extends CustomizableButtonStyles {
  fieldSettings: {
    name: UserDetailsFieldSetting;
    surname: UserDetailsFieldSetting;
    email: UserDetailsFieldSetting;
    phone: UserDetailsFieldSetting;
    message: UserDetailsFieldSetting;
  };
  formData: {
    name: string;
    surname: string;
    email: string;
    phone: string;
    message: string;
  };
}

export interface SetupStep {
  id: string;
  description: string;
}

export interface SetupGuideContent extends CustomizableButtonStyles {
  steps: SetupStep[];
}

export interface WarrantyContent extends CustomizableButtonStyles {
  productName: string;
  productModel?: string;
  serialNumber: string;
  purchaseDate: string;
  warrantyLength: string;
  customerName: string;
  email: string;
  phone?: string;
  retailerName?: string;
  orderNumber?: string;
  receiptFile?: File | null;
  receiptPreviewUrl?: string;
  isTermsAccepted: boolean;
  registrationDate?: string;
  warrantyCode?: string;
  notes?: string;
  isSerialNumberValid?: boolean;
  isSubmitting?: boolean;
  submitError?: string;
}

export interface ShoppingLink {
  id: string;
  url: string;
  displayText: string;
}

export interface ShoppingLinksContent extends CustomizableButtonStyles {
  title?: string;
  links: ShoppingLink[];
  mediaUrl?: string;
  mediaType?: 'pdf' | 'jpg' | 'jpeg' | 'png' | 'svg';
  mediaFileName?: string;
}

export interface SurveyQuestion {
  id: string;
  questionText: string;
}

export interface FeedbackSurveyContent extends CustomizableButtonStyles {
  title?: string;
  questions: SurveyQuestion[];
}

export interface CustomButtonContent {
  text: string;
  url: string;
  textColor: string;
  buttonColor: string;
  borderColor: string;
}

export interface RegistrationModuleContent extends CustomizableButtonStyles {
  collectPurchaseDetails: boolean;
  purchaseFields?: { [key: string]: { required?: boolean } };
  requireReceiptApproval?: boolean;
  disableCompleteProfileScreen?: boolean;
  enableMultipleRegistrations?: boolean;
  formData?: Record<string, any>;
}

export interface FormSurveyModuleContent extends CustomizableButtonStyles {
  surveyDefinitionUrl?: string;
  title: string;
}

export interface DiscountModuleContent extends CustomizableButtonStyles {
  shopifyDiscountCode: string;
  destinationPageUrl: string;
  productInfo?: string;
  discountTerms?: string;
}

export interface IframeModuleContent {
  embedType: 'url' | 'html';
  source: string;
  customAttributes?: string;
}

export interface DocumentModuleContent extends CustomizableButtonStyles {
  documentName: string;
  documentUrl: string;
}

export interface LinkModuleContent extends CustomizableButtonStyles {
  destinationUrl: string;
  linkText?: string;
}

export interface VideoModuleContent {
  sourceType: 'url' | 'embed' | 'upload';
  source: string;
  autoplay?: boolean;
}

export interface ScratchAreaData {
  id: string;
  scratchText: string;
  revealedTextLine1: string;
  revealedTextLine2: string;
  rewardType: 'discount' | 'failure';
  rewardValue: string;
  isRevealed: boolean;
}

export interface ScratchCardModuleContent extends CustomizableButtonStyles {
  headingText: string;
  subTextContent: string;
  initialScratchAreas: ScratchAreaData[];
}

export interface SpinWheelPrize {
  id: string;
  text: string;
  color: string;
  imageSrc?: string;
}

export interface SpinWheelModuleContent extends CustomizableButtonStyles {
  prizes: SpinWheelPrize[];
  wheelBackgroundImageSrc?: string;
  centerPinImageSrc?: string;
}

// The `Module` type from the database has a slightly different structure
// It uses `module_id` and the `content` is a JSON string.
// We'll define a new `RenderableModule` type that matches the structure used in `Screen.tsx`
// after parsing.
export type RenderableModule =
  | { id: string; title: string; type: 'SetupGuide'; content: SetupGuideContent }
  | { id: string; title: string; type: 'Warranty'; content: WarrantyContent }
  | { id: string; title: string; type: 'ShoppingLinks'; content: ShoppingLinksContent }
  | { id: string; title: string; type: 'FeedbackSurvey'; content: FeedbackSurveyContent }
  | { id: string; title: string; type: 'CustomButton'; content: CustomButtonContent }
  | { id: string; title: string; type: 'Registration'; content: RegistrationModuleContent }
  | { id: string; title: string; type: 'FormSurvey'; content: FormSurveyModuleContent }
  | { id: string; title: string; type: 'Discount'; content: DiscountModuleContent }
  | { id: string; title: string; type: 'Iframe'; content: IframeModuleContent }
  | { id: string; title: string; type: 'Document'; content: DocumentModuleContent }
  | { id: string; title: string; type: 'Link'; content: LinkModuleContent }
  | { id: string; title: string; type: 'Video'; content: VideoModuleContent }
  | { id: string; title: string; type: 'ScratchCard'; content: ScratchCardModuleContent }
  | { id: string; title: string; type: 'SpinWheel'; content: SpinWheelModuleContent }
  | { id: string; title: string; type: 'UserDetails'; content: UserDetailsContent };

// This type matches the data coming from Supabase
export interface DbModule {
    module_id: string;
    title: string;
    type: string;
    content: any; // Content can be a stringified JSON or already an object
}

const ExperienceView = () => {
  const { id } = useParams<{ id: string }>();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [experience, setExperience] = useState<{ name: string; modules: DbModule[]; screens?: any[] } | null>(null);
  const [backgroundMedia, setBackgroundMedia] = useState<MediaItem | null>(null);
  const [currentScreen, setCurrentScreen] = useState(0);

  useEffect(() => {
    if (!id) {
      setError("No experience ID provided.");
      setLoading(false);
      return;
    }
    const fetchExperience = async () => {
      try {
        const exp = await getExperience(id);
        if (!exp) throw new Error("Experience data is null.");
        setExperience(exp);
        
        // Extract background media from screens if available
        console.log('Experience data:', JSON.stringify(exp, null, 2)); // Log full experience data
        
        if (exp.screens && exp.screens.length > 0) {
          console.log('Screens data:', JSON.stringify(exp.screens, null, 2)); // Log screens data
          
          const currentScreenData = exp.screens[0]; // Use first screen for now
          console.log('Current screen data:', JSON.stringify(currentScreenData, null, 2)); // Log current screen data
          
          if (currentScreenData.media) {
            console.log('Media data:', JSON.stringify(currentScreenData.media, null, 2)); // Log media data
            
            // Determine media type from mime_type
            const isVideo = currentScreenData.media.mime_type?.startsWith('video/');
            const mediaItem: MediaItem = {
              id: currentScreenData.media.media_id || 'default-media-id',
              type: isVideo ? 'video' : 'image' as 'video' | 'image', // Type assertion to match MediaItem
              url: resolveMediaUrl(currentScreenData.media.path) || ''
            };
            
            console.log('Setting background media:', JSON.stringify(mediaItem, null, 2)); // Log media item
            console.log('Original path:', currentScreenData.media.path);
            console.log('Resolved URL:', resolveMediaUrl(currentScreenData.media.path));
            setBackgroundMedia(mediaItem);
          } else {
            console.log('No media found in current screen data');
          }
        } else {
          console.log('No screens found in experience data');
        }
      } catch (err: any) {
        console.error("Failed to fetch experience:", err);
        setError(err.message || 'Experience not found');
      } finally {
        setLoading(false);
      }
    };
    fetchExperience();
    fetchExperience();

    // Set up Supabase Realtime subscription for experiences table
    const experienceChannel = supabase
      .channel(`experience_${id}`) // Unique channel name for this experience
      .on(
        'postgres_changes',
        { 
          event: 'UPDATE', 
          schema: 'public', 
          table: 'experiences', 
          filter: `experience_id=eq.${id}` 
        },
        (payload) => {
          console.log('Realtime update received for experiences:', payload);
          fetchExperience(); 
        }
      )
      .subscribe();

    // Set up Supabase Realtime subscription for modules table
    const modulesChannel = supabase
      .channel(`modules_${id}`)
      .on(
        'postgres_changes',
        {
          event: '*', // Listen for INSERT, UPDATE, DELETE
          schema: 'public',
          table: 'modules',
          filter: `experience_id=eq.${id}`
        },
        (payload) => {
          console.log('Realtime update received for modules:', payload);
          fetchExperience(); // Re-fetch all data to get latest modules
        }
      )
      .subscribe();

    // Set up Supabase Realtime subscription for screens table
    const screensChannel = supabase
      .channel(`screens_${id}`)
      .on(
        'postgres_changes',
        {
          event: '*', // Listen for INSERT, UPDATE, DELETE
          schema: 'public',
          table: 'screens',
          filter: `experience_id=eq.${id}`
        },
        (payload) => {
          console.log('Realtime update received for screens:', payload);
          fetchExperience(); // Re-fetch all data to get latest screens
        }
      )
      .subscribe();


    // Cleanup function
    return () => {
      supabase.removeChannel(experienceChannel);
      supabase.removeChannel(modulesChannel);
      supabase.removeChannel(screensChannel);
    };

  }, [id]);

  if (loading) {
    return <Container sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}><CircularProgress /></Container>;
  }

  if (error || !experience) {
    return <Container sx={{ textAlign: 'center', mt: 8 }}><Typography variant="h5" color="error">{error || 'An unknown error occurred.'}</Typography></Container>;
  }

  // Convert DB modules to the format expected by MobilePreview
  const convertToRenderableModules = () => {
    if (!experience || !experience.modules) return [];
    
    return experience.modules.map(dbModule => {
      // Content from DB can be an object (if parsed by Supabase) or a string.
      const parsedContent = typeof dbModule.content === 'string'
        ? JSON.parse(dbModule.content)
        : dbModule.content;
      
      // We need to cast this to Module type as defined in the MobilePreview component
      // This is a workaround for TypeScript type checking
      return {
        id: dbModule.module_id,
        title: dbModule.title,
        type: dbModule.type as any, // Type assertion to bypass type checking
        screen: 0, // Default to first screen
        content: parsedContent
      } as any; // Cast to any to bypass type checking
    });
  };

  // Handle screen change in the mobile preview
  const handleScreenChange = (screenIndex: number) => {
    setCurrentScreen(screenIndex);
  };

  // Extract header configuration from experience data with fallbacks
  const headerConfig = {
    appName: (experience as any)?.app_name || (experience as any)?.name || 'Experience',
    appSubtitle: (experience as any)?.app_subtitle || 'Interactive Experience',
    getStartedButtonText: (experience as any)?.get_started_button_text || 'Get Started'
  };

  // If still loading or error, show appropriate message
  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh', p: 3 }}>
        <Typography color="error" variant="h6">{error}</Typography>
      </Box>
    );
  }

  // For the actual experience view, use absolute positioning to fill the entire viewport
  return (
    <Box sx={{ 
      position: 'fixed',
      top: 0,
      left: 0,
      width: '100vw', 
      height: '100vh',
      margin: 0,
      padding: 0,
      overflow: 'hidden',
      backgroundColor: '#fff',
      zIndex: 9999, // Ensure it's above everything else
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    }}>
      <RenderErrorBoundary>
        <MobileScreenOnly
          modules={convertToRenderableModules()}
          backgroundMedia={backgroundMedia}
          currentScreen={currentScreen}
          totalScreens={experience.screens?.length || 1}
          onScreenChange={handleScreenChange}
          headerConfig={headerConfig}
        />
      </RenderErrorBoundary>
    </Box>
  );
};

export default ExperienceView;
