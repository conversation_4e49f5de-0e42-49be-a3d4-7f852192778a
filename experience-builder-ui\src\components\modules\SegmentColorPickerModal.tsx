import React, { useState } from "react";
import { <PERSON>alog, DialogTitle, DialogContent, DialogActions, Box, IconButton, TextField, Button } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { HexColorPicker } from "react-colorful";

interface SegmentColorPickerModalProps {
  open: boolean;
  value: string;
  onChange: (color: string) => void;
  onClose: () => void;
}

const SegmentColorPickerModal: React.FC<SegmentColorPickerModalProps> = ({
  open,
  value,
  onChange,
  onClose,
}) => {
  const isValidHex = (color: string) => /^#[0-9A-F]{6}$/i.test(color);
  const [localColor, setLocalColor] = useState(value);
  const [pickerColor, setPickerColor] = useState(isValidHex(value) ? value : '#000000');

  const handleHexChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newHex = e.target.value.toUpperCase();
    if (/^#[0-9A-F]{0,6}$/.test(newHex)) {
      setLocalColor(newHex);
      if (isValidHex(newHex)) {
        setPickerColor(newHex);
      }
    }
  };

  const handleColorChange = (color: string) => {
    const upperColor = color.toUpperCase();
    setLocalColor(upperColor);
    setPickerColor(upperColor);
  };

  const handleSave = () => {
    if (localColor.length === 7) {
      onChange(localColor);
    }
    onClose();
  };

  const handleCancel = () => {
    onClose();
  };

  const handleClose = (_: object, reason: 'backdropClick' | 'escapeKeyDown') => {
    if (reason !== 'backdropClick') {
        onClose();
    }
};

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="xs" fullWidth>
      <DialogTitle sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', pb: 0 }}>
        Edit Segment Color
        <IconButton size="small" onClick={handleCancel}>
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent>
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 2 }}>
          <HexColorPicker color={pickerColor} onChange={handleColorChange} style={{ width: '100%' }} />
          <TextField
            label="HEX"
            value={localColor}
            onChange={handleHexChange}
            inputProps={{ maxLength: 7 }}
            sx={{ width: '120px' }}
          />
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleCancel}>Cancel</Button>
        <Button onClick={handleSave}>Save</Button>
      </DialogActions>
    </Dialog>
  );
};

export default SegmentColorPickerModal;
