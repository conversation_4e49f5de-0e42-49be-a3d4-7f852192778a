{"name": "experience-builder-ui", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "tsc -b && vite build --debug", "lint": "eslint .", "preview": "vite preview", "analyze": "npx vite-bundle-analyzer"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^5.15.0", "@mui/material": "^5.15.0", "@phosphor-icons/react": "^2.1.10", "@supabase/supabase-js": "^2.50.0", "@tabler/icons-react": "^3.34.0", "@types/lodash": "^4.17.20", "@types/styled-components": "^5.1.34", "clsx": "^2.1.1", "lodash": "^4.17.21", "motion": "^12.23.6", "node-fetch": "^2.7.0", "phosphor-react": "^1.4.1", "qrcode": "^1.5.3", "react": "^19.1.0", "react-colorful": "^5.6.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^19.1.0", "react-easy-crop": "^5.4.2", "react-router-dom": "^6.22.3", "react-window": "^1.8.11", "react-window-infinite-loader": "^1.0.10", "styled-components": "^6.1.19", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/node": "^24.0.10", "@types/qrcode": "^1.5.5", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@types/react-dropzone": "^4.2.2", "@types/react-window": "^1.8.8", "@types/uuid": "^10.0.0", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.19", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "pg": "^8.16.0", "postcss": "^8.4.38", "tailwindcss": "^3.4.4", "tailwindcss-animate": "^1.0.0", "typescript": "^5.5.4", "vite": "^5.4.10", "webpack-bundle-analyzer": "^4.10.2"}}