import React, { useState } from "react";
import { Box, TextField, IconButton, Tooltip } from "@mui/material";
import CheckIcon from "@mui/icons-material/Check";

interface ColorPalettePickerProps {
  value: string;
  onChange: (color: string) => void;
  palette?: string[];
  label?: string;
}

const DEFAULT_PALETTE = [
  "#FF6B35",
  "#F7931E",
  "#FFD23F",
  "#06FFA5",
  "#4D9DE0",
  "#E15FED",
  "#FF0A54",
  "#15616D",
];

const ColorPalettePicker: React.FC<ColorPalettePickerProps> = ({
  value,
  onChange,
  palette = DEFAULT_PALETTE,
  label = "Segment Color",
}) => {
  const [input, setInput] = useState(value);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInput(e.target.value);
  };

  const handleInputBlur = () => {
    if (/^#[0-9A-Fa-f]{6}$/.test(input)) {
      onChange(input);
    } else {
      setInput(value); // revert to valid
    }
  };

  const handleInputSubmit = () => {
    if (/^#[0-9A-Fa-f]{6}$/.test(input)) {
      onChange(input);
    }
  };

  return (
    <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
      <Box sx={{ display: "flex", gap: 0.5 }}>
        {palette.map((color) => (
          <Tooltip key={color} title={color}>
            <Box
              sx={{
                width: 24,
                height: 24,
                borderRadius: "50%",
                backgroundColor: color,
                border: color === value ? "2px solid #333" : "2px solid #eee",
                cursor: "pointer",
                boxShadow: color === value ? "0 0 0 2px #2196f3" : undefined,
                transition: "box-shadow 0.2s",
              }}
              onClick={() => {
                setInput(color);
                onChange(color);
              }}
            />
          </Tooltip>
        ))}
      </Box>
      <TextField
        label={label}
        size="small"
        value={input}
        onChange={handleInputChange}
        onBlur={handleInputBlur}
        sx={{ width: 100 }}
        inputProps={{ maxLength: 7 }}
        placeholder="#FFFFFF"
      />
      <IconButton
        size="small"
        onClick={handleInputSubmit}
        disabled={!/^#[0-9A-Fa-f]{6}$/.test(input)}
        color="primary"
        sx={{ ml: 0.5 }}
      >
        <CheckIcon fontSize="small" />
      </IconButton>
    </Box>
  );
};

export default ColorPalettePicker;
