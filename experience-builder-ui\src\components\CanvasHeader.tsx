import React from 'react';
import { Box } from '@mui/material';
import LaunchSwitch from './ui/LaunchSwitch';

interface CanvasHeaderProps {
  onLaunch: () => Promise<boolean> | boolean;
}

const CanvasHeader: React.FC<CanvasHeaderProps> = ({ onLaunch }) => {
  return (
    <Box sx={{ 
      display: 'flex', 
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'flex-end',
      height: '100%',
      minHeight: '60px',
      marginTop: 'auto'
    }}>
      <LaunchSwitch onLaunch={onLaunch} />
    </Box>
  );
};

export default CanvasHeader;
