import React from 'react';
import { Button, Typography, IconButton } from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';

interface ScreenSelectorProps {
  screens: Array<{
    id: string;
    name: string;
    media?: string;
  }>;
  currentScreenIndex: number;
  onScreenSelect: (index: number) => void;
  onScreenAdd: () => void;
  onScreenDelete: (index: number) => void;

}

export const ScreenSelector: React.FC<ScreenSelectorProps> = ({
  screens,
  currentScreenIndex,
  onScreenSelect,
  onScreenAdd,
  onScreenDelete,
}) => {
  return (
    <div className="space-y-4 p-4">
      <div className="flex justify-between items-center">
        <Typography variant="subtitle2">Screens</Typography>
        <Button
          variant="outlined"
          size="small"
          startIcon={<AddIcon />}
          onClick={onScreenAdd}
        >
          Add Screen
        </Button>
      </div>

      <div className="space-y-2 max-h-96 overflow-y-auto">
        {screens.map((screen, index) => (
          <div
            key={screen.id}
            className={`p-2 border rounded-md cursor-pointer transition-colors ${
              currentScreenIndex === index
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 hover:border-gray-300'
            }`}
            onClick={() => onScreenSelect(index)}
          >
            <div className="flex justify-between items-center">
              <Typography variant="body2">Screen {index + 1}</Typography>
              {screens.length > 1 && (
                <IconButton
                  size="small"
                  onClick={(e) => {
                    e.stopPropagation();
                    onScreenDelete(index);
                  }}
                  className="text-gray-500 hover:text-red-500"
                >
                  <DeleteIcon fontSize="small" />
                </IconButton>
              )}
            </div>
            
            {/* Media preview */}
            {screen.media ? (
              <div className="mt-2 w-full h-20 bg-gray-100 rounded overflow-hidden">
                <img
                  src={screen.media}
                  alt={`Screen ${index + 1} preview`}
                  className="w-full h-full object-cover"
                />
              </div>
            ) : (
              <div className="mt-2 w-full h-20 border-2 border-dashed border-gray-300 rounded flex items-center justify-center">
                <Typography variant="caption" color="textSecondary">
                  No media selected
                </Typography>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};
