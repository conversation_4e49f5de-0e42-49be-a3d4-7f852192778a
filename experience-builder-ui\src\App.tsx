import { useState, useCallback, useMemo } from "react";
import { ThemeProvider } from "@mui/material/styles";
import {
  CssBaseline,
  Paper,
  Typography,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Snackbar,
  Alert,
  Box,
  TextField,
} from "@mui/material";
import "./App.css";
import { modernTheme } from "./theme/modernTheme";
import ModuleItem from "./components/ModuleItem";
import CanvasHeader from "./components/CanvasHeader";
import MobilePreview from "./components/MobilePreview";
import BackgroundSelector, {
  type MediaItem,
} from "./components/BackgroundSelector";
import { saveExperience, supabase } from "./utils/supabaseClient";
import ExperienceBuilderSidebar from "./components/ui/experience-builder-sidebar";
import QrCodeGenerator from "./components/QrCodeGenerator";

import { styled } from "@mui/material/styles";
import { useDrop } from "react-dnd";
import type { ScratchAreaData } from "./components/modules/ScratchCardModule";
// Custom debounce implementation
const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

// Define content structures for modules

export interface HeaderConfig {
  appName: string;
  appSubtitle: string;
  getStartedButtonText: string;
}

export interface CustomizableButtonStyles {
  buttonText?: string;
  buttonTextColor?: string;
  buttonBackgroundColor?: string;
  buttonBorderColor?: string;
}

// User Details Module Content Interface
export interface UserDetailsFieldSetting {
  visible: boolean;
  label: string;
  required?: boolean; // Optional: for future use
}

export interface UserDetailsContent extends CustomizableButtonStyles {
  fieldSettings: {
    name: UserDetailsFieldSetting;
    surname: UserDetailsFieldSetting;
    email: UserDetailsFieldSetting;
    phone: UserDetailsFieldSetting;
    message: UserDetailsFieldSetting;
  };
  formData: {
    name: string;
    surname: string;
    email: string;
    phone: string;
    message: string;
  };
}

// --- Existing Content Interfaces (SetupGuide, Warranty, ShoppingLinks, FeedbackSurvey, CustomButton) ---
export interface SetupStep {
  id: string;
  description: string;
}

export interface SetupGuideContent extends CustomizableButtonStyles {
  steps: SetupStep[];
}

export interface WarrantyContent extends CustomizableButtonStyles {
  // Basic product info
  productName: string;
  productModel?: string;
  serialNumber: string;
  purchaseDate: string;
  warrantyLength: string;

  // Customer info
  customerName: string;
  email: string;
  phone?: string;

  // Purchase info
  retailerName?: string;
  orderNumber?: string;
  receiptFile?: File | null;
  receiptPreviewUrl?: string;

  // Registration details
  isTermsAccepted: boolean;
  registrationDate?: string;
  warrantyCode?: string; // Auto-generated QR code data

  // Additional info
  notes?: string;

  // Validation
  isSerialNumberValid?: boolean;
  isSubmitting?: boolean;
  submitError?: string;
}

export interface ShoppingLink {
  id: string;
  url: string;
  displayText: string;
}

export interface ShoppingLinksContent extends CustomizableButtonStyles {
  title?: string;
  links: ShoppingLink[];
  mediaUrl?: string; // URL of the uploaded media
  mediaType?: "pdf" | "jpg" | "jpeg" | "png" | "svg"; // Type of the media
  mediaFileName?: string; // Original name of the uploaded file
  media?: { url: string; type: string };
}

export interface SurveyQuestion {
  id: string;
  questionText: string;
  questionType: 'text' | 'rating' | 'yesno' | 'csat' | 'nps';
  required?: boolean;
  ratingScale?: {
    min: number;
    max: number;
    labels?: { [key: number]: string };
  };
  options?: string[]; // For multiple choice questions
}

export interface CSATRating {
  score: number;
  label: string;
  emoji?: string;
}

export interface CSATModuleContent extends CustomizableButtonStyles {
  title?: string;
  csatType?: 'feedback' | 'satisfaction' | 'nps' | 'custom';
  questions: SurveyQuestion[];
  enableCSAT?: boolean;
  csatQuestion?: string;
  csatRatings?: CSATRating[];
  enableNPS?: boolean;
  npsQuestion?: string;
  enableStarRating?: boolean;
  starRatingQuestion?: string;
  enableFollowUp?: boolean;
  followUpQuestion?: string;
  thankYouMessage?: string;
}

// Keep for backward compatibility
export interface FeedbackSurveyContent extends CSATModuleContent {}

export interface CustomButtonContent {
  text: string;
  url: string;
  textColor: string;
  buttonColor: string;
  borderColor: string;
}

// --- New Bridg Module Content Interfaces ---
export interface RegistrationModuleContent extends CustomizableButtonStyles {
  // Configuration based on Bridg data for Registration Module
  requirePreSurveyId?: string; // ID of a Form/Survey module if pre-registration survey is required
  collectPurchaseDetails: boolean;
  purchaseFields?: {
    name?: { required?: boolean };
    phone?: { required?: boolean };
    purchaseDate?: { required?: boolean };
    quantity?: { required?: boolean };
    placeOfPurchase?: { required?: boolean };
    serialNumber?: { required?: boolean };
    receiptUpload?: { required?: boolean };
  };
  requireReceiptApproval?: boolean; // Manual or AI (strict, moderate, lenient)
  disableCompleteProfileScreen?: boolean;
  enableMultipleRegistrations?: boolean;
  // Placeholder for actual registration data if stored/managed client-side
  formData?: Record<string, any>;
}

export interface FormSurveyModuleContent extends CustomizableButtonStyles {
  // Primarily used as a target for RegistrationModule, might store survey definition or link
  surveyDefinitionUrl?: string; // Link to an external survey tool or internal definition
  title: string;
}

export interface DiscountModuleContent extends CustomizableButtonStyles {
  shopifyDiscountCode: string;
  destinationPageUrl: string;
  productInfo?: string; // Detailed text about product/discount
  discountTerms?: string; // Editable terms
  // Note: Access requires registration - this is a behavior, not a stored field here
}

export interface IframeModuleContent {
  embedType: "url" | "html";
  source: string; // URL or HTML string
  customAttributes?: string; // e.g., 'frameborder="0" allowfullscreen'
}

export interface DocumentModuleContent extends CustomizableButtonStyles {
  documentName: string;
  documentUrl: string; // URL to the PDF or other document
  // Actual file upload might be handled separately, storing only the URL here
}

export interface LinkModuleContent extends CustomizableButtonStyles {
  destinationUrl: string;
  linkText?: string; // Optional text for the link/button in the module view
}

export interface VideoModuleContent {
  sourceType: "url" | "embed" | "upload";
  source: string; // URL, embed code, or path/ID to uploaded video
  autoplay?: boolean;
}

export interface ScratchCardModuleContent extends CustomizableButtonStyles {
  headingText: string;
  subTextContent: string;
  whatsappNumber: string;
  countryCode?: string;  // Added to fix type error
  initialScratchAreas: ScratchAreaData[];
  // Optional callbacks can be omitted here if not managed at App level
}

export interface SpinWheelPrize {
  id: string;
  text: string;
  color: string; // Hex color for the segment
  imageSrc?: string; // Optional: If prizes can be images on the segments
}

export interface SpinWheelModuleContent extends CustomizableButtonStyles {
  headingText: string;
  subTextContent: string;
  prizes: SpinWheelPrize[];
  wheelBackgroundImageSrc?: string; // Optional: For the main wheel background
  centerPinImageSrc?: string; // Optional: For an image at the center/pin of the wheel
  whatsappNumber?: string;
  countryCode?: string;
}

// Define the structure of a module using a discriminated union for content
export type Module =
  | {
      id: string;
      screen?: number;
      title: string;
      type: "SetupGuide";
      content: SetupGuideContent;
    }
  | {
      id: string;
      screen?: number;
      title: string;
      type: "Warranty";
      content: WarrantyContent;
    }
  | {
      id: string;
      screen?: number;
      title: string;
      type: "ShoppingLinks";
      content: ShoppingLinksContent;
    }
  | {
      id: string;
      screen?: number;
      title: string;
      type: "FeedbackSurvey";
      content: FeedbackSurveyContent;
    }
  | {
      id: string;
      screen?: number;
      title: string;
      type: "CustomButton";
      content: CustomButtonContent;
    }
  | {
      id: string;
      screen?: number;
      title: string;
      type: "Registration";
      content: RegistrationModuleContent;
    }
  | {
      id: string;
      screen?: number;
      title: string;
      type: "FormSurvey";
      content: FormSurveyModuleContent;
    }
  | {
      id: string;
      screen?: number;
      title: string;
      type: "Discount";
      content: DiscountModuleContent;
    }
  | {
      id: string;
      screen?: number;
      title: string;
      type: "Iframe";
      content: IframeModuleContent;
    }
  | {
      id: string;
      screen?: number;
      title: string;
      type: "Document";
      content: DocumentModuleContent;
    }
  | {
      id: string;
      screen?: number;
      title: string;
      type: "Link";
      content: LinkModuleContent;
    }
  | {
      id: string;
      screen?: number;
      title: string;
      type: "Video";
      content: VideoModuleContent;
    }
  | {
      id: string;
      screen?: number;
      title: string;
      type: "ScratchCard";
      content: ScratchCardModuleContent;
    }
  | {
      id: string;
      screen?: number;
      title: string;
      type: "SpinWheel";
      content: SpinWheelModuleContent;
    }
  | {
      id: string;
      screen?: number;
      title: string;
      type: "UserDetails";
      content: UserDetailsContent;
    };

const MainContent = styled("main")(() => ({
  flex: 1,
  minWidth: 0,
  padding: "16px",
  maxWidth: "800px",
  height: "100vh",
  overflowY: "auto",
  display: "flex",
  flexDirection: "column",
  // COMPLETELY hide scrollbar and prevent ANY layout shifts
  scrollbarWidth: "none", // Firefox
  msOverflowStyle: "none", // IE and Edge
  "&::-webkit-scrollbar": {
    display: "none", // Chrome, Safari, Opera
    width: "0px",
    background: "transparent",
  },
  "&::-webkit-scrollbar-thumb": {
    background: "transparent",
  },
  // Force consistent width calculation
  width: "calc(100% - 32px)", // Account for padding
  boxSizing: "border-box",
}));

const PreviewSidebar = styled("aside")(() => ({
  width: "450px",
  flexShrink: 0,
  backgroundColor: "transparent",
  padding: "8px",
  height: "100vh",
  position: "sticky",
  top: "0",
  display: "flex",
  flexDirection: "column",
  overflowY: "auto",
}));

type ExperienceType = "Registration" | "Rebates" | "Sweepstakes";

const moduleButtonLabel: Record<string, string> = {
  ScratchCard: "SCRATCH TO WIN!",
  SpinWheel: "SPIN THE WHEEL!",
  Warranty: "ACTIVATE WARRANTY",
  "Setup Guide": "QUICK START GUIDE",
  Registration: "REGISTER PRODUCT",
  FeedbackSurvey: "FEEDBACK",
  ShoppingLinks: "SHOP ACCESSORIES",
  UserDetails: "SUBMIT DETAILS",
  // CustomButton is handled separately as it already has these fields
  // Document and Link modules might have specific link text, handled below
};

function App() {
  const [experienceName, setExperienceName] = useState("WebApp Experience");
  const [experienceType, setExperienceType] =
    useState<ExperienceType>("Registration");
  const [addedModules, setAddedModules] = useState<Module[]>([]);
  const [currentScreen, setCurrentScreen] = useState(0);
  const [totalScreens, setTotalScreens] = useState(1);
  const [selectedMedia, setSelectedMedia] = useState<MediaItem | null>(null);
  const [experienceId, setExperienceId] = useState<string | null>(null);
  const [submitDialogOpen, setSubmitDialogOpen] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [qrModalOpen, setQrModalOpen] = useState(false);
  const [headerConfig, setHeaderConfig] = useState<HeaderConfig>({
  appName: "",
  appSubtitle: "",
  getStartedButtonText: "Get Started",
});


  const debouncedSetExperienceName = useMemo(
    () => debounce((value: string) => setExperienceName(value), 300),
    []
  );

  const handleUpdateModuleContentInScreen = useCallback(
    (moduleId: string, updatedContentPart: Partial<Module["content"]>) => {
      setAddedModules((prevModules) =>
        prevModules.map((module) => {
          if (module.id === moduleId && module.type === "UserDetails") {
            const currentModuleContent = module.content as UserDetailsContent;
            const updateForUserDetails =
              updatedContentPart as Partial<UserDetailsContent>;

            const newFormData = updateForUserDetails.formData
              ? {
                  ...currentModuleContent.formData,
                  ...updateForUserDetails.formData,
                }
              : currentModuleContent.formData;

            const newContent: UserDetailsContent = {
              ...currentModuleContent,
              ...updateForUserDetails,
              formData: newFormData,
            };
            return { ...module, content: newContent } as Module;
          }
          return module as Module;
        }),
      );
    },
    [setAddedModules],
  );
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState("");
  const [snackbarSeverity, setSnackbarSeverity] = useState<
    "success" | "error" | "info"
  >("info");

  const addModuleToCanvas = useCallback((moduleType: Module["type"], title: string) => {
    let newModule: Module;
    const baseProps = {
      id: `${moduleType}-${Date.now()}`,
      title,
      screen: currentScreen,
    };

    switch (moduleType) {
      case "SetupGuide":
        newModule = {
          ...baseProps,
          type: moduleType,
          content: { steps: [], buttonText: moduleButtonLabel[moduleType] },
        };
        break;
      case "Warranty":
        newModule = {
          ...baseProps,
          type: moduleType,
          content: {
            productName: "",
            serialNumber: "",
            purchaseDate: new Date().toISOString().split("T")[0],
            warrantyLength: "1 year",
            customerName: "",
            email: "",
            isTermsAccepted: false,
            buttonText: moduleButtonLabel[moduleType],
            // Default other optional fields as needed
          },
        };
        break;
      case "ShoppingLinks":
        newModule = {
          ...baseProps,
          type: moduleType,
          content: {
            links: [],
            mediaUrl: undefined,
            mediaType: undefined,
            mediaFileName: undefined,
            buttonText: moduleButtonLabel[moduleType],
          },
        };
        break;
      case "FeedbackSurvey":
        newModule = {
          ...baseProps,
          type: moduleType,
          content: {
            title: "Customer Satisfaction Survey",
            csatType: "feedback" as const,
            questions: [],
            buttonText: "FEEDBACK",
            enableCSAT: true,
            csatQuestion: "How satisfied are you with our service?",
          },
        };
        break;
      case "CustomButton":
        newModule = {
          ...baseProps,
          type: moduleType,
          content: {
            text: "Click Me",
            url: "#",
            textColor: "#FFFFFF",
            buttonColor: "#1976d2",
            borderColor: "#1976d2",
          },
        };
        break;
      case "Registration":
        newModule = {
          ...baseProps,
          type: moduleType,
          content: {
            collectPurchaseDetails: false,
            buttonText: moduleButtonLabel[moduleType],
          },
        };
        break;
      case "FormSurvey":
        newModule = {
          ...baseProps,
          type: moduleType,
          content: {
            title: "Survey",
            buttonText: "SUBMIT SURVEY" /* Or some other default */,
          },
        };
        break;
      case "Discount":
        newModule = {
          ...baseProps,
          type: moduleType,
          content: {
            shopifyDiscountCode: "",
            destinationPageUrl: "",
            buttonText: "GET DISCOUNT",
          },
        };
        break;
      case "Iframe":
        newModule = {
          ...baseProps,
          type: moduleType,
          content: { embedType: "url", source: "" },
        };
        break;
      case "Document":
        newModule = {
          ...baseProps,
          type: moduleType,
          content: {
            documentName: "",
            documentUrl: "",
            buttonText: "VIEW DOCUMENT",
          },
        };
        break;
      case "Link":
        newModule = {
          ...baseProps,
          type: moduleType,
          content: { destinationUrl: "", buttonText: "LEARN MORE" },
        };
        break;
      case "Video":
        newModule = {
          ...baseProps,
          type: moduleType,
          content: { sourceType: "url", source: "" },
        };
        break;
      case "ScratchCard":
        newModule = {
          ...baseProps,
          type: moduleType,
          content: {
            headingText: "🎯 Scratch & Win!",
            subTextContent:
              "Scratch the circles to reveal your discount!\nScratch enough area to see if you won. First winner ends the game!",
            
            initialScratchAreas: [
              {
                id: "app-init-1",
                scratchText: "SCRATCH",
                revealedTextLine1: "10% OFF",
                revealedTextLine2: "Congrats!",
                rewardType: "discount",
                rewardValue: "10%",
                isRevealed: false,
              },
              {
                id: "app-init-2",
                scratchText: "SCRATCH",
                revealedTextLine1: "TRY AGAIN",
                revealedTextLine2: "Better luck next time!",
                rewardType: "failure",
                rewardValue: "0%",
                isRevealed: false,
              },
              {
                id: "app-init-3",
                scratchText: "SCRATCH",
                revealedTextLine1: "20% OFF",
                revealedTextLine2: "You Won!",
                rewardType: "discount",
                rewardValue: "20%",
                isRevealed: false,
              },
              {
                id: "app-init-4",
                scratchText: "SCRATCH",
                revealedTextLine1: "15% OFF",
                revealedTextLine2: "Lucky!",
                rewardType: "discount",
                rewardValue: "15%",
                isRevealed: false,
              },
            ],
            buttonText: moduleButtonLabel[moduleType],
            whatsappNumber: "+1234567890",
            countryCode: "US",
          },
        };
        break;
      case "UserDetails":
        newModule = {
          ...baseProps,
          type: moduleType,
          content: {
            fieldSettings: {
              name: { visible: true, label: "Name", required: true },
              surname: { visible: true, label: "Surname", required: true },
              email: { visible: true, label: "Email", required: true },
              phone: {
                visible: true,
                label: "Phone (Optional)",
                required: false,
              },
              message: {
                visible: true,
                label: "Message (Optional)",
                required: false,
              },
            },
            formData: {
              name: "",
              surname: "",
              email: "",
              phone: "",
              message: "",
            },
            buttonText: moduleButtonLabel[moduleType] || "Submit",
          },
        };
        break;
      case "SpinWheel":
        newModule = {
          ...baseProps,
          type: moduleType,
          content: {
            headingText: "Spin to Win!",
            subTextContent: "Spin the wheel for a chance to win prizes!",
            prizes: [
              { id: "default-1", text: "Prize 1", color: "#F7931E" },
              { id: "default-2", text: "Prize 2", color: "#FFD23F" },
              { id: "default-3", text: "Prize 3", color: "#06FFA5" },
              { id: "default-4", text: "Prize 4", color: "#4D9DE0" },
              { id: "default-5", text: "Prize 5", color: "#E15FED" },
              { id: "default-6", text: "Prize 6", color: "#FF0A54" },
              { id: "default-7", text: "Prize 7", color: "#15616D" },
              { id: "default-8", text: "Prize 8", color: "#FF6B35" },
              { id: "default-9", text: "Try Again", color: "#E0E0E0" },
              { id: "default-10", text: "15% OFF", color: "#2196F3" },
              { id: "default-11", text: "Try Again", color: "#E0E0E0" },
              { id: "default-12", text: "Jackpot!", color: "#9C27B0" },
            ],
          },
        };
        break;
      default:
        // This should not happen if moduleType is correctly typed
        console.error("Unknown module type:", moduleType);
        return;
    }
    setAddedModules((prevModules) => [...prevModules, newModule]);
  }, [currentScreen]);

  const removeModuleFromCanvas = useCallback(
    (moduleId: string) => {
      setAddedModules((prev) => prev.filter((m) => m.id !== moduleId));
    },
    [setAddedModules],
  );

  const updateModuleContent = useCallback(
    (moduleId: string, newContent: Module["content"]) => {
      console.log("[App] updateModuleContent called:", {
        moduleId,
        newContent,
      });
      setAddedModules((prevModules) =>
        prevModules.map((mod) => {
          if (mod.id === moduleId) {
            // This switch ensures that newContent is assigned to the correct content type
            // It relies on the calling component (ModuleItem) to provide newContent that
            // matches the mod.type.
            switch (mod.type) {
              case "SetupGuide":
                return { ...mod, content: newContent as SetupGuideContent };
              case "Warranty":
                return { ...mod, content: newContent as WarrantyContent };
              case "ShoppingLinks":
                return { ...mod, content: newContent as ShoppingLinksContent };
              case "FeedbackSurvey":
                return { ...mod, content: newContent as FeedbackSurveyContent };
              case "CustomButton":
                return { ...mod, content: newContent as CustomButtonContent };
              case "Registration":
                return {
                  ...mod,
                  content: newContent as RegistrationModuleContent,
                };
              case "FormSurvey":
                return {
                  ...mod,
                  content: newContent as FormSurveyModuleContent,
                };
              case "Discount":
                return { ...mod, content: newContent as DiscountModuleContent };
              case "Iframe":
                return { ...mod, content: newContent as IframeModuleContent };
              case "Document":
                return { ...mod, content: newContent as DocumentModuleContent };
              case "Link":
                return { ...mod, content: newContent as LinkModuleContent };
              case "Video":
                return { ...mod, content: newContent as VideoModuleContent };
              case "ScratchCard":
                return {
                  ...mod,
                  content: newContent as ScratchCardModuleContent,
                };
              case "SpinWheel":
                return {
                  ...mod,
                  content: newContent as SpinWheelModuleContent,
                };
              case "UserDetails":
                return { ...mod, content: newContent as UserDetailsContent };
              default:
                // Should not happen with exhaustive check, but good for safety
                return mod;
            }
          }
          return mod;
        }),
      );
    },
    [],
  );

  // Example of how specific updaters might look if needed for complex logic,
  // but for simple content replacement, the generic one is fine.
  // If a specific updater is needed, it would look like:
  // const updateSpecificModuleTypeContent = (moduleId: string, partialNewContent: Partial<SpecificModuleContent>) => {
  //   setAddedModules(prevModules =>
  //     prevModules.map(module => {
  //       if (module.id === moduleId && module.type === 'SpecificType') {
  //         return { ...module, content: { ...module.content, ...partialNewContent } };
  //       }
  //       return module;
  //     })
  //   );
  // };

  const moveModule = (dragIndex: number, hoverIndex: number) => {
    setAddedModules((prev) => {
      const updated = [...prev];
      const [removed] = updated.splice(dragIndex, 1);
      updated.splice(hoverIndex, 0, removed);
      return updated;
    });
  };

  const handleExperienceTypeChange = (
    _: any,
    newType: ExperienceType | null,
  ) => {
    if (newType) {
      setExperienceType(newType);
    }
  };

  // Handle experience submission process
  const handleSubmitExperience = async () => {
    if (!experienceName.trim()) {
      setSnackbarMessage("Please enter a name for your experience.");
      setSnackbarSeverity("error");
      setSnackbarOpen(true);
      return;
    }

    if (addedModules.length === 0) {
      setSnackbarMessage("Please add at least one module to your experience");
      setSnackbarSeverity("error");
      setSnackbarOpen(true);
      return;
    }

    setSubmitDialogOpen(false);
    setSubmitting(true);

    try {
      // Initial save of the experience and its modules
      const savedExperience = await saveExperience(
        experienceName,
        selectedMedia?.id ?? null,
        addedModules,
        headerConfig,
      );

      // Persist the background media for the first screen if one was chosen
      if (selectedMedia?.id) {
        await supabase
          .from("screens" as any)
          .update({ background_media_id: selectedMedia.id })
          .eq("experience_id", savedExperience.experience_id)
          .eq("screen_index", 0);
      }

      // Update UI state
      setExperienceId(savedExperience.experience_id);
      setSnackbarMessage("Experience saved successfully!");
      setSnackbarSeverity("success");
      setSnackbarOpen(true);
      // Show QR code modal upon successful launch
      setQrModalOpen(true);
    } catch (error) {
      console.error("Error submitting experience:", error);
      setSnackbarMessage("Failed to save experience");
      setSnackbarSeverity("error");
      setSnackbarOpen(true);
    } finally {
      setSubmitting(false);
    }
  };

  const handleCloseSnackbar = () => {
    setSnackbarOpen(false);
  };

  // Memoize filtered modules for current screen
  const currentScreenModules = useMemo(() => 
    addedModules.filter((m) => (m.screen ?? 0) === currentScreen),
    [addedModules, currentScreen]
  );

  const renderModule = (module: Module, index: number) => {
    return (
      <ModuleItem
        key={module.id}
        module={module}
        onUpdateContent={(moduleId, newContent) =>
          updateModuleContent(moduleId, newContent)
        }
        onRemoveModule={removeModuleFromCanvas}
        index={index}
        onMoveModule={moveModule}
      />
    );
  };

  // Drop target to add modules via drag from palette
  const [{ isOver, canDrop }, drop] = useDrop<
    { moduleType: string; title: string },
    void,
    { isOver: boolean; canDrop: boolean }
  >({
    accept: "AVAILABLE_MODULE",
    drop: (item) => {
      addModuleToCanvas(item.moduleType as Module["type"], item.title);
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
      canDrop: monitor.canDrop(),
    }),
  });

  const dropTarget = useCallback(
    (node: HTMLDivElement | null) => {
      if (node) drop(node);
    },
    [drop],
  );

  return (
    <ThemeProvider theme={modernTheme}>
      <CssBaseline />
      <ExperienceBuilderSidebar
        experienceType={experienceType}
        onExperienceTypeChange={handleExperienceTypeChange}
        addModule={(moduleType: string, title: string) =>
          addModuleToCanvas(moduleType as Module["type"], title)
        }
        experienceId={experienceId}
        experienceName={experienceName}
      >
        <MainContent>
          <Paper
            elevation={1}
            sx={{
              p: 2, // Better padding
              margin: "8px 0",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              minHeight: "80px", // Natural height with proper spacing
              borderRadius: "12px",
              background: "linear-gradient(145deg, rgba(230, 230, 230, 1) 0%, rgba(255, 255, 255, 1) 100%)",
              border: "none",
              boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1)",
              // PREVENT RESIZING WITHOUT CRAMPING
              flexShrink: 0,
              width: "100%",
              boxSizing: "border-box",
              position: "relative", // Isolate from layout changes below
            }}
          >
            <Box sx={{ flex: "1", mr: 2, minWidth: 0, overflow: 'hidden', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>

              <Typography
                variant="h6"
                sx={{
                  fontWeight: 600,
                  mb: 1,
                  fontSize: "16px",
                  paddingLeft: "2px",
                  color: "white",
                }}
              >
                <span style={{ color: 'black' }}>Experience Name</span>
              </Typography>
              <TextField
                variant="outlined"
                defaultValue={experienceName}
                onChange={(e) => debouncedSetExperienceName(e.target.value)}
                placeholder="Enter experience name"
                fullWidth
                size="small"
                sx={{
                  width: "100%",
                  maxWidth: "100%",
                  minWidth: 0,
                  flex: "1 1 auto",
                  "& .MuiOutlinedInput-root": {
                    borderRadius: "8px",
                    backgroundColor: "#fafbfc",
                    width: "100%",
                    maxWidth: "100%",
                    minWidth: 0,
                    "& input": {
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                      whiteSpace: "nowrap",
                      width: "100%",
                      minWidth: 0,
                    },
                    "& fieldset": {
                      borderColor: "#e2e8f0",
                    },
                    "&:hover fieldset": {
                      borderColor: "#cbd5e1",
                    },
                    "&.Mui-focused fieldset": {
                      borderColor: "#6366f1",
                      borderWidth: "2px",
                    },
                  },
                }}
              />
            </Box>
            <CanvasHeader 
              onLaunch={async () => {
                if (!experienceName.trim()) {
                  setSnackbarMessage("Please enter a name for your experience.");
                  setSnackbarSeverity("error");
                  setSnackbarOpen(true);
                  return false;
                }

                if (addedModules.length === 0) {
                  setSnackbarMessage("Please add at least one module to your experience");
                  setSnackbarSeverity("error");
                  setSnackbarOpen(true);
                  return false;
                }

                try {
                  // Initial save of the experience and its modules
                  const savedExperience = await saveExperience(
                    experienceName,
                    selectedMedia?.id ?? null,
                    addedModules,
                    headerConfig,
                  );

                  // Persist the background media for the first screen if one was chosen
                  if (selectedMedia?.id) {
                    await supabase
                      .from("screens" as any)
                      .update({ background_media_id: selectedMedia.id })
                      .eq("experience_id", savedExperience.experience_id)
                      .eq("screen_index", 0);
                  }

                  // Update UI state
                  setExperienceId(savedExperience.experience_id);
                  setSnackbarMessage("Experience saved successfully!");
                  setSnackbarSeverity("success");
                  setSnackbarOpen(true);
                  // Show QR code modal upon successful launch
                  setQrModalOpen(true);
                  return true; // QR code generation successful
                } catch (error) {
                  console.error("Error submitting experience:", error);
                  setSnackbarMessage("Failed to save experience");
                  setSnackbarSeverity("error");
                  setSnackbarOpen(true);
                  return false; // QR code generation failed
                }
              }}
            />
          </Paper>
          <Paper
            elevation={1}
            sx={{
              p: 2,
              my: 1,
              borderRadius: "12px",
              background: "linear-gradient(145deg, rgba(230, 230, 230, 1) 0%, rgba(255, 255, 255, 1) 100%)",
              border: "none",
              boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1)",
            }}
          >
            <Typography
              variant="h6"
              sx={{ fontWeight: 600, mb: 1, fontSize: "16px", color: "white" }}
            >
              <span style={{ color: 'black' }}>Background & Screens</span>
            </Typography>
            <BackgroundSelector
              selectedMedia={selectedMedia}
              onSelectMedia={setSelectedMedia}
              onScreenChange={(idx: number, total: number) => {
                setCurrentScreen(idx);
                setTotalScreens(total);
              }}
              headerConfig={headerConfig}
              onHeaderConfigChange={setHeaderConfig}
            />
          </Paper>
          <div ref={dropTarget} style={{ flexGrow: 1 }}>
            <Paper
              elevation={1}
              sx={{
                p: 2,
                my: 1,
                display: "flex",
                flexDirection: "column",
                border: isOver && canDrop ? "2px dashed #ffffff" : "none",
                background: "linear-gradient(145deg, rgba(230, 230, 230, 1) 0%, rgba(255, 255, 255, 1) 100%)",
                borderRadius: "12px",
                minHeight: "200px",
                boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1)",
              }}
            >
              <Typography
                variant="h6"
                sx={{ fontWeight: 600, mb: 1, fontSize: "16px", color: "white" }}
              >
                <span style={{ color: 'black' }}>Added Modules</span>
              </Typography>
              {addedModules.length === 0 ? (
                <Box
                  sx={{
                    textAlign: "center",
                    py: 2,
                    color: "white",
                  }}
                >
                  <Typography variant="body2" sx={{ fontSize: "14px", color: "white" }}>
                    Drag modules here or click to add
                  </Typography>
                </Box>
              ) : (
                addedModules.map((module, idx) => renderModule(module, idx))
              )}
            </Paper>
          </div>
        </MainContent>
        <PreviewSidebar>
            <MobilePreview
              experienceName={experienceName}
              modules={currentScreenModules}
              backgroundMedia={selectedMedia}
              currentScreen={currentScreen}
              totalScreens={totalScreens}
              onScreenChange={(idx) => setCurrentScreen(idx)}
              onUpdateModuleContent={handleUpdateModuleContentInScreen}
              headerConfig={headerConfig}
            />
          </PreviewSidebar>
      </ExperienceBuilderSidebar>

      {/* Experience submission dialog */}
      <Dialog
        open={submitDialogOpen}
        onClose={() => setSubmitDialogOpen(false)}
        aria-labelledby="submit-experience-dialog-title"
      >
        <DialogTitle id="submit-experience-dialog-title">
          Submit Experience
        </DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to submit this experience? This will generate
            a QR code that users can scan to access your experience.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSubmitDialogOpen(false)} color="primary">
            Cancel
          </Button>
          <Button
            onClick={handleSubmitExperience}
            color="primary"
            variant="contained"
            disabled={submitting}
          >
            {submitting ? "Submitting..." : "Submit"}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbarSeverity}
          sx={{ width: "100%" }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>

      {/* QR Code Modal */}
      <Dialog
        open={qrModalOpen}
        onClose={() => setQrModalOpen(false)}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: "16px",
            padding: "16px",
            background: "radial-gradient(circle, rgba(255, 255, 255, 1) 0%, rgba(222, 222, 222, 1) 50%, rgba(255, 255, 255, 1) 100%)",
            border: "none",
          },
        }}
      >
        <DialogTitle
          sx={{
            textAlign: "center",
            fontSize: "24px",
            fontWeight: 600,
            color: "white",
            pb: 1,
          }}
        >
          🎉 Experience Launched!
        </DialogTitle>
        <DialogContent sx={{ textAlign: "center", pt: 2 }}>
          <Typography
            variant="body1"
            sx={{ mb: 3, color: "white", fontSize: "16px" }}
          >
            Your experience is ready! Scan the QR code below or share the URL.
          </Typography>
          
          {experienceId && (
            <Box sx={{ display: "flex", flexDirection: "column", alignItems: "center", gap: 2 }}>
              {/* QR Code Display */}
              <Paper
                elevation={2}
                sx={{
                  p: 3,
                  borderRadius: "12px",
                  backgroundColor: "#fafafa",
                  border: "1px solid #e0e0e0",
                }}
              >
                <QrCodeGenerator
                  experienceId={experienceId}
                  experienceName={experienceName}
                />
              </Paper>
            </Box>
          )}
        </DialogContent>
        <DialogActions sx={{ justifyContent: "center", pt: 3, pb: 2 }}>
          <Button
            onClick={() => setQrModalOpen(false)}
            variant="contained"
            sx={{
              backgroundColor: "white",
              color: "#6366F1",
              "&:hover": {
                backgroundColor: "#f0f0ff",
                color: "#4F46E5",
              },
              borderRadius: "8px",
              textTransform: "none",
              fontWeight: 600,
              px: 4,
              py: 1.5,
              boxShadow: "0 2px 4px rgba(0, 0, 0, 0.1)",
            }}
          >
            Got it!
          </Button>
        </DialogActions>
      </Dialog>
    </ThemeProvider>
  );
}

export default App;
