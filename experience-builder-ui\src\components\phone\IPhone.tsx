import React from "react";
import { SideButtons } from "./SideButtons";
import { PhoneFrame } from "./PhoneFrame";
import { ScreenBorder } from "./ScreenBorder";
import { DynamicIsland } from "./DynamicIsland";
import { StatusBar } from "./StatusBar";
import { Screen, type MediaItem, type Module } from "./Screen";

interface IPhoneProps {
  modules?: Module[];
  backgroundMedia?: MediaItem | null;
  currentScreen: number;
  totalScreens: number;
  onScreenChange: (idx: number) => void;
  onUpdateModuleContent?: (
    moduleId: string,
    updatedContentPart: Partial<Module["content"]>,
  ) => void;
}

export const IPhone: React.FC<IPhoneProps> = ({
  modules = [],
  backgroundMedia,
  currentScreen,
  totalScreens,
  onScreenChange,
  onUpdateModuleContent,
}) => {
  return (
    <>
      <link
        href="https://fonts.googleapis.com/css2?family=SF+Pro+Text:wght@400;600&display=swap"
        rel="stylesheet"
      />
      <article
        className="w-[409px] h-[843px] relative max-w-[100vw] max-h-screen mx-auto my-0 max-md:w-full max-md:h-auto max-md:max-w-[409px] max-md:origin-[top_center] max-md:scale-[0.8] max-sm:mt-[-50px] max-sm:scale-[0.6]"
        style={{
          transform: "scale(0.85)",
          transition: "transform 0.15s cubic-bezier(0.4, 0, 0.2, 1)",
          filter: "drop-shadow(0 25px 50px rgba(0, 0, 0, 0.15))",
        }}
      >
        <SideButtons />
        <PhoneFrame />
        <ScreenBorder />
        {/* Filter modules for current screen */}
        <Screen
          modules={modules.filter((m) => (m.screen ?? 0) === currentScreen)}
          backgroundMedia={backgroundMedia}
          currentScreen={currentScreen}
          totalScreens={totalScreens}
          onScreenChange={onScreenChange}
          onUpdateModuleContent={onUpdateModuleContent}
        />
        <DynamicIsland />
        <StatusBar />
      </article>
    </>
  );
};
