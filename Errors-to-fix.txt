[plugin:vite:react-babel] C:\Users\<USER>\OneDrive\Documents\AI\Windsurf\Web-app-experience\experience-builder-ui\src\components\ui\experience-builder-sidebar.tsx: Unexpected token (177:28)
  180 |                         </div>
C:/Users/<USER>/OneDrive/Documents/AI/Windsurf/Web-app-experience/experience-builder-ui/src/components/ui/experience-builder-sidebar.tsx:177:28
175|                              "font-semibold text-lg transition-colors duration-200",
176|                              isActive ? "text-white" : "text-black"
177|                            )>)}>
   |                              ^
178|                              {link.label}
179|                            </span>
    at constructor (C:\Users\<USER>\OneDrive\Documents\AI\Windsurf\Web-app-experience\experience-builder-ui\node_modules\@babel\parser\lib\index.js:363:19)
    at TypeScriptParserMixin.raise (C:\Users\<USER>\OneDrive\Documents\AI\Windsurf\Web-app-experience\experience-builder-ui\node_modules\@babel\parser\lib\index.js:6609:19)
    at TypeScriptParserMixin.unexpected (C:\Users\<USER>\OneDrive\Documents\AI\Windsurf\Web-app-experience\experience-builder-ui\node_modules\@babel\parser\lib\index.js:6629:16)
    at TypeScriptParserMixin.parseExprAtom (C:\Users\<USER>\OneDrive\Documents\AI\Windsurf\Web-app-experience\experience-builder-ui\node_modules\@babel\parser\lib\index.js:11378:16)
    at TypeScriptParserMixin.parseExprAtom (C:\Users\<USER>\OneDrive\Documents\AI\Windsurf\Web-app-experience\experience-builder-ui\node_modules\@babel\parser\lib\index.js:4776:20)
    at TypeScriptParserMixin.parseExprSubscripts (C:\Users\<USER>\OneDrive\Documents\AI\Windsurf\Web-app-experience\experience-builder-ui\node_modules\@babel\parser\lib\index.js:11012:23)
    at TypeScriptParserMixin.parseUpdate (C:\Users\<USER>\OneDrive\Documents\AI\Windsurf\Web-app-experience\experience-builder-ui\node_modules\@babel\parser\lib\index.js:10997:21)
    at TypeScriptParserMixin.parseMaybeUnary (C:\Users\<USER>\OneDrive\Documents\AI\Windsurf\Web-app-experience\experience-builder-ui\node_modules\@babel\parser\lib\index.js:10977:23)
    at TypeScriptParserMixin.parseMaybeUnary (C:\Users\<USER>\OneDrive\Documents\AI\Windsurf\Web-app-experience\experience-builder-ui\node_modules\@babel\parser\lib\index.js:9794:18)
    at TypeScriptParserMixin.parseMaybeUnaryOrPrivate (C:\Users\<USER>\OneDrive\Documents\AI\Windsurf\Web-app-experience\experience-builder-ui\node_modules\@babel\parser\lib\index.js:10830:61)
    at TypeScriptParserMixin.parseExprOpBaseRightExpr (C:\Users\<USER>\OneDrive\Documents\AI\Windsurf\Web-app-experience\experience-builder-ui\node_modules\@babel\parser\lib\index.js:10917:34)
    at TypeScriptParserMixin.parseExprOpRightExpr (C:\Users\<USER>\OneDrive\Documents\AI\Windsurf\Web-app-experience\experience-builder-ui\node_modules\@babel\parser\lib\index.js:10912:21)
    at TypeScriptParserMixin.parseExprOp (C:\Users\<USER>\OneDrive\Documents\AI\Windsurf\Web-app-experience\experience-builder-ui\node_modules\@babel\parser\lib\index.js:10878:27)
    at TypeScriptParserMixin.parseExprOp (C:\Users\<USER>\OneDrive\Documents\AI\Windsurf\Web-app-experience\experience-builder-ui\node_modules\@babel\parser\lib\index.js:9335:18)
    at TypeScriptParserMixin.parseExprOps (C:\Users\<USER>\OneDrive\Documents\AI\Windsurf\Web-app-experience\experience-builder-ui\node_modules\@babel\parser\lib\index.js:10839:17)
    at TypeScriptParserMixin.parseMaybeConditional (C:\Users\<USER>\OneDrive\Documents\AI\Windsurf\Web-app-experience\experience-builder-ui\node_modules\@babel\parser\lib\index.js:10812:23)
    at TypeScriptParserMixin.parseMaybeAssign (C:\Users\<USER>\OneDrive\Documents\AI\Windsurf\Web-app-experience\experience-builder-ui\node_modules\@babel\parser\lib\index.js:10765:21)
    at TypeScriptParserMixin.parseMaybeAssign (C:\Users\<USER>\OneDrive\Documents\AI\Windsurf\Web-app-experience\experience-builder-ui\node_modules\@babel\parser\lib\index.js:9743:20)
    at TypeScriptParserMixin.parseExpressionBase (C:\Users\<USER>\OneDrive\Documents\AI\Windsurf\Web-app-experience\experience-builder-ui\node_modules\@babel\parser\lib\index.js:10718:23)
    at C:\Users\<USER>\OneDrive\Documents\AI\Windsurf\Web-app-experience\experience-builder-ui\node_modules\@babel\parser\lib\index.js:10714:39
    at TypeScriptParserMixin.allowInAnd (C:\Users\<USER>\OneDrive\Documents\AI\Windsurf\Web-app-experience\experience-builder-ui\node_modules\@babel\parser\lib\index.js:12361:12)
    at TypeScriptParserMixin.parseExpression (C:\Users\<USER>\OneDrive\Documents\AI\Windsurf\Web-app-experience\experience-builder-ui\node_modules\@babel\parser\lib\index.js:10714:17)
    at TypeScriptParserMixin.jsxParseExpressionContainer (C:\Users\<USER>\OneDrive\Documents\AI\Windsurf\Web-app-experience\experience-builder-ui\node_modules\@babel\parser\lib\index.js:4644:31)
    at TypeScriptParserMixin.jsxParseAttributeValue (C:\Users\<USER>\OneDrive\Documents\AI\Windsurf\Web-app-experience\experience-builder-ui\node_modules\@babel\parser\lib\index.js:4616:21)
    at TypeScriptParserMixin.jsxParseAttribute (C:\Users\<USER>\OneDrive\Documents\AI\Windsurf\Web-app-experience\experience-builder-ui\node_modules\@babel\parser\lib\index.js:4665:38)
    at TypeScriptParserMixin.jsxParseOpeningElementAfterName (C:\Users\<USER>\OneDrive\Documents\AI\Windsurf\Web-app-experience\experience-builder-ui\node_modules\@babel\parser\lib\index.js:4679:28)
    at TypeScriptParserMixin.jsxParseOpeningElementAfterName (C:\Users\<USER>\OneDrive\Documents\AI\Windsurf\Web-app-experience\experience-builder-ui\node_modules\@babel\parser\lib\index.js:10004:18)
    at TypeScriptParserMixin.jsxParseOpeningElementAt (C:\Users\<USER>\OneDrive\Documents\AI\Windsurf\Web-app-experience\experience-builder-ui\node_modules\@babel\parser\lib\index.js:4674:17)
    at TypeScriptParserMixin.jsxParseElementAt (C:\Users\<USER>\OneDrive\Documents\AI\Windsurf\Web-app-experience\experience-builder-ui\node_modules\@babel\parser\lib\index.js:4698:33)
    at TypeScriptParserMixin.jsxParseElementAt (C:\Users\<USER>\OneDrive\Documents\AI\Windsurf\Web-app-experience\experience-builder-ui\node_modules\@babel\parser\lib\index.js:4710:32)
    at TypeScriptParserMixin.jsxParseElement (C:\Users\<USER>\OneDrive\Documents\AI\Windsurf\Web-app-experience\experience-builder-ui\node_modules\@babel\parser\lib\index.js:4761:17)
    at TypeScriptParserMixin.parseExprAtom (C:\Users\<USER>\OneDrive\Documents\AI\Windsurf\Web-app-experience\experience-builder-ui\node_modules\@babel\parser\lib\index.js:4771:19)
    at TypeScriptParserMixin.parseExprSubscripts (C:\Users\<USER>\OneDrive\Documents\AI\Windsurf\Web-app-experience\experience-builder-ui\node_modules\@babel\parser\lib\index.js:11012:23)
    at TypeScriptParserMixin.parseUpdate (C:\Users\<USER>\OneDrive\Documents\AI\Windsurf\Web-app-experience\experience-builder-ui\node_modules\@babel\parser\lib\index.js:10997:21)
    at TypeScriptParserMixin.parseMaybeUnary (C:\Users\<USER>\OneDrive\Documents\AI\Windsurf\Web-app-experience\experience-builder-ui\node_modules\@babel\parser\lib\index.js:10977:23)
    at TypeScriptParserMixin.parseMaybeUnary (C:\Users\<USER>\OneDrive\Documents\AI\Windsurf\Web-app-experience\experience-builder-ui\node_modules\@babel\parser\lib\index.js:9794:18)
    at TypeScriptParserMixin.parseMaybeUnaryOrPrivate (C:\Users\<USER>\OneDrive\Documents\AI\Windsurf\Web-app-experience\experience-builder-ui\node_modules\@babel\parser\lib\index.js:10830:61)
    at TypeScriptParserMixin.parseExprOps (C:\Users\<USER>\OneDrive\Documents\AI\Windsurf\Web-app-experience\experience-builder-ui\node_modules\@babel\parser\lib\index.js:10835:23)
    at TypeScriptParserMixin.parseMaybeConditional (C:\Users\<USER>\OneDrive\Documents\AI\Windsurf\Web-app-experience\experience-builder-ui\node_modules\@babel\parser\lib\index.js:10812:23)
    at TypeScriptParserMixin.parseMaybeAssign (C:\Users\<USER>\OneDrive\Documents\AI\Windsurf\Web-app-experience\experience-builder-ui\node_modules\@babel\parser\lib\index.js:10765:21)
    at C:\Users\<USER>\OneDrive\Documents\AI\Windsurf\Web-app-experience\experience-builder-ui\node_modules\@babel\parser\lib\index.js:9732:39
    at TypeScriptParserMixin.tryParse (C:\Users\<USER>\OneDrive\Documents\AI\Windsurf\Web-app-experience\experience-builder-ui\node_modules\@babel\parser\lib\index.js:6918:20)
    at TypeScriptParserMixin.parseMaybeAssign (C:\Users\<USER>\OneDrive\Documents\AI\Windsurf\Web-app-experience\experience-builder-ui\node_modules\@babel\parser\lib\index.js:9732:18)
    at C:\Users\<USER>\OneDrive\Documents\AI\Windsurf\Web-app-experience\experience-builder-ui\node_modules\@babel\parser\lib\index.js:10734:39
    at TypeScriptParserMixin.allowInAnd (C:\Users\<USER>\OneDrive\Documents\AI\Windsurf\Web-app-experience\experience-builder-ui\node_modules\@babel\parser\lib\index.js:12361:12)
    at TypeScriptParserMixin.parseMaybeAssignAllowIn (C:\Users\<USER>\OneDrive\Documents\AI\Windsurf\Web-app-experience\experience-builder-ui\node_modules\@babel\parser\lib\index.js:10734:17)
    at TypeScriptParserMixin.parseParenAndDistinguishExpression (C:\Users\<USER>\OneDrive\Documents\AI\Windsurf\Web-app-experience\experience-builder-ui\node_modules\@babel\parser\lib\index.js:11608:28)
    at TypeScriptParserMixin.parseExprAtom (C:\Users\<USER>\OneDrive\Documents\AI\Windsurf\Web-app-experience\experience-builder-ui\node_modules\@babel\parser\lib\index.js:11262:23)
    at TypeScriptParserMixin.parseExprAtom (C:\Users\<USER>\OneDrive\Documents\AI\Windsurf\Web-app-experience\experience-builder-ui\node_modules\@babel\parser\lib\index.js:4776:20)
    at TypeScriptParserMixin.parseExprSubscripts (C:\Users\<USER>\OneDrive\Documents\AI\Windsurf\Web-app-experience\experience-builder-ui\node_modules\@babel\parser\lib\index.js:11012:23
Click outside, press Esc key, or fix the code to dismiss.

[{
	"resource": "/c:/Users/<USER>/OneDrive/Documents/AI/Windsurf/Web-app-experience/experience-builder-ui/src/components/ui/experience-builder-sidebar.tsx",
	"owner": "typescript",
	"code": "17008",
	"severity": 8,
	"message": "JSX element 'Sidebar' has no corresponding closing tag.",
	"source": "ts",
	"startLineNumber": 137,
	"startColumn": 10,
	"endLineNumber": 137,
	"endColumn": 17,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/c:/Users/<USER>/OneDrive/Documents/AI/Windsurf/Web-app-experience/experience-builder-ui/src/components/ui/experience-builder-sidebar.tsx",
	"owner": "typescript",
	"code": "2322",
	"severity": 8,
	"message": "Type 'boolean' is not assignable to type 'string'.",
	"source": "ts",
	"startLineNumber": 174,
	"startColumn": 33,
	"endLineNumber": 174,
	"endColumn": 42,
	"relatedInformation": [
		{
			"startLineNumber": 2687,
			"startColumn": 9,
			"endLineNumber": 2687,
			"endColumn": 18,
			"message": "The expected type comes from property 'className' which is declared here on type 'DetailedHTMLProps<HTMLAttributes<HTMLSpanElement>, HTMLSpanElement>'",
			"resource": "/c:/Users/<USER>/OneDrive/Documents/AI/Windsurf/Web-app-experience/experience-builder-ui/node_modules/@types/react/index.d.ts"
		}
	],
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/c:/Users/<USER>/OneDrive/Documents/AI/Windsurf/Web-app-experience/experience-builder-ui/src/components/ui/experience-builder-sidebar.tsx",
	"owner": "typescript",
	"code": "1109",
	"severity": 8,
	"message": "Expression expected.",
	"source": "ts",
	"startLineNumber": 177,
	"startColumn": 29,
	"endLineNumber": 177,
	"endColumn": 30,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/c:/Users/<USER>/OneDrive/Documents/AI/Windsurf/Web-app-experience/experience-builder-ui/src/components/ui/experience-builder-sidebar.tsx",
	"owner": "typescript",
	"code": "1381",
	"severity": 8,
	"message": "Unexpected token. Did you mean `{'}'}` or `&rbrace;`?",
	"source": "ts",
	"startLineNumber": 177,
	"startColumn": 30,
	"endLineNumber": 177,
	"endColumn": 31,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/c:/Users/<USER>/OneDrive/Documents/AI/Windsurf/Web-app-experience/experience-builder-ui/src/components/ui/experience-builder-sidebar.tsx",
	"owner": "typescript",
	"code": "1382",
	"severity": 8,
	"message": "Unexpected token. Did you mean `{'>'}` or `&gt;`?",
	"source": "ts",
	"startLineNumber": 177,
	"startColumn": 31,
	"endLineNumber": 177,
	"endColumn": 32,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/c:/Users/<USER>/OneDrive/Documents/AI/Windsurf/Web-app-experience/experience-builder-ui/src/components/ui/experience-builder-sidebar.tsx",
	"owner": "typescript",
	"code": "17002",
	"severity": 8,
	"message": "Expected corresponding JSX closing tag for 'div'.",
	"source": "ts",
	"startLineNumber": 179,
	"startColumn": 29,
	"endLineNumber": 179,
	"endColumn": 33,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/c:/Users/<USER>/OneDrive/Documents/AI/Windsurf/Web-app-experience/experience-builder-ui/src/components/ui/experience-builder-sidebar.tsx",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "')' expected.",
	"source": "ts",
	"startLineNumber": 180,
	"startColumn": 25,
	"endLineNumber": 180,
	"endColumn": 27,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/c:/Users/<USER>/OneDrive/Documents/AI/Windsurf/Web-app-experience/experience-builder-ui/src/components/ui/experience-builder-sidebar.tsx",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 181,
	"startColumn": 25,
	"endLineNumber": 181,
	"endColumn": 26,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/c:/Users/<USER>/OneDrive/Documents/AI/Windsurf/Web-app-experience/experience-builder-ui/src/components/ui/experience-builder-sidebar.tsx",
	"owner": "typescript",
	"code": "2322",
	"severity": 8,
	"message": "Type 'boolean' is not assignable to type 'string'.",
	"source": "ts",
	"startLineNumber": 182,
	"startColumn": 30,
	"endLineNumber": 182,
	"endColumn": 39,
	"relatedInformation": [
		{
			"startLineNumber": 2687,
			"startColumn": 9,
			"endLineNumber": 2687,
			"endColumn": 18,
			"message": "The expected type comes from property 'className' which is declared here on type 'DetailedHTMLProps<HTMLAttributes<HTMLDivElement>, HTMLDivElement>'",
			"resource": "/c:/Users/<USER>/OneDrive/Documents/AI/Windsurf/Web-app-experience/experience-builder-ui/node_modules/@types/react/index.d.ts"
		}
	],
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/c:/Users/<USER>/OneDrive/Documents/AI/Windsurf/Web-app-experience/experience-builder-ui/src/components/ui/experience-builder-sidebar.tsx",
	"owner": "typescript",
	"code": "2365",
	"severity": 8,
	"message": "Operator '>' cannot be applied to types 'string' and '{ link: any; \"\": any; }'.",
	"source": "ts",
	"startLineNumber": 182,
	"startColumn": 41,
	"endLineNumber": 186,
	"endColumn": 38,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/c:/Users/<USER>/OneDrive/Documents/AI/Windsurf/Web-app-experience/experience-builder-ui/src/components/ui/experience-builder-sidebar.tsx",
	"owner": "typescript",
	"code": "2304",
	"severity": 8,
	"message": "Cannot find name 'isActive'.",
	"source": "ts",
	"startLineNumber": 184,
	"startColumn": 27,
	"endLineNumber": 184,
	"endColumn": 35,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/c:/Users/<USER>/OneDrive/Documents/AI/Windsurf/Web-app-experience/experience-builder-ui/src/components/ui/experience-builder-sidebar.tsx",
	"owner": "typescript",
	"code": "2552",
	"severity": 8,
	"message": "Cannot find name 'link'. Did you mean 'links'?",
	"source": "ts",
	"startLineNumber": 186,
	"startColumn": 28,
	"endLineNumber": 186,
	"endColumn": 32,
	"relatedInformation": [
		{
			"startLineNumber": 41,
			"startColumn": 9,
			"endLineNumber": 41,
			"endColumn": 14,
			"message": "'links' is declared here.",
			"resource": "/c:/Users/<USER>/OneDrive/Documents/AI/Windsurf/Web-app-experience/experience-builder-ui/src/components/ui/experience-builder-sidebar.tsx"
		}
	],
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/c:/Users/<USER>/OneDrive/Documents/AI/Windsurf/Web-app-experience/experience-builder-ui/src/components/ui/experience-builder-sidebar.tsx",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "',' expected.",
	"source": "ts",
	"startLineNumber": 186,
	"startColumn": 32,
	"endLineNumber": 186,
	"endColumn": 33,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/c:/Users/<USER>/OneDrive/Documents/AI/Windsurf/Web-app-experience/experience-builder-ui/src/components/ui/experience-builder-sidebar.tsx",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "'}' expected.",
	"source": "ts",
	"startLineNumber": 187,
	"startColumn": 25,
	"endLineNumber": 187,
	"endColumn": 27,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/c:/Users/<USER>/OneDrive/Documents/AI/Windsurf/Web-app-experience/experience-builder-ui/src/components/ui/experience-builder-sidebar.tsx",
	"owner": "typescript",
	"code": "1381",
	"severity": 8,
	"message": "Unexpected token. Did you mean `{'}'}` or `&rbrace;`?",
	"source": "ts",
	"startLineNumber": 188,
	"startColumn": 24,
	"endLineNumber": 188,
	"endColumn": 25,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/c:/Users/<USER>/OneDrive/Documents/AI/Windsurf/Web-app-experience/experience-builder-ui/src/components/ui/experience-builder-sidebar.tsx",
	"owner": "typescript",
	"code": "1381",
	"severity": 8,
	"message": "Unexpected token. Did you mean `{'}'}` or `&rbrace;`?",
	"source": "ts",
	"startLineNumber": 191,
	"startColumn": 17,
	"endLineNumber": 191,
	"endColumn": 18,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/c:/Users/<USER>/OneDrive/Documents/AI/Windsurf/Web-app-experience/experience-builder-ui/src/components/ui/experience-builder-sidebar.tsx",
	"owner": "typescript",
	"code": "1381",
	"severity": 8,
	"message": "Unexpected token. Did you mean `{'}'}` or `&rbrace;`?",
	"source": "ts",
	"startLineNumber": 191,
	"startColumn": 19,
	"endLineNumber": 191,
	"endColumn": 20,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/c:/Users/<USER>/OneDrive/Documents/AI/Windsurf/Web-app-experience/experience-builder-ui/src/components/ui/experience-builder-sidebar.tsx",
	"owner": "typescript",
	"code": "17002",
	"severity": 8,
	"message": "Expected corresponding JSX closing tag for 'SidebarBody'.",
	"source": "ts",
	"startLineNumber": 192,
	"startColumn": 17,
	"endLineNumber": 192,
	"endColumn": 20,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/c:/Users/<USER>/OneDrive/Documents/AI/Windsurf/Web-app-experience/experience-builder-ui/src/components/ui/experience-builder-sidebar.tsx",
	"owner": "typescript",
	"code": "17002",
	"severity": 8,
	"message": "Expected corresponding JSX closing tag for 'div'.",
	"source": "ts",
	"startLineNumber": 205,
	"startColumn": 13,
	"endLineNumber": 205,
	"endColumn": 24,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/c:/Users/<USER>/OneDrive/Documents/AI/Windsurf/Web-app-experience/experience-builder-ui/src/components/ui/experience-builder-sidebar.tsx",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "')' expected.",
	"source": "ts",
	"startLineNumber": 206,
	"startColumn": 9,
	"endLineNumber": 206,
	"endColumn": 11,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/c:/Users/<USER>/OneDrive/Documents/AI/Windsurf/Web-app-experience/experience-builder-ui/src/components/ui/experience-builder-sidebar.tsx",
	"owner": "typescript",
	"code": "1109",
	"severity": 8,
	"message": "Expression expected.",
	"source": "ts",
	"startLineNumber": 207,
	"startColumn": 7,
	"endLineNumber": 207,
	"endColumn": 9,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/c:/Users/<USER>/OneDrive/Documents/AI/Windsurf/Web-app-experience/experience-builder-ui/src/components/ui/experience-builder-sidebar.tsx",
	"owner": "typescript",
	"code": "2304",
	"severity": 8,
	"message": "Cannot find name 'div'.",
	"source": "ts",
	"startLineNumber": 207,
	"startColumn": 9,
	"endLineNumber": 207,
	"endColumn": 12,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/c:/Users/<USER>/OneDrive/Documents/AI/Windsurf/Web-app-experience/experience-builder-ui/src/components/ui/experience-builder-sidebar.tsx",
	"owner": "typescript",
	"code": "2304",
	"severity": 8,
	"message": "Cannot find name 'div'.",
	"source": "ts",
	"startLineNumber": 210,
	"startColumn": 8,
	"endLineNumber": 210,
	"endColumn": 11,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/c:/Users/<USER>/OneDrive/Documents/AI/Windsurf/Web-app-experience/experience-builder-ui/src/components/ui/experience-builder-sidebar.tsx",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 210,
	"startColumn": 12,
	"endLineNumber": 210,
	"endColumn": 21,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/c:/Users/<USER>/OneDrive/Documents/AI/Windsurf/Web-app-experience/experience-builder-ui/src/components/ui/experience-builder-sidebar.tsx",
	"owner": "typescript",
	"code": "2304",
	"severity": 8,
	"message": "Cannot find name 'className'.",
	"source": "ts",
	"startLineNumber": 210,
	"startColumn": 12,
	"endLineNumber": 210,
	"endColumn": 21,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/c:/Users/<USER>/OneDrive/Documents/AI/Windsurf/Web-app-experience/experience-builder-ui/src/components/ui/experience-builder-sidebar.tsx",
	"owner": "typescript",
	"code": "2365",
	"severity": 8,
	"message": "Operator '>' cannot be applied to types 'string' and 'Element'.",
	"source": "ts",
	"startLineNumber": 210,
	"startColumn": 22,
	"endLineNumber": 213,
	"endColumn": 15,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/c:/Users/<USER>/OneDrive/Documents/AI/Windsurf/Web-app-experience/experience-builder-ui/src/components/ui/experience-builder-sidebar.tsx",
	"owner": "typescript",
	"code": "1128",
	"severity": 8,
	"message": "Declaration or statement expected.",
	"source": "ts",
	"startLineNumber": 214,
	"startColumn": 7,
	"endLineNumber": 214,
	"endColumn": 9,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/c:/Users/<USER>/OneDrive/Documents/AI/Windsurf/Web-app-experience/experience-builder-ui/src/components/ui/experience-builder-sidebar.tsx",
	"owner": "typescript",
	"code": "2304",
	"severity": 8,
	"message": "Cannot find name 'div'.",
	"source": "ts",
	"startLineNumber": 214,
	"startColumn": 9,
	"endLineNumber": 214,
	"endColumn": 12,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/c:/Users/<USER>/OneDrive/Documents/AI/Windsurf/Web-app-experience/experience-builder-ui/src/components/ui/experience-builder-sidebar.tsx",
	"owner": "typescript",
	"code": "1109",
	"severity": 8,
	"message": "Expression expected.",
	"source": "ts",
	"startLineNumber": 215,
	"startColumn": 5,
	"endLineNumber": 215,
	"endColumn": 7,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/c:/Users/<USER>/OneDrive/Documents/AI/Windsurf/Web-app-experience/experience-builder-ui/src/components/ui/experience-builder-sidebar.tsx",
	"owner": "typescript",
	"code": "2304",
	"severity": 8,
	"message": "Cannot find name 'div'.",
	"source": "ts",
	"startLineNumber": 215,
	"startColumn": 7,
	"endLineNumber": 215,
	"endColumn": 10,
	"extensionID": "vscode.typescript-language-features"
},{
	"resource": "/c:/Users/<USER>/OneDrive/Documents/AI/Windsurf/Web-app-experience/experience-builder-ui/src/components/ui/experience-builder-sidebar.tsx",
	"owner": "typescript",
	"code": "1109",
	"severity": 8,
	"message": "Expression expected.",
	"source": "ts",
	"startLineNumber": 216,
	"startColumn": 3,
	"endLineNumber": 216,
	"endColumn": 4,
	"extensionID": "vscode.typescript-language-features"
}]