.pointer {
  pointer-events: none;
}

.wheel {
  pointer-events: none;
}

.wheelSegment {
  pointer-events: auto;
}

.container {
  background: #fff !important;
  overflow: hidden;
  border-radius: 8px;
  position: relative;
  z-index: 2; /* Elevate above potential gray backgrounds */
}

.wheelContainer {
  overflow: hidden; /* Additional clipping */
  border-radius: inherit; /* Inherit container rounding */
}
