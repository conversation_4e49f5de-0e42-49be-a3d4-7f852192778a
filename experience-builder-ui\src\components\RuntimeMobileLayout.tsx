import React from 'react';
import type { ReactNode } from 'react';

interface RuntimeMobileLayoutProps {
  children: ReactNode;
  className?: string;
}

export const RuntimeMobileLayout: React.FC<RuntimeMobileLayoutProps> = ({ 
  children, 
  className = '' 
}) => {
  return (
    <div className={`flex items-center justify-center min-h-screen bg-gray-100 ${className}`}>
      {/* Phone frame */}
      <div className="relative w-[375px] h-[812px] bg-white rounded-[36px] shadow-2xl overflow-hidden border border-gray-300">
        {/* Speaker notch */}
        <div className="absolute top-3 left-1/2 -translate-x-1/2 w-24 h-2 bg-gray-300 rounded-full"></div>
        
        {/* Screen content */}
        <main className="absolute inset-x-0 top-8 bottom-14 overflow-y-auto">
          {children}
        </main>

        {/* Home indicator */}
        <div className="absolute bottom-3 left-1/2 -translate-x-1/2 w-32 h-1.5 bg-gray-300 rounded-full"></div>

        {/* Bottom nav (fixed) */}
        <nav className="absolute bottom-8 left-0 right-0 px-6">
          <div className="flex justify-around items-center bg-white/70 backdrop-blur-md rounded-xl py-2 shadow">
            <button className="flex flex-col items-center text-gray-600 hover:text-blue-500">
              <span className="text-xs">Home</span>
            </button>
            <button className="flex flex-col items-center text-gray-600 hover:text-blue-500">
              <span className="text-xs">Explore</span>
            </button>
            <button className="flex flex-col items-center text-gray-600 hover:text-blue-500">
              <span className="text-xs">Profile</span>
            </button>
          </div>
        </nav>
      </div>
    </div>
  );
};

export default RuntimeMobileLayout;
