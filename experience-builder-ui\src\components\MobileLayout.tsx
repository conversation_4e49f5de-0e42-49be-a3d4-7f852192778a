import { Box } from '@mui/material';
import React from 'react';

/**
 * MobileLayout – Renders children inside a phone bezel for desktop preview.
 * On actual mobile devices it simply shows the content full-screen.
 */
const MobileLayout: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const isMobile = window.matchMedia('(max-width: 600px)').matches;

  if (isMobile) return <>{children}</>;

  return (
    <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
      <Box
        sx={{
          width: 320,
          height: 640,
          borderRadius: '28px',
          backgroundColor: '#1a202c',
          p: 2,
          boxShadow: '0 0 24px rgba(0,0,0,0.3)',
          overflow: 'hidden',
        }}
      >
        <Box
          sx={{
            width: '100%',
            height: '100%',
            borderRadius: '20px',
            backgroundColor: 'white',
            overflowY: 'auto',
          }}
        >
          {children}
        </Box>
      </Box>
    </Box>
  );
};

export default MobileLayout;
