import React from 'react';
import ModernScratchCard, { type RewardType } from './ModernScratchCard';

// --- TypeScript Interfaces ---
// Using RewardType from ModernScratchCard for compatibility

// Re-export ScratchAreaData for other components to use
import type { ScratchCardReward } from './ModernScratchCard';

// Placeholder for ScratchAreaData - will determine actual source later
export interface ScratchAreaData {
  id: string;
  scratchText: string;
  revealedTextLine1: string;
  revealedTextLine2: string;
  rewardType: RewardType;
  rewardValue: string;
  isRevealed: boolean;
}

export type { RewardType };

// Props for the ScratchCardModule component
export interface ScratchCardModuleProps {
  headingText: string;
  subTextContent: string;
  initialScratchAreas: ScratchAreaData[]; // Parent provides the configuration for scratch areas
  whatsappNumber?: string; // WhatsApp number for claiming rewards
  countryCode?: string;

  onRedeem?: () => void; // Optional: Callback when redeem button is clicked
  onWin?: (reward: ScratchCardReward) => void; // Changed to expect a single ScratchCardReward
}


// --- React Component (Presentational) ---
const ScratchCardModule: React.FC<ScratchCardModuleProps> = (props) => {
  // Map initialScratchAreas to rewards expected by ModernScratchCard
  const rewardsForModernScratchCard = props.initialScratchAreas.map(area => ({
    id: area.id,
    text: area.revealedTextLine1 || area.revealedTextLine2 || '', // Use revealedTextLine1 as the reward text
  }));

  return (
    <ModernScratchCard
      rewards={rewardsForModernScratchCard}
      whatsappNumber={props.whatsappNumber}
      countryCode={props.countryCode}
      onWin={(reward) => {
        if (props.onWin) {
          props.onWin(reward);
        }
      }}
    />
  );
};

export default ScratchCardModule;
