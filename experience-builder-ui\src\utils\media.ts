/**
 * Helper to turn a relative Supabase storage path (e.g. `backgrounds/foo.png`)
 * into a full public URL. If the input already looks like a full URL, it is
 * returned unchanged. This keeps mobile runtime free from server-side API calls.
 */
export const BUCKET_NAME = 'experience-media';

import { supabase } from './supabaseClient';

export interface UploadedMedia {
  mediaId: string;
  url: string;   // public URL
  path: string;  // storage path within bucket
}

/**
 * Upload a File object to the `experience-media` bucket and insert a row in
 * `media_library`. Returns the new media_id and its public URL.
 */
// TODO: Add proper interface for media library when schema is fixed
// interface MediaLibraryInsert {
//   owner_id: string | null;
//   path: string;
//   mime_type: string;
// }



export const uploadFile = async (
  file: File,
  ownerId?: string | null,
): Promise<UploadedMedia> => {
  // Default ownerId to the current logged in user if available
  if (ownerId === undefined) {
    const user = (await supabase.auth.getUser()).data.user;
    ownerId = user?.id ?? null;
  }
  const ext = file.name.split('.').pop() ?? 'bin';
  const uuid = (typeof crypto !== 'undefined' && crypto.randomUUID)
    ? crypto.randomUUID()
    : Math.random().toString(36).slice(2);
  const path = `${ownerId ?? 'anon'}/${uuid}.${ext}`;

  // 1. upload to storage (upsert true allows overwriting during dev)
  const { error: uploadErr } = await supabase.storage
    .from(BUCKET_NAME)
    .upload(path, file, { contentType: file.type, upsert: true });
  if (uploadErr) throw uploadErr;

  // 2. Insert record into media_library table
  const { data: dbData, error: dbError } = await supabase
    .from('media_library' as any)
    .insert({
      file_name: file.name,
      file_path: path,
      path: path, // Both path and file_path for compatibility
      mime_type: file.type,
      file_type: file.type.split('/')[0], // e.g., 'image', 'video'
      file_size: file.size,
      uploaded_by: ownerId,
      is_public: true,
      description: `Uploaded file: ${file.name}`,
      metadata: {
        originalName: file.name,
        uploadedAt: new Date().toISOString(),
        fileSize: file.size
      }
    } as any)
    .select('media_id')
    .single();
    
  if (dbError) {
    console.error('Database insertion error:', dbError);
    throw new Error(`Failed to save media to database: ${dbError.message}`);
  }
  if (!dbData) throw new Error('No data returned from media library insert');

  const url = getPublicUrl(path);
  const mediaId = (dbData as any).media_id;
  return { mediaId, url, path };
};

/**
 * Translate a storage path to a public URL for the configured bucket.
 */
export const getPublicUrl = (path: string): string => {
  const { data } = supabase.storage.from(BUCKET_NAME).getPublicUrl(path);
  // getPublicUrl is synchronous; if the bucket is public this will always succeed.
  // Fall back to resolveMediaUrl if for some reason data.publicUrl is undefined.
  return data.publicUrl ?? resolveMediaUrl(path) ?? path;
};

/**
 * Existing helper: convert stored path or absolute URL into a usable URL.
 * Enhanced with better deployment environment support and error handling.
 */
export const resolveMediaUrl = (path?: string | null): string | undefined => {
  console.log('🔍 MEDIA DEBUG - Input path:', path);
  
  if (!path) {
    console.log('❌ MEDIA DEBUG - No path provided');
    return undefined;
  }
  
  // Return absolute URLs as-is
  if (/^(https?:|blob:|data:)/.test(path)) {
    console.log('✅ MEDIA DEBUG - Absolute URL detected, returning as-is:', path);
    return path;
  }
  
  console.log('🔍 MEDIA DEBUG - Processing relative path:', path);
  console.log('🔍 MEDIA DEBUG - BUCKET_NAME:', BUCKET_NAME);
  console.log('🔍 MEDIA DEBUG - VITE_SUPABASE_URL:', import.meta.env.VITE_SUPABASE_URL);
  
  // Try to use Supabase client's getPublicUrl method first (most reliable)
  try {
    const cleanedPath = path.replace(/^\//, '');
    console.log('🔍 MEDIA DEBUG - Cleaned path for Supabase client:', cleanedPath);
    
    const { data } = supabase.storage.from(BUCKET_NAME).getPublicUrl(cleanedPath);
    console.log('🔍 MEDIA DEBUG - Supabase client response:', data);
    
    if (data.publicUrl) {
      console.log('✅ MEDIA DEBUG - Resolved via Supabase client:', data.publicUrl);
      return data.publicUrl;
    } else {
      console.warn('⚠️ MEDIA DEBUG - Supabase client returned no publicUrl');
    }
  } catch (error) {
    console.error('❌ MEDIA DEBUG - Supabase client failed:', error);
  }
  
  // Fallback: manual URL construction
  const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
  if (!supabaseUrl) {
    console.error('❌ MEDIA DEBUG - VITE_SUPABASE_URL not available!');
    console.log('🔍 MEDIA DEBUG - Available env vars:', Object.keys(import.meta.env));
    return path;
  }
  
  // Clean the path and construct the URL
  const cleanPath = path.replace(/^\//, '');
  const resolvedUrl = `${supabaseUrl}/storage/v1/object/public/${BUCKET_NAME}/${cleanPath}`;
  
  console.log('✅ MEDIA DEBUG - Manual URL construction:', resolvedUrl);
  
  // Test if the URL is accessible (for debugging)
  fetch(resolvedUrl, { method: 'HEAD' })
    .then(response => {
      if (response.ok) {
        console.log('✅ MEDIA DEBUG - URL is accessible:', resolvedUrl);
      } else {
        console.error('❌ MEDIA DEBUG - URL not accessible:', resolvedUrl, 'Status:', response.status);
      }
    })
    .catch(error => {
      console.error('❌ MEDIA DEBUG - URL fetch test failed:', resolvedUrl, error);
    });
  
  return resolvedUrl;
};
