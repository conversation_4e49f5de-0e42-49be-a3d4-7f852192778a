import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Button,
  TextField,
  Rating,
  RadioGroup,
  FormControlLabel,
  Radio,
  Paper,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import StarIcon from '@mui/icons-material/Star';
import SentimentVeryDissatisfiedIcon from '@mui/icons-material/SentimentVeryDissatisfied';
import SentimentDissatisfiedIcon from '@mui/icons-material/SentimentDissatisfied';
import SentimentNeutralIcon from '@mui/icons-material/SentimentNeutral';
import SentimentSatisfiedIcon from '@mui/icons-material/SentimentSatisfied';
import SentimentVerySatisfiedIcon from '@mui/icons-material/SentimentVerySatisfied';
import type { FeedbackSurveyContent, SurveyQuestion, CSATRating } from '../../App';

const StyledPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  margin: theme.spacing(2, 0),
  borderRadius: theme.spacing(2),
  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  color: 'white',
}));

const CSATContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  gap: theme.spacing(3),
  padding: theme.spacing(2),
}));

const RatingContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  gap: theme.spacing(2),
  padding: theme.spacing(2),
  backgroundColor: 'rgba(255, 255, 255, 0.1)',
  borderRadius: theme.spacing(1),
}));

const NPSContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  gap: theme.spacing(2),
  padding: theme.spacing(2),
  backgroundColor: 'rgba(255, 255, 255, 0.1)',
  borderRadius: theme.spacing(1),
}));

const NPSScale = styled(Box)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  gap: theme.spacing(1),
  flexWrap: 'wrap',
}));

const NPSButton = styled(Button)<{ selected?: boolean }>(({ theme, selected }) => ({
  minWidth: '40px',
  height: '40px',
  borderRadius: '50%',
  backgroundColor: selected ? theme.palette.primary.main : 'rgba(255, 255, 255, 0.2)',
  color: selected ? 'white' : 'rgba(255, 255, 255, 0.8)',
  '&:hover': {
    backgroundColor: selected ? theme.palette.primary.dark : 'rgba(255, 255, 255, 0.3)',
  },
}));

interface CSATFeedbackModuleProps {
  content: FeedbackSurveyContent;
  onSubmit?: (responses: any) => void;
  onContentChange?: (content: FeedbackSurveyContent) => void;
}

const defaultCSATRatings: CSATRating[] = [
  { score: 5, label: 'Very Satisfied', emoji: '😊' },
  { score: 4, label: 'Satisfied', emoji: '🙂' },
  { score: 3, label: 'Neutral', emoji: '😐' },
  { score: 2, label: 'Dissatisfied', emoji: '😕' },
  { score: 1, label: 'Very Dissatisfied', emoji: '😞' },
];

const CSATFeedbackModule: React.FC<CSATFeedbackModuleProps> = ({
  content,
  onSubmit,
  // onContentChange is not used in this component but kept in the interface for API consistency
}) => {
  const [responses, setResponses] = useState<Record<string, any>>({});
  const [csatRating, setCSATRating] = useState<number | null>(null);
  const [npsScore, setNPSScore] = useState<number | null>(null);
  const [starRating, setStarRating] = useState<number | null>(null);
  const [submitted, setSubmitted] = useState(false);

  const handleQuestionResponse = (questionId: string, value: any) => {
    setResponses(prev => ({
      ...prev,
      [questionId]: value,
    }));
  };

  const [submitting, setSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);

  const handleSubmit = () => {
    setSubmitting(true);
    setSubmitError(null);
    
    const allResponses = {
      ...responses,
      csatRating,
      npsScore,
      starRating,
      timestamp: new Date().toISOString(),
    };
    
    try {
      onSubmit?.(allResponses);
      setSubmitted(true);
    } catch (error) {
      console.error('Error submitting CSAT feedback:', error);
      setSubmitError('There was an error submitting your feedback. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const renderSentimentIcon = (rating: number) => {
    switch (rating) {
      case 1:
        return <SentimentVeryDissatisfiedIcon />;
      case 2:
        return <SentimentDissatisfiedIcon />;
      case 3:
        return <SentimentNeutralIcon />;
      case 4:
        return <SentimentSatisfiedIcon />;
      case 5:
        return <SentimentVerySatisfiedIcon />;
      default:
        return null;
    }
  };

  const renderQuestion = (question: SurveyQuestion) => {
    switch (question.questionType) {
      case 'text':
        return (
          <TextField
            fullWidth
            multiline
            rows={3}
            variant="outlined"
            placeholder="Please share your feedback..."
            value={responses[question.id] || ''}
            onChange={(e) => handleQuestionResponse(question.id, e.target.value)}
            sx={{
              '& .MuiOutlinedInput-root': {
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
                color: 'white',
              },
            }}
          />
        );

      case 'rating':
        return (
          <Rating
            value={responses[question.id] || 0}
            onChange={(_, value) => handleQuestionResponse(question.id, value)}
            size="large"
            icon={<StarIcon fontSize="inherit" />}
            emptyIcon={<StarIcon fontSize="inherit" />}
            sx={{
              '& .MuiRating-iconFilled': {
                color: '#ffd700',
              },
              '& .MuiRating-iconEmpty': {
                color: 'rgba(255, 255, 255, 0.3)',
              },
            }}
          />
        );

      case 'yesno':
        return (
          <RadioGroup
            value={responses[question.id] || ''}
            onChange={(e) => handleQuestionResponse(question.id, e.target.value)}
            row
          >
            <FormControlLabel
              value="yes"
              control={<Radio sx={{ color: 'white' }} />}
              label="Yes"
              sx={{ color: 'white' }}
            />
            <FormControlLabel
              value="no"
              control={<Radio sx={{ color: 'white' }} />}
              label="No"
              sx={{ color: 'white' }}
            />
          </RadioGroup>
        );

      default:
        return null;
    }
  };

  if (submitted) {
    return (
      <StyledPaper>
        <Box textAlign="center">
          <Typography variant="h5" gutterBottom>
            Thank You! 🎉
          </Typography>
          <Typography variant="body1">
            {content.thankYouMessage || 'Your feedback has been submitted successfully. We appreciate your input!'}
          </Typography>
        </Box>
      </StyledPaper>
    );
  }

  return (
    <StyledPaper>
      <CSATContainer>
        <Typography variant="h4" textAlign="center" gutterBottom>
          {content.title || 'We Value Your Feedback'}
        </Typography>

        {/* CSAT Rating Section */}
        {content.enableCSAT && (
          <RatingContainer>
            <Typography variant="h6" textAlign="center">
              {content.csatQuestion || 'How satisfied are you with our service?'}
            </Typography>
            <Box display="flex" gap={2} justifyContent="center" flexWrap="wrap">
              {(content.csatRatings || defaultCSATRatings).map((rating) => (
                <Box
                  key={rating.score}
                  display="flex"
                  flexDirection="column"
                  alignItems="center"
                  gap={1}
                >
                  <Button
                    variant={csatRating === rating.score ? 'contained' : 'outlined'}
                    onClick={() => setCSATRating(rating.score)}
                    sx={{
                      minWidth: '60px',
                      height: '60px',
                      borderRadius: '50%',
                      fontSize: '24px',
                      backgroundColor: csatRating === rating.score ? 'primary.main' : 'transparent',
                      borderColor: 'white',
                      color: 'white',
                    }}
                  >
                    {rating.emoji || renderSentimentIcon(rating.score)}
                  </Button>
                  <Typography variant="caption" textAlign="center">
                    {rating.label}
                  </Typography>
                </Box>
              ))}
            </Box>
          </RatingContainer>
        )}

        {/* NPS Section */}
        {content.enableNPS && (
          <NPSContainer>
            <Typography variant="h6" textAlign="center">
              {content.npsQuestion || 'How likely are you to recommend us to a friend?'}
            </Typography>
            <NPSScale>
              {Array.from({ length: 6 }, (_, i) => (
                <NPSButton
                  key={i}
                  selected={npsScore === i}
                  onClick={() => setNPSScore(i)}
                >
                  {i}
                </NPSButton>
              ))}
            </NPSScale>
            <Box display="flex" justifyContent="space-between" mt={1}>
              <Typography variant="caption">Not likely</Typography>
              <Typography variant="caption">Very likely</Typography>
            </Box>
          </NPSContainer>
        )}

        {/* Star Rating Section */}
        {content.enableStarRating && (
          <RatingContainer>
            <Typography variant="h6" textAlign="center">
              {content.starRatingQuestion || 'Please rate your experience:'}
            </Typography>
            <Box display="flex" justifyContent="center" mt={2}>
              <Rating
                value={starRating || 0}
                onChange={(_, value) => setStarRating(value)}
                size="large"
                icon={<StarIcon fontSize="inherit" />}
                emptyIcon={<StarIcon fontSize="inherit" />}
                sx={{
                  '& .MuiRating-iconFilled': {
                    color: '#ffd700',
                  },
                  '& .MuiRating-iconEmpty': {
                    color: 'rgba(255, 255, 255, 0.3)',
                  },
                }}
              />
            </Box>
          </RatingContainer>
        )}

        {/* Additional Questions */}
        {content.questions.map((question) => (
          <Box key={question.id}>
            <Typography variant="h6" gutterBottom>
              {question.questionText}
              {question.required && <span style={{ color: '#ff6b6b' }}> *</span>}
            </Typography>
            {renderQuestion(question)}
          </Box>
        ))}

        {/* Qualitative Follow-up */}
        {content.enableFollowUp && (
          <Box>
            <Typography variant="h6" gutterBottom>
              {content.followUpQuestion || "Please tell us more about your experience:"}
            </Typography>
            <TextField
              multiline
              rows={4}
              fullWidth
              variant="outlined"
              placeholder="Your feedback helps us improve..."
              value={responses['followUp'] || ''}
              onChange={(e) => handleQuestionResponse('followUp', e.target.value)}
              sx={{
                "& .MuiOutlinedInput-root": {
                  borderRadius: "12px",
                  backgroundColor: "rgba(255, 255, 255, 0.1)",
                  "& fieldset": {
                    borderColor: "rgba(255, 255, 255, 0.3)",
                  },
                  "&:hover fieldset": {
                    borderColor: "rgba(255, 255, 255, 0.5)",
                  },
                  "&.Mui-focused fieldset": {
                    borderColor: "#ffffff",
                  },
                },
                "& .MuiInputBase-input": {
                  color: "#ffffff",
                },
                "& .MuiInputBase-input::placeholder": {
                  color: "rgba(255, 255, 255, 0.7)",
                  opacity: 1,
                },
              }}
            />
          </Box>
        )}

        {submitError && (
          <Box sx={{ mt: 2, p: 2, bgcolor: 'rgba(255, 0, 0, 0.1)', borderRadius: 1 }}>
            <Typography color="error">{submitError}</Typography>
          </Box>
        )}
        
        <Button
          variant="contained"
          size="large"
          onClick={handleSubmit}
          disabled={submitting}
          sx={{
            mt: 2,
            backgroundColor: 'rgba(255, 255, 255, 0.2)',
            color: 'white',
            '&:hover': {
              backgroundColor: 'rgba(255, 255, 255, 0.3)',
            },
          }}
        >
          {submitting ? 'Submitting...' : 'Submit Feedback'}
        </Button>
      </CSATContainer>
    </StyledPaper>
  );
};

export default CSATFeedbackModule;
