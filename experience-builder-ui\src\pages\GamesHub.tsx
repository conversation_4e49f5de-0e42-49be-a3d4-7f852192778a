import { Container, Typography, Paper } from '@mui/material';
import React from 'react';

/**
 * GamesHub page – placeholder for gamified web components
 * This page will host various mini-games that can be linked from
 * an experience or accessed directly. Each game will be designed
 * as a separate React component and loaded lazily here.
 */
const GamesHub: React.FC = () => {
  return (
    <Container maxWidth="sm" sx={{ mt: 4 }}>
      <Typography variant="h4" gutterBottom>
        Games Hub
      </Typography>
      <Paper sx={{ p: 2 }}>
        <Typography>
          This is a placeholder for the upcoming gamified components. Stay tuned!
        </Typography>
      </Paper>
    </Container>
  );
};

export default GamesHub;
