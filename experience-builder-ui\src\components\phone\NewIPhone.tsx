import React from "react";
import { Screen, type MediaItem, type Module } from "./Screen";
import { type HeaderConfig } from "../BackgroundSelector";

interface NewIPhoneProps {
  modules?: Module[];
  backgroundMedia?: MediaItem | null;
  currentScreen: number;
  totalScreens: number;
  onScreenChange: (idx: number) => void;
  onUpdateModuleContent?: (
    moduleId: string,
    updatedContentPart: Partial<Module["content"]>
  ) => void;
  headerConfig?: HeaderConfig;
}

export const NewIPhone: React.FC<NewIPhoneProps> = ({
  modules = [],
  backgroundMedia,
  currentScreen,
  totalScreens,
  onScreenChange,
  onUpdateModuleContent,
  headerConfig,
}) => {
  return (
    <div
      style={{
        transform: "scale(0.85)",
        position: "relative",
        transition: "transform 0.15s cubic-bezier(0.4, 0, 0.2, 1)",
      }}
    >
      <div
        style={{
          position: "relative",
          width: "377px",
          height: "785px",
        }}
      >
        {/* Side Buttons */}
        <div
          style={{
            position: "absolute",
            left: "-3px",
            top: "149px",
            width: "383px",
            height: "636px",
          }}
        >
          {/* Power Button */}
          <div
            style={{
              position: "absolute",
              left: "378px",
              top: "103px",
              width: "5px",
              height: "96px",
            }}
          >
            <div
              style={{
                position: "absolute",
                left: "0px",
                top: "-4px",
                width: "5px",
                height: "99px",
                backgroundColor: "rgb(240, 236, 227)",
                borderRadius: "2px 0px 0px 2px",
              }}
            />
            <div
              style={{
                position: "absolute",
                left: "0px",
                top: "-4px",
                width: "5px",
                height: "99px",
                backgroundImage:
                  "linear-gradient(rgb(0, 0, 0), rgba(0, 0, 0, 0.6))",
                borderRadius: "2px 0px 0px 2px",
              }}
            />
            <div
              style={{
                position: "absolute",
                left: "0px",
                top: "-4px",
                width: "5px",
                height: "99px",
                backgroundImage:
                  "linear-gradient(to right, rgb(0, 0, 0), rgba(0, 0, 0, 0))",
                borderRadius: "2px 0px 0px 2px",
              }}
            />
            <div
              style={{
                position: "absolute",
                left: "3px",
                top: "-2px",
                width: "1px",
                height: "19px",
                backgroundColor: "rgb(255, 255, 255)",
                borderRadius: "1px 1px 0px 0px",
                filter: "blur(0.5px)",
              }}
            />
            <div
              style={{
                position: "absolute",
                left: "3px",
                top: "-2px",
                width: "1px",
                height: "91px",
                backgroundColor: "rgba(255, 255, 255, 0.8)",
                borderRadius: "0px 0px 1px 1px",
              }}
            />
          </div>

          {/* Mute Switch */}
          <div
            style={{
              position: "absolute",
              left: "0px",
              top: "0px",
              width: "5px",
              height: "31px",
            }}
          >
            <div
              style={{
                position: "absolute",
                left: "0px",
                top: "0px",
                width: "5px",
                height: "31px",
                backgroundColor: "rgb(240, 236, 227)",
                borderRadius: "2px 0px 0px 2px",
              }}
            />
            <div
              style={{
                position: "absolute",
                left: "0px",
                top: "0px",
                width: "5px",
                height: "31px",
                backgroundImage:
                  "linear-gradient(rgb(0, 0, 0), rgba(0, 0, 0, 0))",
                borderRadius: "2px 0px 0px 2px",
              }}
            />
            <div
              style={{
                position: "absolute",
                left: "1px",
                top: "1px",
                width: "1px",
                height: "6px",
                backgroundColor: "rgb(255, 255, 255)",
                borderRadius: "1px 1px 0px 0px",
                filter: "blur(0.5px)",
              }}
            />
            <div
              style={{
                position: "absolute",
                left: "1px",
                top: "3px",
                width: "1px",
                height: "26px",
                backgroundColor: "rgba(255, 255, 255, 0.8)",
                borderRadius: "0px 0px 1px 1px",
                filter: "blur(0.5px)",
              }}
            />
          </div>

          {/* Volume Up */}
          <div
            style={{
              position: "absolute",
              left: "0px",
              top: "60px",
              width: "5px",
              height: "56px",
            }}
          >
            <div
              style={{
                position: "absolute",
                left: "0px",
                top: "0px",
                width: "5px",
                height: "56px",
                backgroundColor: "rgb(240, 236, 227)",
                borderRadius: "2px 0px 0px 2px",
              }}
            />
            <div
              style={{
                position: "absolute",
                left: "0px",
                top: "0px",
                width: "5px",
                height: "56px",
                backgroundImage:
                  "linear-gradient(rgb(0, 0, 0), rgba(0, 0, 0, 0))",
                borderRadius: "2px 0px 0px 2px",
              }}
            />
            <div
              style={{
                position: "absolute",
                left: "1px",
                top: "2px",
                width: "1px",
                height: "11px",
                backgroundColor: "rgb(255, 255, 255)",
                borderRadius: "1px 1px 0px 0px",
                filter: "blur(0.5px)",
              }}
            />
            <div
              style={{
                position: "absolute",
                left: "1px",
                top: "3px",
                width: "1px",
                height: "52px",
                backgroundColor: "rgba(255, 255, 255, 0.8)",
                borderRadius: "0px 0px 1px 1px",
                filter: "blur(0.5px)",
              }}
            />
          </div>

          {/* Volume Down */}
          <div
            style={{
              position: "absolute",
              left: "0px",
              top: "133px",
              width: "5px",
              height: "56px",
            }}
          >
            <div
              style={{
                position: "absolute",
                left: "0px",
                top: "0px",
                width: "5px",
                height: "56px",
                backgroundColor: "rgb(240, 236, 227)",
                borderRadius: "2px 0px 0px 2px",
              }}
            />
            <div
              style={{
                position: "absolute",
                left: "0px",
                top: "0px",
                width: "5px",
                height: "56px",
                backgroundImage:
                  "linear-gradient(rgb(0, 0, 0), rgba(0, 0, 0, 0))",
                borderRadius: "2px 0px 0px 2px",
              }}
            />
            <div
              style={{
                position: "absolute",
                left: "1px",
                top: "2px",
                width: "1px",
                height: "11px",
                backgroundColor: "rgb(255, 255, 255)",
                borderRadius: "1px 1px 0px 0px",
                filter: "blur(0.5px)",
              }}
            />
            <div
              style={{
                position: "absolute",
                left: "1px",
                top: "3px",
                width: "1px",
                height: "52px",
                backgroundColor: "rgba(255, 255, 255, 0.8)",
                borderRadius: "0px 0px 1px 1px",
                filter: "blur(0.5px)",
              }}
            />
          </div>
        </div>

        {/* Phone Frame */}
        <div
          style={{
            position: "absolute",
            left: "0px",
            top: "0px",
            width: "377px",
            height: "785px",
            borderRadius: "62px",
          }}
        >
          <div
            style={{
              position: "absolute",
              left: "0px",
              top: "0px",
              width: "377px",
              height: "785px",
              backgroundColor: "rgb(231, 227, 191)",
              borderRadius: "62px",
            }}
          />
          <div
            style={{
              position: "absolute",
              left: "0px",
              top: "0px",
              width: "377px",
              height: "785px",
              borderRadius: "62px",
              border: "3px solid rgb(195, 195, 195)",
              filter: "blur(2px)",
            }}
          />
          <div
            style={{
              position: "absolute",
              left: "0px",
              top: "0px",
              width: "377px",
              height: "785px",
              borderRadius: "62px",
              border: "1px solid rgb(82, 75, 64)",
            }}
          />
          <div
            style={{
              position: "absolute",
              left: "5px",
              top: "5px",
              width: "368px",
              height: "776px",
              borderRadius: "58px",
              border: "1px solid rgb(255, 255, 255)",
              filter: "blur(1px)",
            }}
          />
          <div
            style={{
              position: "absolute",
              left: "4px",
              top: "4px",
              width: "369px",
              height: "777px",
              borderRadius: "58px",
              border: "1px solid rgb(255, 255, 255)",
            }}
          />
          <div
            style={{
              position: "absolute",
              left: "293px",
              top: "0px",
              width: "6px",
              height: "4px",
              backgroundImage:
                "linear-gradient(rgb(129, 126, 115), rgb(195, 190, 172))",
            }}
          />
          <div
            style={{
              position: "absolute",
              left: "0px",
              top: "81px",
              width: "6px",
              height: "4px",
              backgroundImage:
                "linear-gradient(rgb(129, 126, 115), rgb(195, 190, 172))",
              transform: "rotate(-90deg)",
            }}
          />
          <div
            style={{
              position: "absolute",
              left: "0px",
              top: "698px",
              width: "4px",
              height: "6px",
              backgroundImage:
                "linear-gradient(to right, rgb(129, 126, 115), rgb(195, 190, 172))",
            }}
          />
          <div
            style={{
              position: "absolute",
              left: "63px",
              top: "781px",
              width: "5px",
              height: "4px",
              backgroundImage:
                "linear-gradient(to top, rgb(129, 126, 115), rgb(195, 190, 172))",
            }}
          />
          <div
            style={{
              position: "absolute",
              left: "373px",
              top: "698px",
              width: "4px",
              height: "6px",
              backgroundImage:
                "linear-gradient(to left, rgb(129, 126, 115), rgb(195, 190, 172))",
            }}
          />
        </div>

        {/* Screen Border */}
        <div
          style={{
            position: "absolute",
            left: "4px",
            top: "4px",
            width: "369px",
            height: "777px",
            borderRadius: "58px",
          }}
        >
          <div
            style={{
              position: "absolute",
              left: "0px",
              top: "0px",
              width: "369px",
              height: "777px",
              backgroundColor: "rgb(0, 1, 0)",
              borderRadius: "58px",
            }}
          />
          <div
            style={{
              position: "absolute",
              left: "0px",
              top: "0px",
              width: "369px",
              height: "777px",
              borderRadius: "58px",
              border: "4px solid rgb(135, 145, 148)",
              filter: "blur(3.5px)",
            }}
          />
        </div>

        {/* Screen Content Area */}
        <div
          style={{
            position: "absolute",
            left: "12px",
            top: "12px",
            width: "353px",
            height: "761px",
            backgroundColor: "rgb(255, 255, 255)",
            borderRadius: "52px",
            overflowY: "auto",
            overflowX: "hidden",
            padding: "45px 16px 16px 16px", // Added top padding to account for status bar
            scrollbarWidth: "none", // For Firefox
            msOverflowStyle: "none", // For Internet Explorer and Edge
          }}
          className="hide-scrollbar" // For Webkit browsers
        >
          {/* Main Screen Content */}
          <Screen
            modules={modules.filter((m) => (m.screen ?? 0) === currentScreen)}
            backgroundMedia={backgroundMedia}
            currentScreen={currentScreen}
            totalScreens={totalScreens}
            onScreenChange={onScreenChange}
            onUpdateModuleContent={onUpdateModuleContent}
            headerConfig={headerConfig}
            isFullScreenView={false}
          />
        </div>

        {/* Dynamic Island */}
        <div
          style={{
            position: "absolute",
            left: "133px",
            top: "24px",
            width: "111px",
            height: "31px",
            zIndex: 50,
          }}
        >
          <div
            style={{
              width: "110px",
              height: "31px",
              backgroundColor: "rgb(3, 3, 3)",
              borderRadius: "15.5px",
            }}
          />
          <div
            style={{
              position: "absolute",
              left: "84px",
              top: "6px",
              width: "19px",
              height: "19px",
              backgroundColor: "rgb(14, 11, 15)",
              borderRadius: "50%",
            }}
          />
          <div
            style={{
              position: "absolute",
              left: "88.27px",
              top: "10.27px",
              width: "11px",
              height: "11px",
              backgroundColor: "rgb(22, 20, 36)",
              borderRadius: "50%",
            }}
          />
          <div
            style={{
              position: "absolute",
              left: "90.41px",
              top: "12.41px",
              width: "6px",
              height: "6px",
              backgroundColor: "rgb(15, 15, 42)",
              borderRadius: "50%",
            }}
          />
          <div
            style={{
              position: "absolute",
              left: "92.55px",
              top: "13.48px",
              width: "2px",
              height: "2px",
              backgroundColor: "rgb(57, 55, 82)",
              borderRadius: "50%",
            }}
          />
        </div>

        {/* Status Bar */}
        <div
          style={{
            position: "absolute",
            left: "52px",
            top: "29px",
            width: "292px",
            height: "22px",
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            zIndex: 50,
          }}
        >
          <div
            style={{
              color: "rgb(28, 28, 28)",
              fontSize: "17px",
              fontWeight: 600,
              letterSpacing: "-0.8px",
              lineHeight: "25.5px",
            }}
          >
            9:41
          </div>
          <div
            style={{
              display: "flex",
              alignItems: "center",
              gap: "4px",
            }}
          >
            <div
              style={{
                display: "flex",
                alignItems: "flex-end",
                gap: "1px",
              }}
            >
              <div
                style={{
                  width: "2px",
                  height: "4px",
                  backgroundColor: "rgb(28, 28, 28)",
                  borderRadius: "1px",
                }}
              />
              <div
                style={{
                  width: "2px",
                  height: "6px",
                  backgroundColor: "rgb(28, 28, 28)",
                  borderRadius: "1px",
                }}
              />
              <div
                style={{
                  width: "2px",
                  height: "8px",
                  backgroundColor: "rgb(28, 28, 28)",
                  borderRadius: "1px",
                }}
              />
              <div
                style={{
                  width: "2px",
                  height: "10px",
                  backgroundColor: "rgb(28, 28, 28)",
                  borderRadius: "1px",
                }}
              />
            </div>
            <svg
              width="18"
              height="13"
              viewBox="0 0 18 13"
              fill="none"
              style={{
                marginLeft: "8px",
              }}
            >
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M9 2.58753C11.467 2.58764 13.839 3.55505 15.627 5.28982C15.761 5.42375 15.977 5.42206 16.109 5.28603L17.396 3.96045C17.463 3.89146 17.501 3.798 17.5 3.70076C17.499 3.60353 17.461 3.51052 17.393 3.44234C12.701 -1.14745 5.299 -1.14745 0.607 3.44234C0.539 3.51047 0.501 3.60345 0.5 3.70069C0.499 3.79792 0.537 3.89141 0.604 3.96045L1.891 5.28603C2.023 5.42226 2.239 5.42396 2.373 5.28982C4.161 3.55494 6.534 2.58752 9 2.58753ZM9.036 6.6724C10.391 6.6723 11.698 7.1864 12.703 8.115C12.838 8.2467 13.052 8.2439 13.185 8.1085L14.47 6.7829C14.538 6.7134 14.575 6.6191 14.574 6.5211C14.573 6.4231 14.534 6.3295 14.465 6.2614C11.406 3.35683 6.669 3.35683 3.609 6.2614C3.54 6.3295 3.501 6.4231 3.5 6.5212C3.499 6.6192 3.537 6.7135 3.605 6.7829L4.89 8.1085C5.022 8.2439 5.236 8.2467 5.372 8.115C6.376 7.1871 7.682 6.6729 9.036 6.6724ZM11.65 9.3427C11.652 9.441 11.614 9.5357 11.545 9.6046L9.322 11.8948C9.256 11.9621 9.168 12 9.075 12C8.982 12 8.893 11.9621 8.828 11.8948L6.605 9.6046C6.536 9.5357 6.498 9.4409 6.5 9.3426C6.502 9.2443 6.544 9.1512 6.615 9.0853C8.035 7.8595 10.115 7.8595 11.535 9.0853C11.606 9.1513 11.648 9.2444 11.65 9.3427Z"
                fill="#1C1C1C"
              />
            </svg>
            <div
              style={{
                display: "flex",
                alignItems: "center",
                marginLeft: "8px",
              }}
            >
              <div
                style={{
                  position: "relative",
                  width: "25px",
                  height: "12px",
                  border: "1px solid rgb(28, 28, 28)",
                  borderRadius: "2.6px",
                }}
              >
                <div
                  style={{
                    position: "absolute",
                    width: "20px",
                    height: "10px",
                    backgroundColor: "rgb(28, 28, 28)",
                    borderRadius: "2px",
                  }}
                />
              </div>
              <div
                style={{
                  width: "1.5px",
                  height: "4px",
                  backgroundColor: "rgb(28, 28, 28)",
                  borderRadius: "0px 1px 1px 0px",
                  marginLeft: "1px",
                }}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NewIPhone;
