import React from 'react';

export const PhoneFrame: React.FC = () => {
  return (
    <div className="w-[409px] h-[843px] absolute rounded-[62px] left-0 top-0">
      <div className="w-[409px] h-[843px] absolute bg-[#E7E3BF] rounded-[62px] left-0 top-0" />
      <div className="w-[409px] h-[843px] absolute rounded-[62px] border-solid border-[#C3C3C3] left-0 top-0" />
      <div className="w-[409px] h-[843px] absolute rounded-[62px] border-solid border-[#524B40] left-0 top-0" />
      <div className="w-[399px] h-[832px] absolute rounded-[62px] border-solid border-white left-[5px] top-[5px]" />
      <div className="w-[397px] h-[830px] absolute rounded-[62px] border-[0.4px] border-solid border-white left-1.5 top-1.5" />
      
      {/* Signal Cutouts */}
      <div className="absolute w-1.5 h-1 left-[318px] top-0" />
      <div className="absolute w-1.5 h-1 -rotate-90 left-0 top-[81px]" />
      <div className="absolute w-1 h-1.5 left-0 top-[756px]" />
      <div className="absolute w-[5px] h-1 left-[63px] top-[839px]" />
      <div className="absolute w-1 h-1.5 left-[405px] top-[756px]" />
    </div>
  );
};
