export type ModuleType = 'guide' | 'warranty' | 'shoppingLinks' | 'feedbackSurvey' | 'game';

export interface BaseModule {
  type: ModuleType;
  title: string;
}

export interface GuideModule extends BaseModule {
  type: 'guide';
  setupGuideContent: string;
}

export interface GameModule extends BaseModule {
  type: 'game';
  name: string; // e.g., 'SpinWheel'
  config: Record<string, unknown>;
}

export type Module = GuideModule | GameModule | BaseModule;
