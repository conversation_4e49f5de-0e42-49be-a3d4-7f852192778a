import React from 'react';

export const StatusBar: React.FC = () => {
  return (
    <header className="flex w-[292px] h-[22px] justify-center items-center gap-[182px] absolute left-[68px] top-[29px]">
      {/* Time */}
      <div className="w-[35px] h-[22px] absolute left-0 top-0">
        <time className="font-semibold text-[17px] text-white text-center leading-[22px] tracking-[-0.8px] absolute w-[35px] h-[22px] left-0 top-0">
          9:41
        </time>
      </div>

      {/* System Icons */}
      <div className="absolute right-0 top-1">
        <svg
          width="76"
          height="13"
          viewBox="0 0 76 13"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          className="w-[75px] h-[13px]"
        >
          {/* Battery */}
          <path
            opacity="0.35"
            d="M52 0.527344H69C70.9178 0.527344 72.4727 2.08222 72.4727 4V9C72.4727 10.9178 70.9178 12.4727 69 12.4727H52C50.0822 12.4727 48.5273 10.9178 48.5273 9V4C48.5273 2.08222 50.0822 0.527344 52 0.527344Z"
            stroke="white"
            strokeWidth="1.05509"
          />
          <path
            opacity="0.4"
            d="M74 5V9.22034C74.8491 8.86291 75.4012 8.0314 75.4012 7.11017C75.4012 6.18894 74.8491 5.35744 74 5Z"
            fill="white"
          />
          <path
            d="M50 4C50 2.89543 50.8954 2 52 2H69C70.1046 2 71 2.89543 71 4V9C71 10.1046 70.1046 11 69 11H52C50.8954 11 50 10.1046 50 9V4Z"
            fill="white"
          />
          
          {/* WiFi */}
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M33.5005 2.58753C35.967 2.58764 38.3393 3.55505 40.1269 5.28982C40.2615 5.42375 40.4766 5.42206 40.6092 5.28603L41.896 3.96045C41.9631 3.89146 42.0006 3.798 42 3.70076C41.9994 3.60353 41.9609 3.51052 41.893 3.44234C37.2011 -1.14745 29.7991 -1.14745 25.1072 3.44234C25.0392 3.51047 25.0006 3.60345 25 3.70069C24.9994 3.79792 25.0367 3.89141 25.1038 3.96045L26.391 5.28603C26.5235 5.42226 26.7388 5.42396 26.8733 5.28982C28.6612 3.55494 31.0337 2.58752 33.5005 2.58753ZM33.5359 6.67235C34.8911 6.67227 36.198 7.18644 37.2025 8.11497C37.3384 8.24674 37.5524 8.24389 37.6849 8.10853L38.9702 6.78295C39.0379 6.71342 39.0754 6.61909 39.0744 6.52108C39.0735 6.42306 39.034 6.32954 38.965 6.26142C35.9059 3.35683 31.1685 3.35683 28.1094 6.26142C28.0403 6.32953 28.0009 6.42311 28 6.52115C27.9991 6.6192 28.0368 6.71352 28.1046 6.78295L29.3895 8.10853C29.522 8.24389 29.736 8.24674 29.8719 8.11497C30.8758 7.18706 32.1816 6.67293 33.5359 6.67235ZM36.1496 9.34267C36.1515 9.44096 36.1137 9.53573 36.0449 9.60459L33.8217 11.8948C33.7565 11.9621 33.6676 12 33.5749 12C33.4822 12 33.3933 11.9621 33.3282 11.8948L31.1045 9.60459C31.0358 9.53568 30.998 9.44088 31.0001 9.34259C31.0021 9.2443 31.0438 9.15123 31.1153 9.08534C32.5351 7.8595 34.6147 7.8595 36.0345 9.08534C36.106 9.15128 36.1476 9.24438 36.1496 9.34267Z"
            fill="white"
          />
          
          {/* Signal Bars */}
          <path d="M10 3C10 2.44772 10.4477 2 11 2H12C12.5523 2 13 2.44772 13 3V11C13 11.5523 12.5523 12 12 12H11C10.4477 12 10 11.5523 10 11V3Z" fill="white" />
          <path d="M15 1C15 0.447715 15.4477 0 16 0H17C17.5523 0 18 0.447715 18 1V11C18 11.5523 17.5523 12 17 12H16C15.4477 12 15 11.5523 15 11V1Z" fill="white" />
          <path d="M5 6.5C5 5.94772 5.44772 5.5 6 5.5H7C7.55228 5.5 8 5.94772 8 6.5V11C8 11.5523 7.55228 12 7 12H6C5.44772 12 5 11.5523 5 11V6.5Z" fill="white" />
          <path d="M0 9C0 8.44772 0.447715 8 1 8H2C2.55228 8 3 8.44772 3 9V11C3 11.5523 2.55228 12 2 12H1C0.447715 12 0 11.5523 0 11V9Z" fill="white" />
        </svg>
      </div>
    </header>
  );
};
