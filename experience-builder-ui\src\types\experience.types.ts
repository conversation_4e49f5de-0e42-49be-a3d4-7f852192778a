// Define all types
type Module = {
  module_id: string;
  experience_id: string;
  module_type: string;
  title: string;
  display_order: number;
  config: Record<string, any>;
  created_at: string;
  updated_at: string;
};

type Experience = {
  experience_id: string;
  name: string;
  description?: string;
  background_image?: string | null;
  status: string;
  created_at: string;
  updated_at: string;
  modules: Module[];
  // Header configuration fields
  app_name?: string | null;
  app_subtitle?: string | null;
  get_started_button_text?: string | null;
};

type ExperienceResponse = {
  data: Experience | null;
  error: Error | null;
  loading: boolean;
};

// Export all types
export type { Module, Experience, ExperienceResponse };
