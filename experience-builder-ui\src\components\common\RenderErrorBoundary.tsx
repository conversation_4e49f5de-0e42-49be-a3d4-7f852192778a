import { Component } from 'react';
import type { ErrorInfo, ReactNode } from 'react';

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

/**
 * Error boundary component to catch and display render errors gracefully
 * Used to wrap components that might throw errors during rendering
 */
class RenderErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null
    };
  }

  static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error,
      errorInfo: null
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // You can also log the error to an error reporting service
    console.error('Render Error caught by boundary:', error, errorInfo);
    this.setState({
      error,
      errorInfo
    });
  }

  handleReset = (): void => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null
    });
  }

  render(): ReactNode {
    if (this.state.hasError) {
      // Fallback UI when an error occurs with consistent styling
      return (
        <div className="p-4 bg-white rounded-lg shadow-md" style={{
          background: 'radial-gradient(circle, rgba(255, 255, 255, 1) 0%, rgba(222, 222, 222, 1) 50%, rgba(255, 255, 255, 1) 100%)',
          borderRadius: '12px',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
        }}>
          <div className="text-center">
            <h4 className="font-semibold text-lg mb-4 text-white">
              Something went wrong
            </h4>
            <p className="text-sm text-white/90 mb-4">
              {this.state.error?.message || 'Unknown error occurred while rendering this component'}
            </p>
            <button
              onClick={this.handleReset}
              className="px-4 py-2 bg-white text-purple-600 rounded-lg font-medium text-sm hover:bg-gray-100 transition-colors duration-200"
            >
              Try Again
            </button>
          </div>
        </div>
      );
    }

    // When there's no error, render children normally with consistent container styling
    return (
      <div className="p-4 bg-white rounded-lg shadow-md" style={{
        background: 'radial-gradient(circle, rgba(255, 255, 255, 1) 0%, rgba(222, 222, 222, 1) 50%, rgba(255, 255, 255, 1) 100%)',
        borderRadius: '12px',
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        maxHeight: '70vh',
        overflowY: 'auto'
      }}>
        <div className="text-white">
          {this.props.children}
        </div>
      </div>
    );
  }
}

export default RenderErrorBoundary;
