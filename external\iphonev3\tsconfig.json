{"include": ["**/*.ts", "**/*.tsx", "**/*.json"], "references": [], "compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["./src/*"], "@components/*": ["../../experience-builder-ui/src/components/*"]}, "noImplicitAny": false, "noUnusedParameters": false, "skipLibCheck": true, "allowJs": true, "jsx": "react-jsx", "esModuleInterop": true, "noUnusedLocals": false, "strictNullChecks": false, "noEmit": true, "allowImportingTsExtensions": true}}