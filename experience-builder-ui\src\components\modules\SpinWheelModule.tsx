import React, { useState, useRef, useCallback } from "react";
import { <PERSON>, Typo<PERSON>, <PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "@mui/material";
import { Gift, Sparkle } from "phosphor-react";
import { styled, keyframes } from "@mui/material/styles";

// Import SpinWheel types from Screen.tsx using type-only imports
import type { SpinWheelModuleContent, SpinWheelPrize } from "../phone/Screen";

// Define props for the SpinWheelModule, making all properties from SpinWheelModuleContent optional
// as defaults are handled within the component.
export interface SpinWheelModuleProps extends Partial<SpinWheelModuleContent> {
  claimButtonLabel?: string;
  onRedeem?: (prize: SpinWheelPrize) => void;
  whatsappNumber?: string;
  countryCode?: string; // Add this prop
}

// Animations

const sparkleFloat = keyframes`
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-10px) rotate(180deg); }
`;

const bounce = keyframes`
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
`;

// Styled Components
const Container = styled(Box)(() => ({
  background: "linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%)",
  borderRadius: "24px",
  padding: "16px",
  position: "relative",
  overflow: "hidden",
  boxShadow: "0 20px 40px rgba(0,0,0,0.15)",
  display: "flex",
  flexDirection: "column",
  alignItems: "center",
  justifyContent: "center",
  minHeight: "420px",
  width: "100%",
  height: "100%",
}));

const WheelContainer = styled(Box)(() => ({
  position: "relative",
  width: "240px",
  height: "240px",
  margin: "10px auto",
  flex: 1,
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
}));

const WheelSVG = styled("svg")<{ $isSpinning: boolean }>(({ $isSpinning }) => ({
  width: "100%",
  height: "100%",
  borderRadius: "50%",
  boxShadow: "0 8px 30px rgba(0,0,0,0.3)",
  transition: $isSpinning
    ? "transform 3s cubic-bezier(0.25, 0.46, 0.45, 0.94)"
    : "none",
  filter: "drop-shadow(0 4px 8px rgba(0,0,0,0.2))",
}));

const Pointer = styled(Box)(() => ({
  position: "absolute",
  top: "-10px",
  left: "50%",
  transform: "translateX(-50%)",
  width: 0,
  height: 0,
  borderLeft: "15px solid transparent",
  borderRight: "15px solid transparent",
  borderTop: "30px solid #FFD700",
  zIndex: 10,
  filter: "drop-shadow(0 2px 4px rgba(0,0,0,0.3))",
}));

const SpinButton = styled(MuiButton, {
  shouldForwardProp: (prop) => prop !== "$isSpinning",
})<{ $isSpinning: boolean }>(({ $isSpinning }) => ({
  background: $isSpinning
    ? "linear-gradient(135deg, #9e9e9e, #757575)"
    : "linear-gradient(135deg, #4CAF50, #45a049)",
  color: "white",
  fontWeight: "bold",
  padding: "12px 24px",
  borderRadius: "25px",
  fontSize: "1rem",
  textTransform: "none",
  border: "none",
  boxShadow: $isSpinning
    ? "0 2px 6px rgba(158, 158, 158, 0.2)"
    : "0 4px 12px rgba(76, 175, 80, 0.3)",
  position: "relative",
  overflow: "hidden",
  cursor: $isSpinning ? "not-allowed" : "pointer",
  transition: "all 0.3s ease",
  "&:hover": {
    background: $isSpinning
      ? "linear-gradient(135deg, #9e9e9e, #757575)"
      : "linear-gradient(135deg, #45a049, #4CAF50)",
    transform: $isSpinning ? "none" : "translateY(-2px)",
    boxShadow: $isSpinning
      ? "0 2px 6px rgba(158, 158, 158, 0.2)"
      : "0 6px 16px rgba(76, 175, 80, 0.4)",
  },
  "&:disabled": {
    background: "linear-gradient(135deg, #9e9e9e, #757575)",
    color: "rgba(255, 255, 255, 0.6)",
    cursor: "not-allowed",
    transform: "none",
    boxShadow: "0 2px 6px rgba(158, 158, 158, 0.2)",
  },
}));

const ClaimButton = styled(MuiButton)({
  background: "linear-gradient(135deg, #4CAF50, #45a049)",
  color: "white",
  fontWeight: "bold",
  padding: "12px 24px",
  borderRadius: "25px",
  fontSize: "1rem",
  textTransform: "none",
  boxShadow: "0 4px 12px rgba(76, 175, 80, 0.3)",
  transition: "all 0.3s ease",
  "&:hover": {
    background: "linear-gradient(135deg, #45a049, #4CAF50)",
    transform: "translateY(-2px)",
    boxShadow: "0 6px 16px rgba(76, 175, 80, 0.4)",
  },
});

const FloatingSparkle = styled(Sparkle, {
  shouldForwardProp: (prop) => !String(prop).startsWith("$"),
})<{ delay: number }>(({ delay }) => ({
  position: "absolute",
  color: "rgba(255, 255, 255, 0.6)",
  animation: `${sparkleFloat} 3s ease-in-out infinite`,
  animationDelay: `${delay}s`,
  zIndex: 1000,
  pointerEvents: "none",
}));

const ResultChip = styled(Chip)(() => ({
  background: "linear-gradient(135deg, #FFD700, #FFA500)",
  color: "white",
  fontWeight: "bold",
  fontSize: "1rem",
  padding: "8px 16px",
  animation: `${bounce} 1s infinite`,
}));

// Default prizes to be used if none are provided via props
const defaultPrizes: SpinWheelPrize[] = [
  { id: "default-1", text: "10% OFF", color: "#FF6B35" },
  { id: "default-2", text: "Try Again", color: "#E0E0E0" },
  { id: "default-3", text: "20% OFF", color: "#F7931E" },
  { id: "default-4", text: "Free Gift", color: "#06FFA5" },
  { id: "default-5", text: "Try Again", color: "#E0E0E0" },
  { id: "default-6", text: "15% OFF", color: "#4D9DE0" },
  { id: "default-7", text: "Try Again", color: "#E0E0E0" },
  { id: "default-8", text: "Jackpot!", color: "#E15FED" },
];

const SpinWheelModule: React.FC<SpinWheelModuleProps> = ({ whatsappNumber, countryCode, headingText, subTextContent, ...props }) => {
  console.log('SpinWheelModule rendering with props:', { whatsappNumber, props });
  
  const prizes =
    props.prizes && props.prizes.length > 0 ? props.prizes : defaultPrizes;
  const displayHeading = headingText || '🎰 Spin to Win!';
  const displaySubText = subTextContent || 'Spin the wheel for amazing prizes and discounts!';
  
  console.log('Prizes being used:', prizes);
  
  // Add error checking
  if (!prizes || prizes.length === 0) {
    return (
      <Container>
        <Typography variant="h6" color="white" textAlign="center">
          No prizes configured for the spin wheel
        </Typography>
      </Container>
    );
  }

  const [isSpinning, setIsSpinning] = useState(false);
  const [result, setResult] = useState<SpinWheelPrize | null>(null);
  const [winningIndex, setWinningIndex] = useState<number | null>(null);
  const [, setShowClaimButton] = useState(false);
  const [hasSpun, setHasSpun] = useState(false);
  const [hasWon, setHasWon] = useState(false);
  const svgRef = useRef<SVGSVGElement>(null);
  const pointerRef = useRef<HTMLDivElement>(null);

  const handleSpin = useCallback(() => {
    if (isSpinning || hasSpun) return;

    setIsSpinning(true);
    setResult(null);
    setShowClaimButton(false);
    setHasSpun(true);

    // Spin by a random angle (multiple full rotations + random offset)
    const spinRotations = 5;
    const randomOffset = Math.random() * 360;
    const finalRotation = spinRotations * 360 + randomOffset;

    // Apply the rotation to the SVG element
    if (svgRef.current) {
      svgRef.current.style.transform = `rotate(${finalRotation}deg)`;
    }

    // Wait for wheel to stop spinning, then determine winner based on pointer position
    setTimeout(() => {
      // Calculate which segment the top pointer (yellow triangle) is touching
      const segmentAngle = 360 / prizes.length;
      // Get the final rotation and normalize it
      const finalAngle = finalRotation % 360;
      // The pointer is at top (0°), calculate which segment it's pointing to
      const pointerAngle = (360 - finalAngle) % 360;
      const winningIndex = Math.floor(pointerAngle / segmentAngle) % prizes.length;
      
      setWinningIndex(winningIndex);
      const winner = prizes[winningIndex];
      setResult(winner);
      setIsSpinning(false);

      // Check if it's a winning result
      const isWinner =
        !winner.text.toLowerCase().includes("try again") &&
        !winner.text.toLowerCase().includes("better luck");
      setHasWon(isWinner);
      setShowClaimButton(isWinner);
    }, 3000);
  }, [isSpinning, hasSpun, prizes]);

  const createSegmentPath = (index: number, total: number, radius: number) => {
    const angle = 360 / total;
    const startAngle = angle * index - 90;
    const endAngle = startAngle + angle;

    const startAngleRad = (startAngle * Math.PI) / 180;
    const endAngleRad = (endAngle * Math.PI) / 180;

    const x1 = radius + radius * Math.cos(startAngleRad);
    const y1 = radius + radius * Math.sin(startAngleRad);
    const x2 = radius + radius * Math.cos(endAngleRad);
    const y2 = radius + radius * Math.sin(endAngleRad);

    const largeArcFlag = angle > 180 ? 1 : 0;

    return `M ${radius},${radius} L ${x1},${y1} A ${radius},${radius} 0 ${largeArcFlag} 1 ${x2},${y2} Z`;
  };

  const getTextPosition = (index: number, total: number, radius: number) => {
    const angle = 360 / total;
    const segmentAngle = (angle * index + angle / 2 - 90) * (Math.PI / 180);
    const textRadius = radius * 0.7; 

    // Add 4 pixels to the textRadius for the x and y calculation
    const adjustedTextRadius = textRadius + 16;

    return {
      x: radius + adjustedTextRadius * Math.cos(segmentAngle),
      y: radius + adjustedTextRadius * Math.sin(segmentAngle),
      rotation: angle * index + angle / 2,
    };
  };

  // In handleClaimReward or similar function:
  const handleClaimReward = () => {
    const cc = countryCode || '+1';
    const wn = whatsappNumber || '1234567890';
    const fullNumber = `${cc}${wn}`.replace(/[^0-9+]/g, '');
    const message = `I just won ${result?.text} on the Spin Wheel! Please help me claim my prize.`;
    const whatsappUrl = `https://wa.me/${fullNumber}?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
  };

  const resetGame = () => {
    setIsSpinning(false);
    setResult(null);
    setShowClaimButton(false);
    setHasSpun(false);
    setHasWon(false);

    // Reset wheel rotation
    if (svgRef.current) {
      svgRef.current.style.transform = "rotate(0deg)";
    }
  };

  return (
    <Container>
      {/* Floating decorative elements */}
      <FloatingSparkle
        delay={0}
        size={20}
        style={{ top: "22px", left: "23px" }}
      />
      <FloatingSparkle
        delay={1}
        size={16}
        style={{ top: "214px", left: "281px" }}
      />
      <FloatingSparkle
        delay={2}
        size={24}
        style={{ top: "349px", left: "23px" }}
      />
      <FloatingSparkle
        delay={1.5}
        size={18}
        style={{ bottom: "10%", right: "10%" }}
      />

      <Typography
        variant="h4"
        sx={{
          fontWeight: "bold",
          color: "white",
          textShadow: "0 2px 4px rgba(0,0,0,0.3)",
          textAlign: "center",
          fontSize: "1.6rem",
          mb: 0.5,
        }}
      >
        {displayHeading}
      </Typography>
      <Typography
        variant="body1"
        sx={{
          color: "white",
          textShadow: "0 1px 2px rgba(0,0,0,0.3)",
          fontSize: "0.8rem",
          textAlign: "center",
          mb: 1,
        }}
      >
        {displaySubText}
      </Typography>

      <WheelContainer>
        <WheelSVG ref={svgRef} viewBox="0 0 220 220" $isSpinning={isSpinning}>
          {prizes.map((prize, index) => {
            const segmentPath = createSegmentPath(index, prizes.length, 110);
            const textPos = getTextPosition(index, prizes.length, 110);
            // Use prize color directly
            const color = prize.color || '#FFD23F';
            // Highlight if this is the winning segment by index
            const isWinner = winningIndex !== null && winningIndex === index;
            return (
              <g key={index}>
                <path
                  d={segmentPath}
                  fill={color}
                  stroke={isWinner ? "#FFD700" : "none"} // Change #ffffff to "none"
                  strokeWidth={isWinner ? 6 : 0} // Change 3 to 0
                  style={isWinner ? { filter: 'drop-shadow(0 0 10px #FFD700)' } : {}}
                  data-index={index}
                />
                <text
                  x={textPos.x}
                  y={textPos.y}
                  fill="white"
                  fontSize="12"
                  fontWeight="bold"
                  textAnchor="start" // Reverted to 'start'
                  transform={`rotate(${textPos.rotation + 90}, ${textPos.x}, ${textPos.y})`}
                  dominantBaseline="middle"
                  style={{ textShadow: "0 1px 2px rgba(0,0,0,0.8)" }}
                >
                  {prize.text}
                </text>
              </g>
            );
          })}
        </WheelSVG>

        <Pointer ref={pointerRef} />
      </WheelContainer>

      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          gap: 2,
          width: "100%",
        }}
      >
        {!hasSpun && !isSpinning && (
          <SpinButton
            onClick={handleSpin}
            disabled={isSpinning}
            $isSpinning={isSpinning}
          >
            Spin the Wheel!
          </SpinButton>
        )}

        {isSpinning && (
          <SpinButton disabled={true} $isSpinning={true}>
            Spinning...
          </SpinButton>
        )}

        {result && (
          <Box
            sx={{
              textAlign: "center",
              animation: `${bounce} 0.8s ease-out`,
              width: "100%",
            }}
          >
            <ResultChip
              label={hasWon ? `🎉 ${result.text} 🎉` : result.text}
              sx={{
                mb: 2,
                background: hasWon
                  ? "linear-gradient(135deg, #FFD700, #FFA500)"
                  : "linear-gradient(135deg, #6b7280, #4b5563)",
              }}
            />
            {hasWon && (
              <ClaimButton onClick={handleClaimReward}>
                <Gift size={20} weight="fill" style={{ marginRight: "8px" }} />
                CLAIM {result?.text ? result.text.toUpperCase() : "REWARD"}
              </ClaimButton>
            )}
            {!hasWon && result && (
              <ClaimButton
                onClick={resetGame}
                sx={{
                  backgroundColor: "#6b7280",
                  "&:hover": { backgroundColor: "#4b5563" },
                }}
              >
                TRY AGAIN
              </ClaimButton>
            )}
          </Box>
        )}
      </Box>
    </Container>
  );
};

export default SpinWheelModule;
