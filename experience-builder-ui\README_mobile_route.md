# Mobile experience route

The mobile-friendly experience is now served by the same Vite app.

• Route: `/m/:experienceId`
• QR code URLs default to `http://localhost:5173/m/<id>` (override with `VITE_MOBILE_RUNTIME_URL`).

Database tables required (already present):
- experiences
- modules
- module_configs
- qr_codes

No additional tables are needed; each module’s JSON config is stored in `module_configs.config`.
