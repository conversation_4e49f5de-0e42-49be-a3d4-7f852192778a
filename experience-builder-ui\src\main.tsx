import './index.css';
import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import { createBrowserRouter, RouterProvider } from 'react-router-dom';
import { ThemeProvider, createTheme, CssBaseline } from '@mui/material';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';

import App from './App.tsx';
import ExperienceView from './pages/ExperienceView';
import IPhoneV3Page from './pages/IPhoneV3Page';
import MobileExperiencePage from './pages/MobileExperiencePage';

const theme = createTheme({
  palette: {
    mode: 'light',
  },
});

// Combined routes from both branches
const router = createBrowserRouter([
  {
    path: '/',
    element: <App />,
  },
  {
    path: '/experience/:id',
    element: <ExperienceView />,
  },
  {
    path: '/iphone-v3',
    element: <IPhoneV3Page />,
  },
  {
    path: '/m/:experienceId',
    element: <MobileExperiencePage />,
  },
]);

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <DndProvider backend={HTML5Backend}>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <RouterProvider router={router} />
      </ThemeProvider>
    </DndProvider>
  </StrictMode>,
);

console.log(import.meta.env.VITE_PUBLIC_BASE_URL);
// should log: "https://1d32-154-70-214-167.ngrok-free.app"