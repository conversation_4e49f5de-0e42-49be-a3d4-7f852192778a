import { type Module, type MediaItem } from "./phone/Screen";
import { NewIPhone } from "./phone/NewIPhone";
import { styled } from "@mui/material/styles";
import { type HeaderConfig } from "./BackgroundSelector";
import { memo, useMemo } from "react";

interface MobilePreviewProps {
  experienceName: string;
  modules: Module[];
  backgroundMedia?: MediaItem | null;
  currentScreen: number;
  totalScreens: number;
  onScreenChange: (idx: number) => void;
  onUpdateModuleContent?: (
    moduleId: string,
    updatedContentPart: Partial<Module["content"]>
  ) => void;
  headerConfig?: HeaderConfig;
}

const PhoneContainer = styled("div")({
  position: "relative",
  width: "100%",
  display: "flex",
  flexDirection: "column",
  alignItems: "center",
  justifyContent: "center",
  minHeight: "600px",
  background: "transparent",
});

// Memoize the entire component
const MobilePreview = memo<MobilePreviewProps>(({ 
  modules, 
  backgroundMedia, 
  currentScreen, 
  totalScreens, 
  onScreenChange, 
  onUpdateModuleContent, 
  headerConfig 
}) => {
  // Memoize expensive calculations
  const memoizedModules = useMemo(() => modules, [modules]);
  
  return (
    <PhoneContainer>
      <NewIPhone
        modules={memoizedModules}
        backgroundMedia={backgroundMedia}
        currentScreen={currentScreen}
        totalScreens={totalScreens}
        onScreenChange={onScreenChange}
        onUpdateModuleContent={onUpdateModuleContent}
        headerConfig={headerConfig}
      />
    </PhoneContainer>
  );
});

// Add the missing default export
export default MobilePreview;
