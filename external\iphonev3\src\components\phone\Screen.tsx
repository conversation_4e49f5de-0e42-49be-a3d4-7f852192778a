import React, { useState, lazy, Suspense } from 'react';

const SpinWheelModule = lazy(() => import('@components/modules/SpinWheelModule'));



// --- Copied from App.tsx --- //
export interface SetupStep {
  id: string;
  description: string;
}

export interface SetupGuideContent {
  steps: SetupStep[];
}

export interface WarrantyContent {
  productName: string;
  productModel?: string;
  serialNumber: string;
  purchaseDate: string;
  warrantyLength: string;
  customerName: string;
  email: string;
  phone?: string;
  retailerName?: string;
  orderNumber?: string;
  receiptFile?: File | null;
  receiptPreviewUrl?: string;
  isTermsAccepted: boolean;
  registrationDate?: string;
  warrantyCode?: string;
  notes?: string;
  isSerialNumberValid?: boolean;
  isSubmitting?: boolean;
  submitError?: string;
}

export interface ShoppingLink {
  id: string;
  url: string;
  displayText: string;
}

export interface ShoppingLinksContent {
  title?: string;
  links: ShoppingLink[];
}

export interface SurveyQuestion {
  id: string;
  questionText: string;
}

export interface FeedbackSurveyContent {
  title?: string;
  questions: SurveyQuestion[];
}

export interface CustomButtonContent {
  text: string;
  url: string;
  textColor: string;
  buttonColor: string;
  borderColor: string;
}

export interface RegistrationModuleContent {
  collectPurchaseDetails: boolean;
  purchaseFields?: { [key: string]: { required?: boolean } };
  requireReceiptApproval?: boolean;
  disableCompleteProfileScreen?: boolean;
  enableMultipleRegistrations?: boolean;
  formData?: Record<string, any>;
}

export interface FormSurveyModuleContent {
  surveyDefinitionUrl?: string;
  title: string;
}

export interface DiscountModuleContent {
  shopifyDiscountCode: string;
  destinationPageUrl: string;
  productInfo?: string;
  discountTerms?: string;
}

export interface IframeModuleContent {
  embedType: 'url' | 'html';
  source: string;
  customAttributes?: string;
}

export interface DocumentModuleContent {
  documentName: string;
  documentUrl: string;
}

export interface LinkModuleContent {
  destinationUrl: string;
  linkText?: string;
}

export interface VideoModuleContent {
  sourceType: 'url' | 'embed' | 'upload';
  source: string;
  autoplay?: boolean;
}

export interface SpinWheelModuleContent {
  // For now, content is self-contained within the component
}

export type Module =
  | { id: string; screen?: number; title: string; type: 'SetupGuide'; content: SetupGuideContent }
  | { id: string; screen?: number; title: string; type: 'Warranty'; content: WarrantyContent }
  | { id: string; screen?: number; title: string; type: 'ShoppingLinks'; content: ShoppingLinksContent }
  | { id: string; screen?: number; title: string; type: 'FeedbackSurvey'; content: FeedbackSurveyContent }
  | { id: string; screen?: number; title: string; type: 'CustomButton'; content: CustomButtonContent }
  | { id: string; screen?: number; title: string; type: 'Registration'; content: RegistrationModuleContent }
  | { id: string; screen?: number; title: string; type: 'FormSurvey'; content: FormSurveyModuleContent }
  | { id:string; screen?: number; title: string; type: 'Discount'; content: DiscountModuleContent }
  | { id: string; screen?: number; title: string; type: 'Iframe'; content: IframeModuleContent }
  | { id: string; screen?: number; title: string; type: 'Document'; content: DocumentModuleContent }
  | { id: string; screen?: number; title: string; type: 'Link'; content: LinkModuleContent }
  | { id: string; screen?: number; title: string; type: 'Video'; content: VideoModuleContent }
  | { id: string; screen?: number; title: string; type: 'SpinWheel'; content: SpinWheelModuleContent };
// --- End of copied types --- //

export interface MediaItem {
  id: string;
  url: string;
  type: 'image' | 'video';
}

interface ScreenProps {
  backgroundMedia?: MediaItem | null;
  modules?: Module[];
  currentScreen?: number;
  totalScreens?: number;
  onScreenChange?: (screenIndex: number) => void;
}

const moduleButtonLabel: Record<string, string> = {
  'SpinWheel': 'SPIN THE WHEEL!',
  'Warranty': 'ACTIVATE WARRANTY',
  'Setup Guide': 'QUICK START GUIDE',
  'Registration': 'REGISTER PRODUCT',
  'FeedbackSurvey': 'FEEDBACK',
  'ShoppingLinks': 'SHOP ACCESSORIES',
  'CustomButton': 'CUSTOM BUTTON',
};

const renderModuleContent = (module: Module) => {
  switch (module.type) {
    case 'Warranty':
      return (
        <div>
          <h4 className="font-semibold text-sm mb-2">{module.content.productName || 'Warranty Registration'}</h4>
          <p className="text-xs text-gray-600">Please fill out the form to register your product warranty.</p>
          <p className="mt-1 text-xs italic">Warranty Length: {module.content.warrantyLength}</p>
        </div>
      );
    case 'Registration':
      return (
        <div>
          <h4 className="font-semibold text-sm mb-2">Product Registration</h4>
          <p className="text-xs text-gray-600">Complete the fields below to register.</p>
          {module.content.collectPurchaseDetails && (
             <p className="text-xs text-gray-500 mt-1 italic">Purchase details will be collected.</p>
          )}
        </div>
      );
    case 'SetupGuide':
        return (
            <div>
                <h4 className="font-semibold text-sm mb-2">Setup Guide</h4>
                <ul className="list-disc list-inside text-xs text-gray-600 space-y-1">
                    {module.content.steps.slice(0, 3).map(step => <li key={step.id}>{step.description}</li>)}
                    {module.content.steps.length > 3 && <li className="italic">...and more</li>}
                </ul>
            </div>
        );
    case 'ShoppingLinks':
      return (
        <div>
          <h4 className="font-semibold text-sm mb-2">{module.content.title || 'Shop Now'}</h4>
          <ul className="list-disc list-inside text-xs text-blue-600 space-y-1">
            {module.content.links.map(link => (
              <li key={link.id}>
                <a href={link.url} target="_blank" rel="noopener noreferrer" className="underline hover:text-blue-800">
                  {link.displayText}
                </a>
              </li>
            ))}
          </ul>
        </div>
      );

    case 'SpinWheel':
      return <Suspense fallback={<div>Loading...</div>}><SpinWheelModule /></Suspense>;
    case 'FeedbackSurvey':
      return (
        <div>
          <h4 className="font-semibold text-sm mb-2">{module.content.title || 'Feedback Survey'}</h4>
          <p className="text-xs text-gray-600 mb-2">Let us know what you think:</p>
          <ul className="list-decimal list-inside text-xs text-gray-700 space-y-1">
            {module.content.questions.map(q => (
              <li key={q.id}>{q.questionText}</li>
            ))}
          </ul>
        </div>
      );
    default:
      return <p className="text-sm text-gray-700">Content for {module.title} module will appear here.</p>;
  }
};


export const Screen: React.FC<ScreenProps> = ({
  backgroundMedia,
  modules = [],
  currentScreen = 0,
  totalScreens = 1,
  onScreenChange,
}) => {
  console.log('Screen component received backgroundMedia:', backgroundMedia); // Added logging
  const [expandedModuleId, setExpandedModuleId] = useState<string | null>(null);
  const expandedModule = modules.find(m => m.id === expandedModuleId);

  const mediaType = backgroundMedia?.type;
  const heroSrc =
    backgroundMedia?.url ||
    'https://images.unsplash.com/photo-1606815067878-1c2363ac01c3?auto=format&fit=crop&w=800&q=80';

  const handleModuleToggle = (moduleId: string) => {
    setExpandedModuleId((prevId) => (prevId === moduleId ? null : moduleId));
  };

  const handleScreenAdvance = () => {
    if (onScreenChange && currentScreen < totalScreens - 1) {
      onScreenChange(currentScreen + 1);
    }
  };

  const handleBackClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onScreenChange && currentScreen > 0) {
      onScreenChange(currentScreen - 1);
    }
  };

  const isFirstScreen = currentScreen === 0;

  return (
    <main className="w-[385px] h-[819px] absolute overflow-hidden rounded-[52px] left-3 top-3 bg-white">
      {!isFirstScreen && (
        <button
          onClick={handleBackClick}
          className="absolute top-4 left-4 z-20 w-10 h-10 flex items-center justify-center bg-white bg-opacity-80 rounded-full shadow-md hover:bg-opacity-100 transition-all"
        >
          <span className="text-black text-xl">‹</span>
        </button>
      )}

      <div className="absolute inset-0 w-full h-full overflow-hidden">
        {mediaType === 'video' ? (
          <video
            key={heroSrc} // Add key to ensure re-render on src change
            src={heroSrc}
            autoPlay
            loop
            muted
            playsInline
            className="w-full h-full object-cover"
            onError={(e) => {
              const videoElement = e.target as HTMLVideoElement;
              const error = videoElement.error;
              console.error('Video playback error:', e);
              if (error) {
                console.error(`MediaError code: ${error.code}, message: ${error.message}`);
              } else {
                console.error('Video playback error: No MediaError object available on target.');
              }
            }}
          />
        ) : (
          <img src={heroSrc} alt="" className="w-full h-full object-cover" />
        )}
      </div>

      <div 
        className="absolute inset-x-0 bottom-0 bg-white rounded-t-[16px] shadow-lg p-6 flex flex-col z-10" // Added flex flex-col, removed space-y-4
        style={{ maxHeight: 'calc(60vh - env(safe-area-inset-top, 0px) - env(safe-area-inset-bottom, 0px))' }} // Reduced to 60vh and adjusted for safe areas, removed overflowY
      >
        <div className="w-12 h-1 bg-gray-300 rounded-full mx-auto mb-4 flex-shrink-0" /> {/* Added mb-4, flex-shrink-0 */}

        <div className="flex justify-between items-start mb-4 flex-shrink-0"> {/* Added mb-4, flex-shrink-0 */}
          <div>
            <h2 className="text-sm font-medium text-gray-900">Humidifier</h2>
            <p className="text-xs text-gray-500">White</p>
          </div>
          <div className="text-right cursor-pointer" onClick={handleScreenAdvance}>
            <p className="text-xs text-gray-500">Screen {currentScreen + 1} of {totalScreens}</p>
            <p className="text-sm font-medium text-gray-900">{isFirstScreen ? 'Get Started' : 'Continue'}</p>
          </div>
        </div>

        <div className="w-full bg-gray-200 rounded-full h-1.5 mb-4 flex-shrink-0"> {/* Added mb-4, flex-shrink-0 */}
          <div 
            className="bg-black h-1.5 rounded-full transition-all duration-300" 
            style={{ width: `${((currentScreen + 1) / totalScreens) * 100}%` }}
          />
        </div>

        {/* New scrollable container for buttons and expanded content */}
        <div className="flex-grow flex-shrink min-h-0 overflow-y-auto space-y-3 scrollbar-hide">
          {modules.map((m) => {
            if (m.type === 'CustomButton') {
              const { text, url, textColor, buttonColor, borderColor } = m.content;
              return (
                <a
                  key={m.id}
                  href={url || '#'}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="block w-full text-sm font-semibold py-3 rounded-full transition-colors text-center no-underline"
                  style={{
                    color: textColor,
                    backgroundColor: buttonColor,
                    border: `1px solid ${borderColor}`,
                  }}
                >
                  {text}
                </a>
              );
            }

            const label = moduleButtonLabel[m.type] || m.title;
            return (
              <React.Fragment key={m.id}>
                <button
                  onClick={() => handleModuleToggle(m.id)}
                  className={
                  `w-full text-sm font-semibold py-3 rounded-full transition-colors
                  ${isFirstScreen && m.type === 'Warranty' 
                    ? 'bg-black text-white hover:bg-gray-800' 
                    : 'border border-black text-black hover:bg-gray-100'}`
                }
              >
                {label.toUpperCase()}
                </button>
                {expandedModuleId === m.id && (
                  <div 
                    className={
                      m.type === 'SpinWheel'
                        ? "mt-2" // Minimal styling for game containers
                        : "p-3 mt-2 border border-gray-200 rounded-md bg-gray-50 transition-all duration-300 ease-in-out"
                    }
                    // style prop removed from here, maxHeight and overflowY are now on the parent panel
                  >
                    {renderModuleContent(m)}
                  </div>
                )}
              </React.Fragment>
            );
          })}
        </div> {/* End of new scrollable container */}
      </div>
    </main>
  );
};
