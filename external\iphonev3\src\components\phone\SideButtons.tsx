import React from 'react';

export const SideButtons: React.FC = () => {
  return (
    <div className="w-[415px] h-[669px] absolute left-[-3px] top-[149px]">
      {/* Power Button */}
      <div className="absolute w-[5px] h-[100px] left-[410px] top-[97px]">
        <div className="w-full h-full absolute bg-[#F0ECE3] rounded-[2px_0px_0px_2px] left-0 top-0" />
        <div className="w-full h-full absolute opacity-[0.76] rounded-[2px_0px_0px_2px] left-0 top-0" />
        <div className="w-full h-full absolute rounded-[2px_0px_0px_2px] left-0 top-0" />
        <div className="w-full h-full absolute opacity-[0.48] rounded-[2px_0px_0px_2px] left-0 top-0" />
        <div className="blur-[0.5px] absolute w-px h-[19px] bg-white rounded-[1px_1px_0px_0px] left-[3px] top-0.5" />
        <div className="blur-[0.5px] absolute w-px h-[91px] bg-[rgba(255,255,255,0.80)] rounded-[0px_0px_1px_1px] left-[3px] top-0.5" />
      </div>

      {/* Mute Switch */}
      <div className="absolute w-[5px] h-[31px] left-0 top-0">
        <div className="w-full h-full absolute bg-[#F0ECE3] rounded-[2px_0px_0px_2px] left-0 top-0" />
        <div className="w-full h-full absolute opacity-[0.76] rounded-[2px_0px_0px_2px] left-0 top-0" />
        <div className="w-full h-full absolute rounded-[2px_0px_0px_2px] left-0 top-0" />
        <div className="w-full h-full absolute opacity-[0.48] rounded-[2px_0px_0px_2px] left-0 top-0" />
        <div className="blur-[0.5px] absolute w-px h-[19px] bg-white rounded-[1px_1px_0px_0px] left-px top-0.5" />
        <div className="blur-[0.5px] absolute w-px h-[91px] bg-[rgba(255,255,255,0.80)] rounded-[0px_0px_1px_1px] left-px top-0.5" />
      </div>

      {/* Volume Up */}
      <div className="absolute w-[5px] h-14 left-0 top-[60px]">
        <div className="w-full h-full absolute bg-[#F0ECE3] rounded-[2px_0px_0px_2px] left-0 top-0" />
        <div className="w-full h-full absolute opacity-[0.76] rounded-[2px_0px_0px_2px] left-0 top-0" />
        <div className="w-full h-full absolute rounded-[2px_0px_0px_2px] left-0 top-0" />
        <div className="w-full h-full absolute opacity-[0.48] rounded-[2px_0px_0px_2px] left-0 top-0" />
        <div className="blur-[0.5px] absolute w-px h-[19px] bg-white rounded-[1px_1px_0px_0px] left-px top-0.5" />
        <div className="blur-[0.5px] absolute w-px h-[91px] bg-[rgba(255,255,255,0.80)] rounded-[0px_0px_1px_1px] left-px top-0.5" />
      </div>

      {/* Volume Down */}
      <div className="absolute w-[5px] h-14 left-0 top-[133px]">
        <div className="w-full h-full absolute bg-[#F0ECE3] rounded-[2px_0px_0px_2px] left-0 top-0" />
        <div className="w-full h-full absolute opacity-[0.76] rounded-[2px_0px_0px_2px] left-0 top-0" />
        <div className="w-full h-full absolute rounded-[2px_0px_0px_2px] left-0 top-0" />
        <div className="w-full h-full absolute opacity-[0.48] rounded-[2px_0px_0px_2px] left-0 top-0" />
        <div className="blur-[0.5px] absolute w-px h-[19px] bg-white rounded-[1px_1px_0px_0px] left-px top-0.5" />
        <div className="blur-[0.5px] absolute w-px h-[91px] bg-[rgba(255,255,255,0.80)] rounded-[0px_0px_1px_1px] left-px top-0.5" />
      </div>
    </div>
  );
};
