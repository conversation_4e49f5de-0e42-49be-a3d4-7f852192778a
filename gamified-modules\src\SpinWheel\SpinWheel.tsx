import { motion, useAnimation } from 'framer-motion';
import React, { useState } from 'react';

export interface SpinWheelProps {
  segments: string[];
  onComplete?: (selected: string) => void;
}

const SpinWheel: React.FunctionComponent<SpinWheelProps> = ({ segments, onComplete }: SpinWheelProps) => {
  const controls = useAnimation();
  const [spinning, setSpinning] = useState(false);

  const spin = async () => {
    if (spinning) return;
    setSpinning(true);
    const rotations = 5 + Math.random() * 3;
    await controls.start({ rotate: 360 * rotations, transition: { duration: 4, ease: 'easeOut' } });
    const index = Math.floor(Math.random() * segments.length);
    onComplete?.(segments[index]);
    setSpinning(false);
  };

  return (
    <div className="flex flex-col items-center">
      <motion.div
        className="w-40 h-40 rounded-full border-4 border-indigo-500 flex items-center justify-center text-sm text-center select-none"
        animate={controls}
        onClick={spin}
      >
        Spin
      </motion.div>
    </div>
  );
};

export default SpinWheel;
