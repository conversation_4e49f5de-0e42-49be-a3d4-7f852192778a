// Test script to debug media upload issues
import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';

// Load environment variables
const supabaseUrl = 'https://jqaqkymjacdnllytexou.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpxYXFreW1qYWNkbmxseXRleG91Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcwNDM5MDYsImV4cCI6MjA2MjYxOTkwNn0.LoJMnX2qO945At_Gebd7khYGsttudBJfiiC-XzM3-8I';

const supabase = createClient(supabaseUrl, supabaseKey);

async function testUpload() {
  console.log('Testing Supabase connection...');
  
  // Test 1: Check if we can connect to Supabase
  try {
    const { data, error } = await supabase.from('experiences').select('count');
    if (error) {
      console.error('❌ Supabase connection failed:', error);
      return;
    }
    console.log('✅ Supabase connection successful');
  } catch (err) {
    console.error('❌ Supabase connection error:', err);
    return;
  }

  // Test 2: Check storage bucket
  try {
    const { data: buckets, error: bucketError } = await supabase.storage.listBuckets();
    if (bucketError) {
      console.error('❌ Storage bucket check failed:', bucketError);
      return;
    }
    console.log('✅ Storage buckets:', buckets.map(b => b.name));
    
    const experienceMediaBucket = buckets.find(b => b.name === 'experience-media');
    if (!experienceMediaBucket) {
      console.error('❌ experience-media bucket not found');
      return;
    }
    console.log('✅ experience-media bucket found:', experienceMediaBucket);
  } catch (err) {
    console.error('❌ Storage bucket error:', err);
    return;
  }

  // Test 3: Try to upload a simple test file
  try {
    const testContent = 'This is a test file for debugging upload issues';
    const testFile = new Blob([testContent], { type: 'text/plain' });
    const testPath = `test/debug-${Date.now()}.txt`;
    
    console.log('Testing file upload...');
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('experience-media')
      .upload(testPath, testFile, { 
        contentType: 'text/plain',
        upsert: true 
      });
    
    if (uploadError) {
      console.error('❌ Upload failed:', uploadError);
      return;
    }
    
    console.log('✅ Upload successful:', uploadData);
    
    // Test 4: Try to get public URL
    const { data: urlData } = supabase.storage
      .from('experience-media')
      .getPublicUrl(testPath);
    
    console.log('✅ Public URL:', urlData.publicUrl);
    
  } catch (err) {
    console.error('❌ Upload test error:', err);
  }

  // Test 5: Check media_library table
  try {
    const { data: mediaData, error: mediaError } = await supabase
      .from('media_library')
      .select('*')
      .limit(5);
    
    if (mediaError) {
      console.error('❌ Media library query failed:', mediaError);
      return;
    }
    
    console.log('✅ Media library accessible, records:', mediaData.length);
  } catch (err) {
    console.error('❌ Media library error:', err);
  }
}

testUpload().catch(console.error);
