import { createClient } from '@supabase/supabase-js';
import type { Database } from './types/database.types';
import type { Module } from '../App';

// Get environment variables
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

// Debug environment variables
console.log('Supabase Environment Check:');
console.log('URL:', supabaseUrl ? 'Present' : 'MISSING');
console.log('Key:', supabaseAnonKey ? 'Present' : 'MISSING');

// Check if environment variables are set
if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ CRITICAL: Missing Supabase environment variables!');
  console.error('URL:', supabaseUrl);
  console.error('Key:', supabaseAnonKey);
  throw new Error('Supabase environment variables are not configured properly');
}

// Create the Supabase client
export const supabase = createClient<Database>(
  supabaseUrl, 
  supabaseAnonKey
);

/**
 * Save an experience to the database
 * @param name Experience name
 * @param backgroundImage URL of the background image
 * @param modules Array of modules for the experience
 * @returns The created experience including its ID
 */
export interface SavedExperience {
  experience_id: string;
  name: string;
  created_at: string;
  updated_at: string;
  
  status: string;
}

export const saveExperience = async (
  name: string,
  backgroundMediaId: string | null,
  modules: Module[],
  headerConfig?: { appName: string; appSubtitle: string; getStartedButtonText: string },
): Promise<SavedExperience> => {
  try {
    // Insert the experience record with header config
    const { data: experience, error: experienceError } = await supabase
      .from('experiences')
      .insert({
        name,
        status: 'draft',
        app_name: headerConfig?.appName || null,
        app_subtitle: headerConfig?.appSubtitle || null,
        get_started_button_text: headerConfig?.getStartedButtonText || null,
      })
      .select('*')
      .single();

    if (experienceError) {
      console.error('Error inserting experience:', JSON.stringify(experienceError, null, 2));
      throw experienceError;
    }
    
    if (!experience) {
      throw new Error('Failed to create experience record');
    }

    // 1. create default screen (index 0)
    // Insert default screen (cast to any to bypass missing generated types)
    const { data: screenRow, error: screenError } = await supabase
      .from('screens' as any)
      .insert({
        experience_id: experience.experience_id,
        screen_index: 0,
        background_media_id: backgroundMediaId,
      })
      .select('screen_id')
      .single();
    if (screenError) throw screenError;
    const screenId = (screenRow as any)?.screen_id;

    // 2. Insert all modules with their display order and screen reference
    const modulesWithExperienceId = modules.map((module, index) => {
      // Convert module content to a stringified JSON that can be stored
      const content = JSON.stringify(module.content ?? null);
      
      return {
        experience_id: experience.experience_id,
        screen_id: screenId,
        type: module.type,
        title: module.title,
        display_order: index,
        content
      };
    });

    if (modulesWithExperienceId.length > 0) {
      const { error: modulesError } = await supabase
        .from('modules')
        .insert(modulesWithExperienceId);

      if (modulesError) {
        console.error('Error inserting modules:', modulesError);
        throw modulesError;
      }
    }

    // Generate and save QR code entry
    const { error: qrError } = await supabase
      .from('qr_codes')
      .insert({
        experience_id: experience.experience_id
      });
    
    if (qrError) {
      console.error('Error inserting QR code:', qrError);
      throw qrError;
    }

    return experience as SavedExperience;
  } catch (error) {
    console.error('Error saving experience:', error);
    throw error;
  }
};

/**
 * Generate QR code URL for an experience
 * @param experienceId UUID of the experience
 * @returns URL to access the QR code
 */
export const generateQrCodeUrl = (experienceId: string): string => {
  const baseUrl = 'https://web-app-experience.netlify.app';
  return `${baseUrl}/experience/${experienceId}`;
};

/**
 * Update QR code URL in the database
 * @param experienceId UUID of the experience
 * @param qrImageUrl URL of the generated QR code image
 */
export const updateQrCodeImage = async (experienceId: string, qrImageUrl: string) => {
  const { error } = await supabase
    .from('qr_codes')
    .update({ qr_image_url: qrImageUrl })
    .eq('experience_id', experienceId);
  
  if (error) {
    console.error('Error updating QR code:', error);
    throw error;
  }
};

/**
 * Get an experience by its ID
 * @param experienceId UUID of the experience
 * @returns The experience data with its modules
 */
export const getExperience = async (experienceId: string) => {
  // Get the experience record with explicit header config fields - force fresh data
  const { data: experience, error: experienceError } = await supabase
    .from('experiences')
    .select('*, app_name, app_subtitle, get_started_button_text')
    .eq('experience_id', experienceId)
    .order('updated_at', { ascending: false })
    .limit(1)
    .single();
    
  console.log('DEBUG getExperience: Raw experience data:', experience);
  console.log('DEBUG getExperience: Header fields:', {
    app_name: experience?.app_name,
    app_subtitle: experience?.app_subtitle,
    get_started_button_text: experience?.get_started_button_text
  });
  
  if (experienceError) {
      console.error('Supabase experience insert error:', JSON.stringify(experienceError, null, 2));
      throw experienceError;
    }
  
  // Get screens
  const { data: screens, error: screensError } = await supabase
    .from('screens' as any)
    .select(`screen_id, screen_index, background_media_id`)
    .eq('experience_id', experienceId)
    .order('screen_index');
  if (screensError) throw screensError;

  // Fetch media rows associated with screens to resolve paths in one query
  const mediaIds = (screens ?? []).map((s: any) => s.background_media_id).filter(Boolean);
  let mediaMap: Record<string, any> = {};
  if (mediaIds.length) {
    const { data: mediaRows, error: mediaErr } = await supabase
      .from('media_library' as any)
      .select('media_id, path, mime_type')
      .in('media_id', mediaIds as any);
    if (mediaErr) {
      console.warn('Media library query failed, using fallback:', mediaErr);
    } else {
      mediaMap = Object.fromEntries((mediaRows ?? []).map((m: any) => [m.media_id, m]));
    }
  }
  const screensWithMedia = (screens ?? []).map((s: any) => ({
    ...s,
    media: mediaMap[s.background_media_id] ?? null,
    // Add fallback for background image if media_library is empty
    background_image: s.background_media_id || null,
  }));

  // Get all modules for this experience
  const { data: modules, error: modulesError } = await supabase
    .from('modules')
    .select('*')
    .eq('experience_id', experienceId)
    .order('display_order', { ascending: true });
  
  if (modulesError) throw modulesError;

  // Increment the access count for the QR code
  // We'll do a simple increment without using RPC until we create the function
  const { data: qrCode, error: getQrError } = await supabase
    .from('qr_codes')
    .select('access_count')
    .eq('experience_id', experienceId)
    .single();
    
  if (getQrError) {
    console.error('Error fetching QR code access count:', getQrError);
  }
  
  const currentCount = qrCode?.access_count || 0;
  
  const { error: qrError } = await supabase
    .from('qr_codes')
    .update({ 
      access_count: currentCount + 1,
      last_accessed: new Date().toISOString()
    })
    .eq('experience_id', experienceId);
  
  if (qrError) console.error('Error updating QR code access:', qrError);
  
  return {
    ...experience,
    screens: screensWithMedia,
    modules,
  };
};

/**
 * Save CSAT response data to the analytics table
 * @param experienceId UUID of the experience
 * @param moduleId ID of the CSAT module
 * @param responseData CSAT response data including ratings and feedback
 * @param sessionId Session identifier for the user
 * @param userAgent User agent string
 * @param ipAddress IP address of the user
 * @returns The created analytics record
 */
export interface CSATResponse {
  csatRating?: number;
  npsScore?: number;
  starRating?: number;
  followUp?: string;
  timestamp: string;
  [key: string]: any; // For additional survey responses
}

export const saveCSATResponse = async (
  experienceId: string,
  moduleId: string,
  responseData: CSATResponse,
  sessionId: string = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
  userAgent: string | null = null,
  ipAddress: string | null = null
) => {
  try {
    const { data: analyticsRecord, error } = await supabase
      .from('analytics')
      .insert({
        experience_id: experienceId,
        session_id: sessionId,
        event_type: 'csat_response',
        event_data: {
          ...responseData,
          module_id: moduleId,
        },
        user_agent: userAgent,
        ip_address: ipAddress,
      })
      .select('*')
      .single();

    if (error) {
      console.error('Error saving CSAT response:', error);
      throw error;
    }

    console.log('CSAT response saved successfully:', analyticsRecord);
    return analyticsRecord;
  } catch (error) {
    console.error('Failed to save CSAT response:', error);
    throw error;
  }
};
