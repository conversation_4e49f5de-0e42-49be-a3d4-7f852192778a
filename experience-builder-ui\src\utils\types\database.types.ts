export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          created_at: string | null
          updated_at: string | null
          // Add other profile fields as needed
        }
        Insert: {
          id: string
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: []
      },
      experiences: {
        Row: {
          experience_id: string
          name: string
          created_at: string
          updated_at: string
          background_image: string | null
          status: 'draft' | 'published' | 'archived'
          app_name: string | null
          app_subtitle: string | null
          get_started_button_text: string | null
        }
        Insert: {
          experience_id?: string
          name: string
          created_at?: string
          updated_at?: string
          background_image?: string | null
          status?: 'draft' | 'published' | 'archived'
          app_name?: string | null
          app_subtitle?: string | null
          get_started_button_text?: string | null
        }
        Update: {
          experience_id?: string
          name?: string
          created_at?: string
          updated_at?: string
          background_image?: string | null
          status?: 'draft' | 'published' | 'archived'
          app_name?: string | null
          app_subtitle?: string | null
          get_started_button_text?: string | null
        }
        Relationships: []
      },
      modules: {
        Row: {
          module_id: string
          experience_id: string
          type: string
          title: string
          display_order: number
          created_at: string
          content: Json
        }
        Insert: {
          module_id?: string
          experience_id: string
          type: string
          title: string
          display_order: number
          created_at?: string
          content?: Json
        }
        Update: {
          module_id?: string
          experience_id?: string
          type?: string
          title?: string
          display_order?: number
          created_at?: string
          content?: Json
        }
        Relationships: [
          {
            foreignKeyName: "modules_experience_id_fkey"
            columns: ["experience_id"]
            referencedRelation: "experiences"
            referencedColumns: ["experience_id"]
          }
        ]
      },
      qr_codes: {
        Row: {
          qr_id: string
          experience_id: string
          qr_image_url: string | null
          created_at: string
          last_accessed: string | null
          access_count: number
        }
        Insert: {
          qr_id?: string
          experience_id: string
          qr_image_url?: string | null
          created_at?: string
          last_accessed?: string | null
          access_count?: number
        }
        Update: {
          qr_id?: string
          experience_id?: string
          qr_image_url?: string | null
          created_at?: string
          last_accessed?: string | null
          access_count?: number
        }
        Relationships: [
          {
            foreignKeyName: "qr_codes_experience_id_fkey"
            columns: ["experience_id"]
            referencedRelation: "experiences"
            referencedColumns: ["experience_id"]
          }
        ]
      },
      analytics: {
        Row: {
          analytics_id: string
          experience_id: string
          session_id: string
          event_type: string
          event_data: Json | null
          created_at: string
          user_agent: string | null
          ip_address: string | null
        }
        Insert: {
          analytics_id?: string
          experience_id: string
          session_id: string
          event_type: string
          event_data?: Json | null
          created_at?: string
          user_agent?: string | null
          ip_address?: string | null
        }
        Update: {
          analytics_id?: string
          experience_id?: string
          session_id?: string
          event_type?: string
          event_data?: Json | null
          created_at?: string
          user_agent?: string | null
          ip_address?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "analytics_experience_id_fkey"
            columns: ["experience_id"]
            referencedRelation: "experiences"
            referencedColumns: ["experience_id"]
          }
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      increment_counter: {
        Args: { row_id: string }
        Returns: number
      }
      update_timestamp: {
        Args: Record<string, never>
        Returns: unknown
      }
    }
    Enums: {
      [_ in never]: never
    }
  }
}
