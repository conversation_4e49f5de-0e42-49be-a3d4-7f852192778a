import React, { useState, useEffect, useMemo, useRef } from 'react';
import { TextField, Button, Typography, Grid, Box, Select, MenuItem, FormControl, InputLabel, Card, CardContent, IconButton } from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';
import WhatsAppIcon from '@mui/icons-material/WhatsApp';
import type { ScratchAreaData } from './ScratchCardModule';
import type { ScratchCardModuleContent } from '../../App'; // Use the stricter content type from App
import CountryCodeSelector from '../common/CountryCodeSelector';


interface ScratchCardEditorProps {
  initialContent: Partial<ScratchCardModuleContent>;
  onContentChange: (newContent: ScratchCardModuleContent) => void;
}

const ScratchCardEditor: React.FC<ScratchCardEditorProps> = ({ initialContent, onContentChange }) => {
  const prevContentPayloadRef = useRef<string | null>(null);
  const [headingText, setHeadingText] = useState(initialContent.headingText || '🎯 Scratch & Win!');
  const [subTextContent, setSubTextContent] = useState(initialContent.subTextContent || 'Scratch the circles to reveal your discount!\nScratch enough area to see if you won. First winner ends the game!');
  
  const [whatsappNumber, setWhatsappNumber] = useState(initialContent.whatsappNumber || '');
  const [countryCode, setCountryCode] = useState(initialContent.countryCode || '+1');
  const [scratchAreas, setScratchAreas] = useState<ScratchAreaData[]>(initialContent.initialScratchAreas || [
    { id: 'area-1', scratchText: 'SCRATCH', revealedTextLine1: '10% OFF', revealedTextLine2: 'Congrats!', rewardType: 'discount', rewardValue: '10%', isRevealed: false },
    { id: 'area-2', scratchText: 'SCRATCH', revealedTextLine1: 'TRY AGAIN', revealedTextLine2: 'Oops!', rewardType: 'failure', rewardValue: '0%', isRevealed: false },
    { id: 'area-3', scratchText: 'SCRATCH', revealedTextLine1: '20% OFF', revealedTextLine2: 'You Won!', rewardType: 'discount', rewardValue: '20%', isRevealed: false },
  ]);

  // Remove handleEditWhatsapp, handleSaveWhatsapp, handleCancelWhatsapp, isEditingWhatsapp, localWhatsappNumber, localCountryCode

  // Memoize the content payload to ensure it only changes when its data dependencies change
  const contentPayload = useMemo((): ScratchCardModuleContent => ({
    headingText,
    subTextContent,

    whatsappNumber,
    countryCode,
    initialScratchAreas: scratchAreas,
  }), [headingText, subTextContent,  whatsappNumber, countryCode, scratchAreas]);
  useEffect(() => {
    const currentPayloadString = JSON.stringify(contentPayload);
    if (prevContentPayloadRef.current !== currentPayloadString) {
      onContentChange(contentPayload);
      prevContentPayloadRef.current = currentPayloadString;
    }
  }, [contentPayload, onContentChange]);

  const handleScratchAreaChange = (index: number, field: keyof ScratchAreaData, value: string | boolean) => {
    const updatedAreas = scratchAreas.map((area, i) => {
      if (i === index) {
        return { ...area, [field]: value };
      }
      return area;
    });
    setScratchAreas(updatedAreas);
  };

  const addScratchArea = () => {
    setScratchAreas([
      ...scratchAreas,
      {
        id: `area-${Date.now()}`,
        scratchText: 'NEW',
        revealedTextLine1: 'Prize',
        revealedTextLine2: 'Details',
        rewardType: 'discount',
        rewardValue: '5%',
        isRevealed: false,
      },
    ]);
  };

  const removeScratchArea = (index: number) => {
    setScratchAreas(scratchAreas.filter((_, i) => i !== index));
  };

  return (
    <Box sx={{ p: 2 }}>
      <Typography variant="h6" gutterBottom sx={{ mb: 2 }}>Scratch Card Editor</Typography>
      
      <Typography variant="subtitle1" gutterBottom>WhatsApp Configuration for Prize Claims</Typography>
      <Box sx={{ display: 'flex', gap: 2, alignItems: 'flex-start', mb: 2 }}>
        <CountryCodeSelector
          value={countryCode}
          onChange={setCountryCode}
          size="small"
        />
        <TextField
          label="WhatsApp Number"
          size="small"
          fullWidth
          variant="outlined"
          placeholder="1234567890 (digits only, no leading zero)"
          value={whatsappNumber}
          onChange={(e) => {
            const value = e.target.value.replace(/[^0-9]/g, '');
            setWhatsappNumber(value);
          }}
          InputProps={{
            startAdornment: (
              <WhatsAppIcon color="success" style={{ marginRight: 8 }} />
            ),
          }}
          helperText="Enter the phone number digits only (no leading zero) that customers should contact via WhatsApp to claim rewards"
        />
      </Box>
      
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="subtitle1" gutterBottom>General Settings</Typography>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <TextField
                label="Main Heading" size="small"
                fullWidth
                variant="outlined"
                value={headingText}
                onChange={(e) => setHeadingText(e.target.value)}
                sx={{ mb: 2 }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="Subtext / Instructions" size="small"
                fullWidth
                multiline
                rows={3}
                variant="outlined"
                value={subTextContent}
                onChange={(e) => setSubTextContent(e.target.value)}
                sx={{ mb: 2 }}
              />
            </Grid>
            
            
          </Grid>
        </CardContent>
      </Card>

      <Typography variant="subtitle1" gutterBottom sx={{ mb: 2 }}>Scratch Areas</Typography>
      <Grid container spacing={2}>
        {scratchAreas.map((area, index) => (
          <Grid item xs={12} key={area.id || index}>
            <Card sx={{ mb: 2, position: 'relative' }}>
              <IconButton 
                aria-label="delete scratch area"
                onClick={() => removeScratchArea(index)} 
                color="error"
                size="small"
                sx={{ position: 'absolute', top: 8, right: 8 }}
              >
                <DeleteIcon fontSize="small" />
              </IconButton>
              <CardContent sx={{ pt: 4 }}> 
                <Typography variant="subtitle1" gutterBottom>Scratch Area {index + 1}</Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="Scratch Text (e.g., SCRATCH HERE)"
                      fullWidth
                      size="small"
                      value={area.scratchText}
                      onChange={(e) => handleScratchAreaChange(index, 'scratchText', e.target.value)}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="Revealed Text (Line 1 - e.g., 10% OFF)"
                      fullWidth
                      size="small"
                      value={area.revealedTextLine1}
                      onChange={(e) => handleScratchAreaChange(index, 'revealedTextLine1', e.target.value)}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="Revealed Text (Line 2 - e.g., Congrats!)"
                      fullWidth
                      size="small"
                      value={area.revealedTextLine2}
                      onChange={(e) => handleScratchAreaChange(index, 'revealedTextLine2', e.target.value)}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth size="small">
                      <InputLabel>Reward Type</InputLabel>
                      <Select
                        value={area.rewardType}
                        label="Reward Type"
                        onChange={(e) => handleScratchAreaChange(index, 'rewardType', e.target.value as 'discount' | 'failure')}
                      >
                        <MenuItem value="discount">Discount</MenuItem>
                        <MenuItem value="failure">Failure (Try Again)</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="Reward Value (e.g., 10%, TRY AGAIN)"
                      fullWidth
                      size="small"
                      value={area.rewardValue}
                      onChange={(e) => handleScratchAreaChange(index, 'rewardValue', e.target.value)}
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        ))}
        <Grid item xs={12}>
          <Button onClick={addScratchArea} variant="outlined" fullWidth startIcon={<AddIcon />}>
            Add Scratch Area
          </Button>
        </Grid>
      </Grid>
    </Box>
  );
};

export default ScratchCardEditor;
