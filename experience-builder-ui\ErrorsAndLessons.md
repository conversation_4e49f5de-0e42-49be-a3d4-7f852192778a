# Changelog - Key Bug Fixes & Lessons Learned

This document tracks significant bug fixes and important lessons learned during the development of the Experience Builder application.

## [YYYY-MM-DD] - Mobile Experience & Data Handling Improvements

### 1. QR Code URL Inconsistency
*   **Issue:** QR codes generated unreliable URLs for local development (e.g., using local network IPs).
*   **Fix:** Modified `generateQrCodeUrl` in `src/utils/supabaseClient.ts` to prioritize the `VITE_PUBLIC_URL` environment variable. If not set, it defaults to `http://localhost:5173` in development.
*   **Lesson:** Ensure consistent base URL generation for shareable links, especially across development and production. Use environment variables for flexibility and provide sensible defaults for local development.

### 2. React Hook Rendering Errors
*   **Issue:** "Rendered more hooks than during the previous render" error in `MobileExperiencePage.tsx` due to conditional hook calls.
*   **Fix:** Refactored `MobileExperiencePage.tsx` to move all React hook calls (`useState`, `useCallback`, etc.) to the top level of the component, before any conditional rendering logic.
*   **Lesson:** Always call React hooks at the top level of functional components and in the same order on every render. Avoid calling hooks inside loops, conditions, or nested functions.

### 3. Background Media Not Displaying (400 Bad Request)
*   **Issue:** Mobile preview showed a `400 Bad Request` for background images.
*   **Root Cause:** A mismatch in Supabase bucket names. The `resolveMediaUrl` function in `src/utils/media.ts` was hardcoded to use `media_library_dev`, while actual uploads and other utilities used `experience-media` (defined by the `BUCKET_NAME` constant).
*   **Fix:** Modified `resolveMediaUrl` to use the shared `BUCKET_NAME` constant (`'experience-media'`).
*   **Lessons:**
    *   **Consistency is Key:** Use shared constants or configuration values for critical identifiers (like bucket names, API endpoints). Avoid hardcoding.
    *   **Verify Data Paths:** When debugging asset loading errors, meticulously check the entire URL/path construction logic.
    *   **Trust Network Tab:** The browser's Network tab provides the definitive URL requested and the server's response.

### 4. Game Modules Not Rendering (Undefined Type)
*   **Issue:** Game modules (ScratchCard, SpinWheel) were not appearing in the mobile preview because their `type` property was `undefined` after data transformation.
*   **Root Cause:** The module transformation logic in `MobileExperiencePage.tsx` was attempting to read `apiModule.module_type` from the raw module data fetched from Supabase. However, the actual property name in the raw data was `apiModule.type`.
*   **Fix:** Corrected the mapping in `MobileExperiencePage.tsx` (within the `.map((apiModule) => { ... })` block) to use `apiModule.type` when setting the `type` for transformed modules.
*   **Lesson:** When transforming data, especially from an API or database, always verify the exact property names in the source data. Console logging the raw data *before* transformation is a crucial debugging step.

### 5. TypeScript Errors with Supabase Client (Ongoing Concern)
*   **Issue:** Type errors encountered in `src/utils/media.ts` when interacting with Supabase tables, suggesting outdated or incomplete auto-generated types.
*   **Fix (Temporary):** Applied `as any` type assertions to bypass TypeScript errors and allow compilation.
*   **Lesson/Recommendation:** Regularly regenerate Supabase TypeScript types (e.g., `npx supabase gen types typescript ...`) after database schema changes or when encountering such errors to maintain type safety.
