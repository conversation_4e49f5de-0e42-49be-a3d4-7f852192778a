import { ThemeProvider, createTheme, CssBaseline } from '@mui/material';
import { IPhone } from '@components/phone/IPhone';

const theme = createTheme({
  palette: { mode: 'light' },
});

export default function IPhoneV3Page() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <div className="flex items-center justify-center min-h-screen bg-gray-900">
        <IPhone 
          modules={[]} 
          backgroundMedia={null}
          currentScreen={0}
          totalScreens={1}
          onScreenChange={() => {}}
        />
      </div>
    </ThemeProvider>
  );
}
