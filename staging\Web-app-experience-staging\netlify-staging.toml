# Netlify configuration for STAGING environment
# This file is used for staging deployments only

[build]
  # Build command for staging
  command = "cd experience-builder-ui && rm -rf node_modules package-lock.json && npm install --force && npm run build:staging"
  
  # Publish directory
  publish = "experience-builder-ui/dist"
  
  # Base directory
  base = "."

[build.environment]
  # Node.js version
  NODE_VERSION = "18"
  
  # Staging environment flag
  ENVIRONMENT = "staging"
  
  # Enable staging-specific build optimizations
  VITE_ENVIRONMENT = "staging"
  VITE_DEBUG_MODE = "true"

# Staging-specific redirects and headers
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    # Staging-specific header
    X-Environment = "staging"

# Staging branch deployment settings
[context.staging]
  command = "cd experience-builder-ui && rm -rf node_modules package-lock.json && npm install --force && npm run build:staging"
  
[context.staging.environment]
  VITE_ENVIRONMENT = "staging"
  VITE_DEBUG_MODE = "true"

# Branch-specific settings for staging branch
[context.branch-deploy]
  command = "cd experience-builder-ui && rm -rf node_modules package-lock.json && npm install --force && npm run build:staging"

# Staging-specific functions (if any)
# [functions]
#   directory = "netlify/functions"
