# Web App Experience Builder

A powerful drag-and-drop platform for creating interactive mobile-first web experiences. Combine building blocks into modules to create engaging campaigns, registrations, surveys, and gamified experiences without coding.

## 🚀 Features

### Visual Experience Builder
- **Drag-and-drop interface** for combining blocks into modules
- **Real-time preview** with iPhone-style mobile preview
- **Multi-screen support** for complex user journeys
- **Custom background** images with cropping tools

### Building Blocks
Individual blocks combine to form modules, which then combine to create complete experiences.

**Gamified Blocks:**
- **Scratch Cards** - Canvas-based scratch-to-win games with progress tracking and WhatsApp prize claiming
- **Spin Wheels** - Customizable prize wheels with animations and reward systems
- **Modern Variants** - Enhanced versions with advanced UI and touch interactions

**Standard Blocks:**
- **User Registration** - Forms with validation
- **Surveys & Forms** - Dynamic question sets
- **Discount Codes** - Coupon distribution
- **Video & Media** - Rich content display
- **Shopping Links** - Product integrations
- **Custom Buttons** - Call-to-action elements

**Architecture**: Blocks → Modules → Experiences

### Mobile-First Design
- **QR Code Generation** - Instant mobile access with download functionality
- **iPhone Simulation** - Dynamic Island, status bar, and realistic phone frame
- **Touch Interactions** - Canvas-based scratching, spin wheel mechanics
- **WhatsApp Integration** - Direct prize claiming via WhatsApp messages
- **Media Management** - Advanced cropping, background selection, and optimization

### Analytics & Management
- **Usage Tracking** - QR code scans and interactions
- **Experience Management** - Save, edit, and organize projects
- **Database Integration** - Supabase backend storage

## 🛠️ Technology Stack

- **Frontend**: React 19, TypeScript, Vite
- **UI Framework**: Material-UI (MUI) + Tailwind CSS
- **Interactions**: React DND, Framer Motion, Canvas API
- **Backend**: Supabase (PostgreSQL)
- **Mobile Runtime**: React SPA with mobile-optimized routing
- **QR Codes**: qrcode library for generation and download
- **Graphics**: HTML5 Canvas for scratch cards, Phosphor React icons
- **Integration**: WhatsApp Business API for prize claiming

## 🏗️ Project Structure

```
├── experience-builder-ui/     # Main React application
│   ├── src/
│   │   ├── components/        # UI components & building blocks
│   │   ├── pages/            # Application pages
│   │   ├── utils/            # Utilities and API clients
│   │   ├── hooks/            # Custom React hooks
│   │   ├── styles/           # Global styles
│   │   ├── theme/            # MUI theme configuration
│   │   └── types/            # TypeScript definitions
│   ├── db/                   # Database schemas and migrations
│   └── public/               # Static assets
├── gamified-modules/         # Reusable gamified block components
│   └── src/SpinWheel/        # Spin wheel implementation
├── shared/                   # Common types and utilities
│   └── types/                # Shared TypeScript definitions
├── mobile-runtime/           # Mobile-specific code (minimal)
└── test-server.js           # Development server
```

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ and npm/yarn
- Supabase account and project

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/D4RK-777/Web-app-experience.git
   cd Web-app-experience
   ```

2. **Install dependencies**
   ```bash
   npm install
   cd experience-builder-ui && npm install
   cd ../gamified-modules && npm install
   ```

3. **Set up environment variables**
   Create `.env` in `experience-builder-ui/`:
   ```env
   VITE_SUPABASE_URL=your-supabase-url
   VITE_SUPABASE_ANON_KEY=your-supabase-anon-key
   ```

4. **Set up database**
   ```bash
   # Run the SQL schema in your Supabase project
   psql -f experience-builder-ui/db/create_tables.sql
   ```

### Development

```bash
# Start main UI development server
cd experience-builder-ui && npm run dev

# Build gamified modules
cd gamified-modules && npm run build

# Start test server
node test-server.js
```

### Building for Production

```bash
# Build UI
cd experience-builder-ui && npm run build

# Build modules
cd gamified-modules && npm run build

# Lint code
cd experience-builder-ui && npm run lint
```

## 📱 Usage

### Creating an Experience

1. **Add Details**: Set experience name and background
2. **Build Experience**: Drag blocks from the sidebar to create modules
3. **Customize**: Configure each block's settings within modules
4. **Preview**: Test in mobile preview pane
5. **Save**: Generate QR code for sharing

### Block Configuration

Each block type has specific configuration options:

- **Scratch Cards**: Canvas dimensions, reward text, progress thresholds, WhatsApp integration
- **Spin Wheels**: Segment colors, prize configuration, animation settings, claiming mechanics
- **User Forms**: Field visibility, validation, labels, country code selection
- **Surveys**: Question types, required fields, logic flows
- **Media Blocks**: Cropping tools, aspect ratios, background selections

### Mobile Access

Experiences are accessible via:
- QR codes (automatically generated)
- Direct URLs: `https://web-app-experience.netlify.app/experience/{id}`
- Mobile-optimized interface with touch interactions

## 🗄️ Database Schema

### Core Tables
- **experiences**: Main experience records
- **screens**: Multi-screen support with backgrounds
- **modules**: Individual blocks grouped per experience
- **qr_codes**: QR code tracking and analytics
- **analytics**: Usage events and metrics

### Key Relationships
- Experiences → Screens (1:many)
- Screens → Modules (1:many) - modules contain blocks
- Experiences → QR Codes (1:1)

## 🎯 API Reference

### Experience Management
```typescript
// Save experience with modules containing blocks
await saveExperience(name, backgroundMediaId, modules)

// Get experience by ID
await getExperience(experienceId)

// Generate QR code URL
generateQrCodeUrl(experienceId)
```

### Block Types
```typescript
interface Block {
  id: string;
  type: 'ScratchCard' | 'SpinWheel' | 'UserDetails' | ...;
  title: string;
  content: BlockContent;
}

interface Module {
  id: string;
  blocks: Block[];
  screenId: string;
}
```

## 🚀 Deployment

### Netlify (Current)
- Automatic deployments from main branch
- Environment variables configured in Netlify dashboard
- Custom domain: `web-app-experience.netlify.app`

### Manual Deployment
```bash
cd experience-builder-ui
npm run build
# Deploy dist/ folder to your hosting provider
```

## 🧪 Testing

Currently no test suite configured. To add testing:
```bash
cd experience-builder-ui
npm install --save-dev vitest @testing-library/react @testing-library/jest-dom
```

## 📊 Analytics

Track experience performance:
- QR code scan counts
- User interaction events
- Block completion rates
- Experience access patterns

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue in the GitHub repository
- Check the documentation in `/docs`
- Review the database schema in `/experience-builder-ui/db/`

## 🏆 Acknowledgments

- Built with React, TypeScript, and Vite
- UI components from Material-UI and Tailwind CSS
- Backend powered by Supabase
- QR code generation via qrcode library
