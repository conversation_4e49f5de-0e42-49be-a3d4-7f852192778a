import React, { useRef } from "react";
import { Typo<PERSON>, But<PERSON>, <PERSON>ack, Paper } from "@mui/material";
import {
  Article,
  ShoppingCart,
  ChatCircle,
  Shield,
  Cursor,
  UserPlus,
  Star,
  User,
  Sparkle,
  CircleNotch,
  Receipt,
  Calendar,
  ChatText,
  CheckCircle,
} from "phosphor-react";
import { styled } from "@mui/material/styles";
import { useDrag } from "react-dnd";

type ExperienceType = "Registration" | "Rebates" | "Sweepstakes";

interface BuildExperienceProps {
  addModule: (moduleType: string, title: string) => void;
  experienceType: ExperienceType;
}

const ModuleButton = styled(Button)(({ theme }) => ({
  width: "100%",
  justifyContent: "flex-start",
  padding: "14px 18px",
  borderRadius: "8px",
  textTransform: "none",
  overflow: "hidden",
  color: theme.palette.text.primary,
  backgroundColor: "white",
  border: `2px solid ${theme.palette.grey[200]}`,
  boxShadow: "0 1px 3px rgba(0, 0, 0, 0.1)",
  fontSize: "0.875rem",
  fontWeight: 500,
  transition: "all 0.2s ease",
  "&:hover": {
    backgroundColor: theme.palette.grey[50],
    borderColor: theme.palette.primary.main,
    transform: "translateY(-1px)",
    boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)",
  },
  "&:active": {
    transform: "translateY(0)",
  },
  whiteSpace: "nowrap",
  gap: "12px",
}));

interface DraggableButtonProps {
  module: { type: string; title: string };
  addModule: (type: string, title: string) => void;
}

const DraggableModuleButton: React.FC<DraggableButtonProps> = ({
  module,
  addModule,
}) => {
  const ref = useRef<HTMLDivElement>(null);
  const [{ isDragging }, drag] = useDrag(
    () => ({
      type: "AVAILABLE_MODULE",
      item: { moduleType: module.type, title: module.title },
      collect: (monitor) => ({ isDragging: monitor.isDragging() }),
    }),
    [module],
  );
  drag(ref);

  const getIcon = () => {
    const iconProps = { size: 20, weight: "duotone" as const };
    switch (module.type) {
      case "Warranty":
        return <Shield {...iconProps} color="#6366F1" />;
      case "SetupGuide":
        return <Article {...iconProps} color="#10B981" />;
      case "ShoppingLinks":
        return <ShoppingCart {...iconProps} color="#F59E0B" />;
      case "FeedbackSurvey":
        return <ChatCircle {...iconProps} color="#EC4899" />;
      case "CustomButton":
        return <Cursor {...iconProps} color="#8B5CF6" />;
      case "Registration":
        return <UserPlus {...iconProps} color="#3B82F6" />;
      case "ScratchCard":
        return <Sparkle {...iconProps} color="#EF4444" />;
      case "SpinWheel":
        return <CircleNotch {...iconProps} color="#F97316" />;
      case "UserDetails":
        return <User {...iconProps} color="#06B6D4" />;
      case "CampaignDate":
        return <Calendar {...iconProps} color="#84CC16" />;
      case "Campaign":
        return <Star {...iconProps} color="#F59E0B" />;
      case "IntroMessage":
        return <ChatText {...iconProps} color="#8B5CF6" />;
      case "ConfirmationMessage":
        return <CheckCircle {...iconProps} color="#10B981" />;
      case "Receipts":
        return <Receipt {...iconProps} color="#6366F1" />;
      default:
        return null;
    }
  };

  return (
    <div ref={ref} style={{ opacity: isDragging ? 0.4 : 1 }}>
      <ModuleButton
        variant="text"
        onClick={() => addModule(module.type, module.title)}
        startIcon={getIcon()}
      >
        {module.title}
      </ModuleButton>
    </div>
  );
};

const BuildExperience: React.FC<BuildExperienceProps> = ({
  addModule,
  experienceType,
}) => {
  let availableModules: { type: string; title: string }[] = [];

  switch (experienceType) {
    case "Registration":
      availableModules = [
        { type: "SetupGuide", title: "Setup Guide" },
        { type: "FeedbackSurvey", title: "Feedback Survey" },
        { type: "Registration", title: "Registration Form" },
        { type: "Warranty", title: "Warranty" },
        { type: "ShoppingLinks", title: "Shopping" },
        { type: "CustomButton", title: "Custom Button" },
        { type: "ScratchCard", title: "Scratch Card Game" },
        { type: "SpinWheel", title: "Spin Wheel Game" },
        { type: "UserDetails", title: "User Details Form" },
      ];
      break;
    case "Rebates":
      availableModules = [
        { type: "CampaignDate", title: "Campaign Date" },
        { type: "Campaign", title: "Campaign Details" },
        { type: "IntroMessage", title: "Intro Message" },
        { type: "ConfirmationMessage", title: "Confirmation Message" },
        { type: "UserDetails", title: "User Details Form" },
      ];
      break;
    case "Sweepstakes":
      availableModules = [
        { type: "Receipts", title: "Receipts" },
        { type: "ShoppingLinks", title: "Shopping" },
        { type: "FeedbackSurvey", title: "Feedback Survey" },
        { type: "UserDetails", title: "User Details Form" },
      ];
      break;
    default:
      availableModules = [];
  }

  return (
    <Paper
      elevation={1}
      sx={{ p: 1, borderRadius: "8px", border: "1px solid #f1f5f9" }}
    >
      <Typography
        variant="h6"
        sx={{ fontWeight: 600, mb: 1, fontSize: "16px" }}
      >
        Add Modules
      </Typography>
      <Stack spacing={2}>
        {availableModules.map((module) => (
          <DraggableModuleButton
            key={module.type}
            module={module}
            addModule={addModule}
          />
        ))}
      </Stack>
    </Paper>
  );
};

export default BuildExperience;
