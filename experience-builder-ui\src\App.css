/* Reset basic styles */
body, html, #root {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  background-color: #ffffff; /* White background for the whole page */
  color: #333;
}

.app-container {
  display: flex;
  height: 100vh; /* Full viewport height */
  overflow: hidden; /* Prevent scrollbars on the main container */
}

.sidebar {
  width: 250px;
  background-color: #ffffff; /* White background for sidebar */
  padding: 20px;
  border-right: 1px solid #e0e0e0; /* Light border */
  box-shadow: 2px 0 5px rgba(0,0,0,0.05);
  overflow-y: auto; /* Allow scrolling if content exceeds height */
}

.sidebar h2 {
  font-size: 1.2em;
  color: #1a202c;
  margin-top: 0;
  margin-bottom: 20px;
}

.add-details-section,
.build-experience-section,
.qr-code-section {
  margin-bottom: 20px;
  padding: 10px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  background-color: #ffffff;
}

.add-details-section h4,
.build-experience-section h4,
.qr-code-section h4 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 1em;
  color: #333;
}

.add-details-section label {
  display: block;
  margin-bottom: 5px;
  font-size: 0.9em;
}

.add-details-section input[type="text"] {
  width: calc(100% - 16px); /* Account for padding */
  padding: 8px;
  margin-bottom: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-sizing: border-box;
}

.build-experience-section ul {
  list-style-type: none;
  padding-left: 0;
  margin: 0;
}

.build-experience-section li {
  padding: 5px 0;
  font-size: 0.9em;
  margin-bottom: 5px; /* Add some space between buttons */
}

.build-experience-section button {
  width: 100%;
  padding: 8px;
  background-color: #ffffff;
  border: 1px solid #cdd2d8;
  border-radius: 4px;
  text-align: left;
  cursor: pointer;
  font-size: 0.9em;
}

.build-experience-section button:hover {
  background-color: #ffffff;
}

/* Canvas Specific Styles */
.canvas-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 15px;
  margin-bottom: 15px;
  border-bottom: 1px solid #e0e0e0;
}

.canvas-header h3 {
  margin: 0;
  font-size: 1.3em;
}

.launch-button {
  padding: 8px 15px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9em;
}
.launch-button:hover {
  background-color: #0056b3;
}

.background-settings,
.module-toolbar,
.modules-list-canvas {
  background-color: #ffffff;
  padding: 15px;
  margin-bottom: 15px;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
}

.background-settings h4,
.module-toolbar h4,
.modules-list-canvas h4 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 1.1em;
}

.uploader-placeholder {
  border: 2px dashed #ccc;
  padding: 20px;
  text-align: center;
  color: #777;
  border-radius: 4px;
  cursor: pointer;
}

.toolbar-icons span {
  margin-right: 10px;
  cursor: pointer;
  /* Replace with actual icons later */
  padding: 5px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

.canvas-module-item {
  background-color: #ffffff;
  border: 1px solid #e0e0e0;
  padding: 10px;
  margin-bottom: 10px;
  border-radius: 4px;
}

.module-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding-bottom: 5px;
  border-bottom: 1px solid #ddd; /* Light separator for header */
}

.module-header h4 {
  margin: 0;
  font-size: 1.05em; /* Slightly adjust title size */
}

.module-item-controls span {
  margin-left: 8px; /* Space out control icons */
  cursor: pointer;
  font-size: 1.1em; /* Make icons a bit larger */
  color: #555;
}

.module-item-controls span:hover {
  color: #000;
}

.module-content {
  font-size: 0.9em;
  color: #333;
}

/* Remove or adjust old .module-controls if it conflicts or is redundant */
.module-controls span {
  margin-right: 8px;
  color: #007bff;
  cursor: pointer;
  font-size: 0.9em;
}

.preview-module-item {
  padding: 5px;
  margin-bottom: 5px;
  background-color: #f0f0f0;
  border-radius: 3px;
  text-align: center;
}

.preview-module-item h5 {
  margin: 0;
  font-size: 0.8em;
}

.main-content {
  flex-grow: 1; /* Takes up remaining space */
  padding: 20px;
  background-color: #ffffff; /* White background for canvas */
  overflow-y: auto; /* Allow scrolling if content exceeds height */
}

.main-content h2 {
  font-size: 1.4em;
  color: #1a202c;
  margin-top: 0;
  margin-bottom: 20px;
}

.mobile-preview {
  width: 375px; /* Typical mobile width */
  min-width: 320px;
  background-color: #ffffff; /* White background for the preview area */
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-left: 1px solid #e0e0e0;
  box-shadow: -2px 0 5px rgba(0,0,0,0.05);
}

.mobile-preview h2 {
  font-size: 1.2em;
  color: #1a202c;
  margin-top: 0;
  margin-bottom: 20px;
}

.phone-bezel {
  width: 320px; /* Width of the phone screen area */
  height: 640px; /* Height of the phone screen area */
  background-color: #1a202c; /* Dark bezel color */
  border-radius: 30px; /* Rounded corners for the phone */
  padding: 15px;
  box-shadow: 0 0 20px rgba(0,0,0,0.2);
  display: flex;
  justify-content: center;
  align-items: center;
}

.phone-screen {
  width: 100%;
  height: 100%;
  background-color: #ffffff; /* White screen */
  border-radius: 15px; /* Slightly rounded inner screen corners */
  overflow-y: auto; /* Allow scrolling within the phone screen */
  padding: 10px;
  color: #333;
}
