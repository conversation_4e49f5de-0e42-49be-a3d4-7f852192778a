import React from 'react';

export const DynamicIsland: React.FC = () => {
  return (
    <div className="w-[111px] h-[31px] absolute left-[149px] top-[24px]" style={{ zIndex: 10 }}>
      <svg
        width="111"
        height="31"
        viewBox="0 0 111 31"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className="w-full h-full"
      >
        <rect width="110" height="31" rx="15.5" fill="#030303" />
        <circle cx="93.6202" cy="15.6202" r="9.62021" fill="#0E0B0F" />
        <circle cx="93.62" cy="15.6202" r="5.34456" fill="#161424" />
        <circle cx="93.6203" cy="15.6202" r="3.20674" fill="#0F0F2A" />
        <circle cx="93.6202" cy="14.5512" r="1.06891" fill="#393752" />
      </svg>
    </div>
  );
};
