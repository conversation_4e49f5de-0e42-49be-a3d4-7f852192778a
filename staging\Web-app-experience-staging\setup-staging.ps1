# Staging Environment Setup Script
# This script automates the setup of the staging environment

Write-Host "🚀 Setting up Experience Builder Staging Environment..." -ForegroundColor Green

# Check if Node.js is installed
Write-Host "📋 Checking prerequisites..." -ForegroundColor Yellow
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js version: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js is not installed. Please install Node.js v18+ first." -ForegroundColor Red
    exit 1
}

# Check if npm is installed
try {
    $npmVersion = npm --version
    Write-Host "✅ npm version: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ npm is not installed. Please install npm first." -ForegroundColor Red
    exit 1
}

# Navigate to experience-builder-ui directory
Write-Host "📁 Navigating to experience-builder-ui directory..." -ForegroundColor Yellow
Set-Location "experience-builder-ui"

# Install dependencies
Write-Host "📦 Installing dependencies..." -ForegroundColor Yellow
npm install

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Failed to install dependencies." -ForegroundColor Red
    exit 1
}

Write-Host "✅ Dependencies installed successfully!" -ForegroundColor Green

# Check if .env.staging exists
if (Test-Path ".env.staging") {
    Write-Host "✅ .env.staging file found" -ForegroundColor Green
} else {
    Write-Host "⚠️  .env.staging file not found. Creating default..." -ForegroundColor Yellow
    
    $envContent = @"
# Staging Environment Configuration
VITE_SUPABASE_URL=https://jqaqkymjacdnllytexou.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpxYXFreW1qYWNkbmxseXRleG91Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcwNDM5MDYsImV4cCI6MjA2MjYxOTkwNn0.LoJMnX2qO945At_Gebd7khYGsttudBJfiiC-XzM3-8I
VITE_PUBLIC_URL=http://localhost:4000
VITE_ENVIRONMENT=staging
VITE_DEBUG_MODE=true
VITE_ENABLE_DEBUG_TOOLS=true
VITE_ENABLE_STAGING_FEATURES=true
"@
    
    $envContent | Out-File -FilePath ".env.staging" -Encoding UTF8
    Write-Host "✅ Created .env.staging file" -ForegroundColor Green
}

# Test staging scripts
Write-Host "🧪 Testing staging configuration..." -ForegroundColor Yellow

# Check if staging scripts exist in package.json
$packageJson = Get-Content "package.json" | ConvertFrom-Json
if ($packageJson.scripts."start:staging") {
    Write-Host "✅ Staging scripts found in package.json" -ForegroundColor Green
} else {
    Write-Host "⚠️  Staging scripts not found. Please check package.json configuration." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🎉 Staging environment setup complete!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Next steps:" -ForegroundColor Cyan
Write-Host "1. Start staging server: npm run start:staging" -ForegroundColor White
Write-Host "2. Open browser: http://localhost:4000" -ForegroundColor White
Write-Host "3. Review README-STAGING.md for detailed instructions" -ForegroundColor White
Write-Host ""
Write-Host "🔧 Available commands:" -ForegroundColor Cyan
Write-Host "  npm run start:staging    - Start staging development server" -ForegroundColor White
Write-Host "  npm run build:staging    - Build for staging deployment" -ForegroundColor White
Write-Host "  npm run test:staging     - Run staging tests" -ForegroundColor White
Write-Host "  npm run preview:staging  - Preview staging build" -ForegroundColor White
Write-Host ""

# Ask if user wants to start the staging server
$startServer = Read-Host "Would you like to start the staging server now? (y/N)"
if ($startServer -eq "y" -or $startServer -eq "Y") {
    Write-Host "🚀 Starting staging server..." -ForegroundColor Green
    npm run start:staging
} else {
    Write-Host "✅ Setup complete. Run 'npm run start:staging' when ready." -ForegroundColor Green
}
