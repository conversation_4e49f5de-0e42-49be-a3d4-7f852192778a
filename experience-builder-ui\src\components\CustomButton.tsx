import React from 'react';
import { Box, Button } from '@mui/material';

export interface CustomButtonProps {
  text: string;
  url: string;
  textColor: string;
  buttonColor: string;
  borderColor: string;
}

const CustomButton: React.FC<CustomButtonProps> = ({
  text,
  url,
  textColor,
  buttonColor,
  borderColor,
}) => {
  return (
    <Box sx={{ display: 'inline-block', m: 1 }}>
      <Button
        variant="contained"
        href={url}
        target="_blank"
        rel="noopener noreferrer"
        sx={{
          color: textColor,
          backgroundColor: buttonColor,
          borderColor: borderColor,
          borderStyle: 'solid',
          borderWidth: '2px',
          '&:hover': {
            backgroundColor: buttonColor,
            opacity: 0.9,
          },
        }}
      >
        {text}
      </Button>
    </Box>
  );
};

export default CustomButton;
