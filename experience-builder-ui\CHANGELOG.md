# Experience Builder UI – Change Log

This document is maintained automatically by Cascade. Every code addition, removal, or modification on this branch is captured here with both a **technical** and **user-friendly** explanation.

---

## 2025-06-12

### Added – Database schema & tables (SQL)
* **Technical**  Created `experience_builder` schema plus tables `experiences`, `modules`, `qr_codes`, and `analytics`, indexes, trigger `trg_experience_updated`, helper function `increment_qr_counter`.
* **User-friendly**  The app now has its own isolated area in the database where all experiences, modules, QR codes, and analytics live. This prevents naming clashes and keeps data for future projects separate.

### Added – Type definitions
* **Technical**  Regenerated `src/utils/types/database.types.ts` to include full typings for the new schema and tables.
* **User-friendly**  Type-safe queries mean fewer runtime errors and better autocompletion while coding.

### Added – Utility & component scaffolding
* **Technical**  `supabaseClient.ts` now contains `saveExperience`, `updateQrCodeImage`, and `getExperience` helpers. `QrCodeGenerator.tsx` generates QR codes, saves the PNG, and displays a download button.
* **User-friendly**  Users can submit an experience and immediately receive a QR code they can print or share.

### Added – Environment configuration
* **Technical**  `.env` now stores `VITE_SUPABASE_URL` and `VITE_SUPABASE_ANON_KEY`; app reads these at build time.
* **User-friendly**  Credentials are kept out of the codebase, making deployment safer and more flexible.

### Added – Setup script
* **Technical**  `db/setupDatabase.js` was authored (later superseded by direct SQL) to automate schema creation via the Supabase API.
* **User-friendly**  Provides a repeatable way to initialise the database for new environments.

## [0.3.0] - 2025-06-12
### Added
- End-to-end saving of experiences to Supabase `public` schema.
- Automatic QR-code generation and storage in `qr_codes` table.
- Browser preview workflow for local development.

### Fixed
- Supabase REST errors (`PGRST106`) by moving tables/queries to `public` schema.
- TypeScript database types updated to match new schema location.
- Internal preview proxy stability.

### Upcoming
- QR-code redirect endpoint to mobile viewer.
- Offline mobile experience renderer.
- Gamified games component for web browser.

---

> **Next entries** will be appended automatically as new tasks are completed (page route, drag-and-drop persistence, auth/RLS, etc.).
