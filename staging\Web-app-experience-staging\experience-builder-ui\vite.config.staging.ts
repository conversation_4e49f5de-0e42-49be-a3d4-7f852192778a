import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { fileURLToPath, URL } from 'node:url';

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  const isStaging = mode === 'staging';
  
  return {
    // Use staging environment file
    envDir: '.',
    envPrefix: 'VITE_',
    
    server: {
      host: true,
      port: isStaging ? 4000 : 5173,
      strictPort: true,
    },
    
    preview: {
      port: isStaging ? 4001 : 4173,
      strictPort: true,
    },
    
    plugins: [react()],
    
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
        '@components': fileURLToPath(new URL('./src/components', import.meta.url)),
      },
    },
    
    build: {
      // Staging-specific build options
      sourcemap: isStaging ? true : false,
      minify: isStaging ? false : 'esbuild',
      rollupOptions: {
        output: {
          // Add staging identifier to build files
          entryFileNames: isStaging ? 'staging-[name]-[hash].js' : '[name]-[hash].js',
          chunkFileNames: isStaging ? 'staging-[name]-[hash].js' : '[name]-[hash].js',
          assetFileNames: isStaging ? 'staging-[name]-[hash].[ext]' : '[name]-[hash].[ext]',
        },
      },
    },
    
    define: {
      // Staging-specific global constants
      __STAGING__: isStaging,
      __ENVIRONMENT__: JSON.stringify(isStaging ? 'staging' : 'production'),
    },
  };
});
