import React, { useState, useRef, useCallback, useEffect } from "react";
import { Box, Typography, Button } from "@mui/material";
import { styled } from "@mui/material/styles";
// The Sparkle import is used in the FloatingSparkle styled component
import { Sparkle } from "phosphor-react";
import { keyframes } from "@emotion/react";

export type RewardType = 'prize' | 'discount' | 'none' | 'failure';  // Added 'failure' to fix type mismatch

export interface ScratchCardReward {
  id: string;
  text: string;
}

interface ModernScratchCardProps {
  rewards: ScratchCardReward[];
  onWin?: (reward: ScratchCardReward) => void;
  whatsappNumber?: string;
  countryCode?: string; // Add this prop
}

// Animations
const revealAnimation = keyframes`
  0% { opacity: 0; transform: scale(0.8); }
  100% { opacity: 1; transform: scale(1); }
`;

const sparkleFloat = keyframes`
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-10px) rotate(180deg); }
`;

// Styled Components
const FloatingSparkle = styled(Sparkle)<{ delay: number }>`
  position: absolute;
  color: rgba(255, 255, 255, 0.6);
  animation: ${sparkleFloat} 3s ease-in-out infinite;
  animation-delay: ${props => props.delay}s;
  z-index: 1000;
`;



const ClaimButton = styled(Button)`
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  font-weight: bold;
  padding: 12px 24px;
  border-radius: 25px;
  font-size: 1rem;
  text-transform: none;
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
  transition: all 0.3s ease;
  
  &:hover {
    background: linear-gradient(135deg, #45a049, #4CAF50);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(76, 175, 80, 0.4);
  }
  
  &:disabled {
    background: linear-gradient(135deg, #9e9e9e, #757575);
    color: rgba(255, 255, 255, 0.6);
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 2px 6px rgba(158, 158, 158, 0.2);
  }
`;

// Configuration
const SCRATCH_CONFIG = {
  brushSize: 12, // Made thinner for better scratch experience
  revealThreshold: 70, // Percentage of area that needs to be scratched
};

const ModernScratchCard: React.FC<ModernScratchCardProps> = ({
  rewards,
  onWin = () => {},
  whatsappNumber,
  countryCode,
}) => {
  const [gameOver, setGameOver] = useState(false);
  const [winningReward, setWinningReward] = useState<ScratchCardReward | null>(null);
  const scratchCardRef = useRef<HTMLCanvasElement>(null);
  const [scratchProgress, setScratchProgress] = useState(0);
  const isScratching = useRef(false);

  // Randomly select a reward when the component mounts
  useEffect(() => {
    if (rewards && rewards.length > 0) {
      const randomIndex = Math.floor(Math.random() * rewards.length);
      setWinningReward(rewards[randomIndex]);
      console.log('Winning Reward Set:', rewards[randomIndex]);
    }
  }, [rewards]);

  // Draw initial scratch layer
  useEffect(() => {
    const canvas = scratchCardRef.current;
    if (!canvas) return;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Draw white rectangle instead of silver
    ctx.fillStyle = '#FFFFFF';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    console.log('Initial white layer drawn');
  }, []);

  const getScratchedPercentage = useCallback(() => {
    const canvas = scratchCardRef.current;
    if (!canvas) return 0;
    const ctx = canvas.getContext('2d');
    if (!ctx) return 0;

    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const pixels = imageData.data;
    let transparentPixels = 0;

    for (let i = 0; i < pixels.length; i += 4) {
      // Check if pixel is mostly transparent (alpha < 128)
      if (pixels[i + 3] < 128) {
        transparentPixels++;
      }
    }
    return (transparentPixels / (pixels.length / 4)) * 100;
  }, []);

  const handleScratch = useCallback((x: number, y: number) => {
    const canvas = scratchCardRef.current;
    if (!canvas) return;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    ctx.globalCompositeOperation = 'destination-out';
    ctx.beginPath();
    ctx.arc(x, y, SCRATCH_CONFIG.brushSize, 0, Math.PI * 2);
    ctx.fill();

    const currentProgress = getScratchedPercentage();
    setScratchProgress(currentProgress);
    console.log('Scratch Progress:', currentProgress.toFixed(2), '%, Game Over:', gameOver);

    if (currentProgress >= SCRATCH_CONFIG.revealThreshold && !gameOver) {
      console.log('Game Over set to true. Reveal Threshold Reached!');
      setGameOver(true);
      console.log('Game Over:', gameOver);
      if (winningReward && onWin) {
        console.log('Winning Reward:', winningReward);
        onWin(winningReward);
      }
      // Clear the remaining gold layer to fully reveal the prize
      ctx.clearRect(0, 0, canvas.width, canvas.height);
    }
  }, [getScratchedPercentage, gameOver, winningReward, onWin]);

  const onMouseDown = useCallback((e: React.MouseEvent<HTMLCanvasElement>) => {
    isScratching.current = true;
    const canvas = e.target as HTMLCanvasElement;
    const rect = canvas.getBoundingClientRect();
    // Scale coordinates to match canvas internal dimensions
    const scaleX = canvas.width / rect.width;
    const scaleY = canvas.height / rect.height;
    const x = (e.clientX - rect.left) * scaleX;
    const y = (e.clientY - rect.top) * scaleY;
    handleScratch(x, y);
  }, [handleScratch]);

  const onMouseMove = useCallback((e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isScratching.current) return;
    const canvas = e.target as HTMLCanvasElement;
    const rect = canvas.getBoundingClientRect();
    // Scale coordinates to match canvas internal dimensions
    const scaleX = canvas.width / rect.width;
    const scaleY = canvas.height / rect.height;
    const x = (e.clientX - rect.left) * scaleX;
    const y = (e.clientY - rect.top) * scaleY;
    handleScratch(x, y);
  }, [handleScratch]);

  const onMouseUp = useCallback(() => {
    isScratching.current = false;
  }, []);

  const onMouseLeave = useCallback(() => {
    isScratching.current = false;
  }, []);

  const onTouchStart = useCallback((e: React.TouchEvent<HTMLCanvasElement>) => {
    isScratching.current = true;
    e.preventDefault();
    const touch = e.touches[0];
    const canvas = e.target as HTMLCanvasElement;
    const rect = canvas.getBoundingClientRect();
    // Scale coordinates to match canvas internal dimensions
    const scaleX = canvas.width / rect.width;
    const scaleY = canvas.height / rect.height;
    const x = (touch.clientX - rect.left) * scaleX;
    const y = (touch.clientY - rect.top) * scaleY;
    handleScratch(x, y);
  }, [handleScratch]);

  const onTouchMove = useCallback((e: React.TouchEvent<HTMLCanvasElement>) => {
    if (!isScratching.current) return;
    e.preventDefault();
    const touch = e.touches[0];
    const canvas = e.target as HTMLCanvasElement;
    const rect = canvas.getBoundingClientRect();
    // Scale coordinates to match canvas internal dimensions
    const scaleX = canvas.width / rect.width;
    const scaleY = canvas.height / rect.height;
    const x = (touch.clientX - rect.left) * scaleX;
    const y = (touch.clientY - rect.top) * scaleY;
    handleScratch(x, y);
  }, [handleScratch]);

  const onTouchEnd = useCallback(() => {
    isScratching.current = false;
  }, []);

  return (
    <Box
      id="scratch-card-main-container"
      sx={{
        background: "linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%)",
        borderRadius: "24px",
        padding: "16px",
        position: "relative",
        overflow: "hidden",
        boxShadow: "0 20px 40px rgba(0,0,0,0.15)",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "flex-start",
        minHeight: "420px",
        width: "100%",
        height: "100%",
        margin: "0 auto",
        paddingTop: "24px",
      }}
    >
      {/* Floating decorative elements */}
      <FloatingSparkle
        delay={1}
        size={16}
        style={{ top: "214px", left: "281px" }}
      />
      <FloatingSparkle
        delay={2}
        size={24}
        style={{ top: "349px", left: "23px" }}
      />
      <FloatingSparkle
        delay={1.5}
        size={18}
        style={{ bottom: "10%", right: "10%" }}
      />

      <Typography
        id="scratch-card-title"
        variant="h4"
        sx={{
          fontWeight: "bold",
          color: "white",
          textShadow: "0 2px 4px rgba(0,0,0,0.3)",
          textAlign: "center",
          fontSize: "1.6rem",
          mb: 0.5,
          position: "relative",
          zIndex: 1001,
        }}
      >
        Scratch to Reveal Your Prize!
      </Typography>
      <Typography
        variant="body1"
        sx={{
          color: "white",
          textShadow: "0 1px 2px rgba(0,0,0,0.3)",
          fontSize: "0.8rem",
          textAlign: "center",
          mb: 1,
        }}
      >
        Scratch away the gold to reveal your reward!
      </Typography>

      <Box
        id="scratch-card-scratch-area"
        sx={{
          position: 'relative',
          width: 'calc(100% - 16px)', // Reduce width to create 8px spacing on each side
          height: '140px',
          borderRadius: '12px',
          overflow: 'hidden',
          boxShadow: '0 4px 8px rgba(0,0,0,0.1)',
          margin: '20px 0',
        }}
      >
        <canvas
          id="scratch-card-canvas"
          ref={scratchCardRef}
          width={300}
          height={140}
          onMouseDown={onMouseDown}
          onMouseMove={onMouseMove}
          onMouseUp={onMouseUp}
          onMouseLeave={onMouseLeave}
          onTouchStart={onTouchStart}
          onTouchMove={onTouchMove}
          onTouchEnd={onTouchEnd}
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            touchAction: 'none',
            cursor: 'pointer',
            zIndex: 2,
          }}
        />
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            background: 'linear-gradient(135deg, #FFD700, #FFA500)',
            color: 'white',
            fontSize: '1.8rem',
            fontWeight: 'bold',
            textShadow: '0 2px 4px rgba(0,0,0,0.3)',
            zIndex: 1,
          }}
        >
          {gameOver || scratchProgress >= SCRATCH_CONFIG.revealThreshold ? (
            <Box sx={{ animation: `${revealAnimation} 1s forwards` }}>
              {winningReward?.text}
            </Box>
          ) : (
            <Box sx={{ filter: 'blur(8px)', transition: 'filter 0.5s ease' }}>
              {winningReward?.text || 'Scratch Me!'}
            </Box>
          )}
        </Box>
      </Box>

      {/* Always show the claim button, but disable it until reward is won */}
      <ClaimButton
        id="scratch-card-claim-button"
        sx={{ 
          mt: 2,
          opacity: gameOver && winningReward ? 1 : 0.5,
        }}
        disabled={!gameOver || !winningReward}
        onClick={() => {
          if (gameOver && winningReward) {
            const effectiveCountryCode = countryCode || '+1';
            const effectiveWhatsappNumber = whatsappNumber || '1234567890';
            const whatsappMessage = `I just won ${winningReward.text} on the Scratch Card! Please help me claim my prize.`;
            const fullNumber = `${effectiveCountryCode}${effectiveWhatsappNumber}`.replace(/[^0-9+]/g, '');
            const whatsappUrl = `https://wa.me/${fullNumber}?text=${encodeURIComponent(whatsappMessage)}`;
            window.open(whatsappUrl, '_blank');
          }
        }}
      >
        {gameOver && winningReward ? `Claim ${winningReward.text}` : 'Claim Reward'}
      </ClaimButton>
    </Box>
  );
};

export default ModernScratchCard;
