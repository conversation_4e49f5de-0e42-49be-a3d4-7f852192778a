# Task Plan: Refine <PERSON><PERSON><PERSON> Card and Spin Wheel Editors

## Overall Goal
- Remove reset and reveal all button/label settings from Scratch Card general settings.
- Add general settings (heading and subtext/instructions) to Spin Wheel editor, mirroring Scratch Card structure.
- Ensure changes are minimal and match existing code style.
- Update this plan.md as tasks progress.

## Step-by-Step Plan
1. **Modify ScratchCardEditor.tsx**: Remove TextField components for Reset Button Label and Reveal All Button Label from the General Settings Grid.
2. **Modify ScratchCardModule.tsx**: If reset/reveal buttons exist, remove them to match editor changes.
3. **Add to SpinWheelEditor.tsx**: Introduce states and TextFields for headingText and subTextContent, similar to ScratchCardEditor.
4. **Update SpinWheelModule.tsx**: Add rendering for new heading and subtext if not present.
5. **Update Types in App.tsx**: Ensure SpinWheelModuleContent includes headingText and subTextContent.
6. **Test Changes**: Open preview to verify.
7. **Update Plan**: Mark steps as completed and note any issues.

## Naming Conventions
- Use camelCase for variables (e.g., headingText).
- Follow existing patterns in files.

## Progress
- Step 1: Completed - Removed reset and reveal all label settings from ScratchCardEditor.tsx.
- Step 2: Completed - Removed unused resetButtonLabel and revealAllButtonLabel props from ScratchCardModule.tsx.
- etc.

# New Task Plan: Country Code Improvements and Header Settings Fixes

## Overall Goal
- Implement improvements to country code selection in SpinWheelEditor.
- Fix issues with header settings configuration and live preview.
- Ensure minimal changes matching existing style.

## Step-by-Step Plan
1. **Sort Country Codes Alphabetically**: In SpinWheelEditor.tsx, sort the countryCodes array by country name.
2. **Add Search/Filter Functionality**: Add a search input to filter country codes as user types.
3. **Implement IP Detection for Preselection**: Use a geolocation API to detect user's country and preselect the code, allowing manual change.
4. **Fix Live Preview for Header Settings**: Identify the component handling header config (likely in App.tsx or a specific editor) and ensure updates propagate to the preview.
5. **Correct Mislabeled Fields**: Update labels from 'Continue Button Text' and 'Get Started Button Text' to appropriate header text labels, ensuring they control the correct elements.
6. **Test All Changes**: Verify in preview that sorting, search, IP preselect work, and header updates reflect in real-time.
7. **Update Plan and Knowledge Graph**: Mark completed, add relations in graph for task completions.

## Progress
- To be updated as tasks are completed.

# New Task Plan: Fix Netlify Build Errors

## Overall Goal
- Resolve TypeScript compilation errors preventing Netlify deployment.
- Ensure Node.js version is properly specified for Netlify builds.
- Fix all build-breaking issues to enable successful deployment.

## Step-by-Step Plan
1. **Fix TypeScript Error in experience-builder-sidebar.tsx**: Remove invalid className prop from Sidebar component.
2. **Apply Background Styling Correctly**: Move background classes to SidebarBody component which accepts className.
3. **Create .node-version File**: Add Node.js version specification file for Netlify.
4. **Fix Unused Import Error**: Remove unused Typography import from CanvasHeader.tsx.
5. **Verify Build Success**: Run npm build to confirm all errors are resolved.
6. **Update Plan**: Document completed fixes.

## Progress
- Step 1: ✅ Completed - Removed className prop from Sidebar component in experience-builder-sidebar.tsx.
- Step 2: ✅ Completed - Applied background styling to SidebarBody component instead.
- Step 3: ✅ Completed - Created .node-version file with Node.js version 18.
- Step 4: ✅ Completed - Removed unused Typography import from CanvasHeader.tsx.
- Step 5: ✅ Completed - Build now succeeds with exit code 0.
- Step 6: ✅ Completed - Plan updated with successful resolution.

## Issues Resolved
- **TypeScript Error TS2322**: Fixed invalid className prop on Sidebar component.
- **TypeScript Error TS6133**: Removed unused Typography import.
- **Node.js Version Specification**: Added .node-version file for Netlify deployment.
- **Build Success**: All compilation errors resolved, build completes successfully.