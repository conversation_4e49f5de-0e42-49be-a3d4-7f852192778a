:root {
  --iphone-bezel-color: #d0d0d2;
  --iphone-button-color: #b2b2b5;
  --iphone-frame-border-color: #8a8a8a;
}

.iphone-frame {
  width: 514px;
  height: 896px;
  background-color: var(--iphone-bezel-color);
  border: 1px solid var(--iphone-frame-border-color);
  border-radius: 60px;
  box-shadow: 0 0 40px rgba(0, 0, 0, 0.3), inset 0 0 0 1.5px var(--iphone-frame-border-color), inset 0 0 3px 2px rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: visible;
  padding: 6px;
}

.screen-content-wrapper {
  width: 100%;
  height: 100%;
  background-color: #ffffff;
  border-radius: 52px;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
}

.dynamic-island {
  display: none; /* Hidden since SVG overlay provides the notch */
}

.screen-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  padding-top: 50px;
}

.status-bar {
  padding: 0 24px;
  height: 44px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: black;
  font-size: 14px;
  font-weight: 500;
  z-index: 10;
  position: absolute;
  top: 6px;
  left: 6px;
  right: 6px;
  background-color: transparent;
}

.status-bar .time {
  font-weight: 600;
  font-size: 14px;
  position: absolute;
  left: 24px;
  top: 12px;
}

.status-bar .right-icons {
  display: flex;
  align-items: center;
  gap: 7px;
  position: absolute;
  right: 24px;
  top: 12px;
}

.home-indicator {
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  width: 140px;
  height: 5px;
  background-color: black;
  border-radius: 100px;
  opacity: 0.6;
  z-index: 30;
}

.phone-button {
  position: absolute;
  background-color: var(--iphone-button-color);
  border: 0.5px solid rgba(100, 100, 100, 0.4);
  box-shadow: inset 0 0.5px 0.5px rgba(255, 255, 255, 0.2), 0 0.5px 1px rgba(0, 0, 0, 0.3);
  z-index: 5;
}

.ringer-switch {
  top: 115px;
  left: -2.5px;
  width: 4px;
  height: 32px;
  border-top-left-radius: 1.5px;
  border-bottom-left-radius: 1.5px;
  border-top-right-radius: 0.5px;
  border-bottom-right-radius: 0.5px;
}

.volume-up {
  top: 160px;
  left: -2.5px;
  width: 4px;
  height: 50px;
  border-top-left-radius: 1.5px;
  border-bottom-left-radius: 1.5px;
  border-top-right-radius: 0.5px;
  border-bottom-right-radius: 0.5px;
}

.volume-down {
  top: 220px;
  left: -2.5px;
  width: 4px;
  height: 50px;
  border-top-left-radius: 1.5px;
  border-bottom-left-radius: 1.5px;
  border-top-right-radius: 0.5px;
  border-bottom-right-radius: 0.5px;
}

.side-button {
  top: 170px;
  right: -2.5px;
  width: 4px;
  height: 85px;
  border-top-right-radius: 1.5px;
  border-bottom-right-radius: 1.5px;
  border-top-left-radius: 0.5px;
  border-bottom-left-radius: 0.5px;
}

.frame-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 25;
}
