import React from 'react';
import { Screen, type MediaItem, type Module, type HeaderConfig } from './Screen';
import { Box } from '@mui/material';

interface MobileScreenOnlyProps {
  modules?: Module[];
  backgroundMedia?: MediaItem | null;
  currentScreen: number;
  totalScreens: number;
  onScreenChange: (idx: number) => void;
  onUpdateModuleContent?: (moduleId: string, updatedContentPart: Partial<Module['content']>) => void;
  headerConfig?: HeaderConfig;
}

/**
 * A component that renders only the phone screen content without the bezel
 * Used for the Experience page where we only want to show the screen content
 */
export const MobileScreenOnly: React.FC<MobileScreenOnlyProps> = ({ 
  modules = [], 
  backgroundMedia, 
  currentScreen, 
  totalScreens, 
  onScreenChange,
  onUpdateModuleContent,
  headerConfig 
}) => {
  return (
    <Box 
      sx={{ 
        width: '100vw', 
        height: '100vh',
        position: 'absolute',
        top: 0,
        left: 0,
        overflow: 'hidden',
        margin: 0,
        padding: 0,
        borderRadius: '0', // Remove border radius for full viewport experience
        boxShadow: 'none' // Remove shadow for full viewport experience
      }}
    >
      <Screen 
        modules={modules.filter(m => (m.screen ?? 0) === currentScreen)}
        backgroundMedia={backgroundMedia}
        currentScreen={currentScreen}
        totalScreens={totalScreens}
        onScreenChange={onScreenChange}
        onUpdateModuleContent={onUpdateModuleContent}
        isFullScreenView={true} // Use full screen view mode
        headerConfig={headerConfig}
      />
    </Box>
  );
};
