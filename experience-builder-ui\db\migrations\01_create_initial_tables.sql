-- Create schema for experience builder data
CREATE SCHEMA IF NOT EXISTS experience_builder;

-- Set up extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create table for experiences
CREATE TABLE IF NOT EXISTS experience_builder.experiences (
    experience_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    background_image VARCHAR(255),
    status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived'))
);

-- Create table for modules in experiences
CREATE TABLE IF NOT EXISTS experience_builder.modules (
    module_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    experience_id UUID NOT NULL REFERENCES experience_builder.experiences(experience_id) ON DELETE CASCADE,
    type VARCHA<PERSON>(50) NOT NULL,
    title VARCHAR(100) NOT NULL,
    display_order INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    content JSONB NOT NULL DEFAULT '{}'
);

-- Create index for faster module lookups by experience
CREATE INDEX IF NOT EXISTS idx_modules_experience_id ON experience_builder.modules(experience_id);

-- Create table for QR codes
CREATE TABLE IF NOT EXISTS experience_builder.qr_codes (
    qr_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    experience_id UUID NOT NULL REFERENCES experience_builder.experiences(experience_id) ON DELETE CASCADE,
    qr_image_url VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_accessed TIMESTAMP WITH TIME ZONE,
    access_count INTEGER DEFAULT 0
);

-- Create unique index to ensure one QR code per experience
CREATE UNIQUE INDEX IF NOT EXISTS idx_qr_codes_experience_id ON experience_builder.qr_codes(experience_id);

-- Create table for experience_analytics
CREATE TABLE IF NOT EXISTS experience_builder.analytics (
    analytics_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    experience_id UUID NOT NULL REFERENCES experience_builder.experiences(experience_id) ON DELETE CASCADE,
    session_id UUID NOT NULL,
    event_type VARCHAR(50) NOT NULL,
    event_data JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    user_agent TEXT,
    ip_address VARCHAR(50)
);

-- Create index for faster analytics queries
CREATE INDEX IF NOT EXISTS idx_analytics_experience_id ON experience_builder.analytics(experience_id);
CREATE INDEX IF NOT EXISTS idx_analytics_session_id ON experience_builder.analytics(session_id);

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION experience_builder.update_timestamp()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = NOW(); 
   RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for experiences table
CREATE TRIGGER update_experiences_timestamp
BEFORE UPDATE ON experience_builder.experiences
FOR EACH ROW EXECUTE FUNCTION experience_builder.update_timestamp();
