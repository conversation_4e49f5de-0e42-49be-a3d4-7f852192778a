import React from 'react';
import { TextField, Typography, Box } from '@mui/material';
import { styled } from '@mui/material/styles';

const StyledTextField = styled(TextField)(({ theme }) => ({
  '& .MuiOutlinedInput-root': {
    borderRadius: '8px',
    '& fieldset': {
      borderColor: 'rgba(0, 0, 0, 0.23)',
      transition: theme.transitions.create('border-color'),
    },
    '&:hover fieldset': {
      borderColor: theme.palette.primary.main,
    },
    '&.Mui-focused fieldset': {
      borderColor: theme.palette.primary.main,
      borderWidth: '2px',
    },
  },
}));

interface AddDetailsProps {
  experienceName: string;
  setExperienceName: (name: string) => void;
}

const AddDetails: React.FC<AddDetailsProps> = ({ experienceName, setExperienceName }) => {
  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
      <Typography variant="h6" sx={{ color: 'black' }}>Experience Name</Typography>
      <StyledTextField
        variant="outlined"
        value={experienceName}
        onChange={(e) => setExperienceName(e.target.value)}
        placeholder="Enter experience name"
        fullWidth
      />
    </Box>
  );
};

export default AddDetails;
