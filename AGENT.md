# Agent Development Guide

## Build/Test Commands
- **Main UI**: `cd experience-builder-ui && npm run dev` (development server)
- **Build UI**: `cd experience-builder-ui && npm run build`
- **Lint**: `cd experience-builder-ui && npm run lint`
- **Build modules**: `cd gamified-modules && npm run build`
- **Test server**: `node test-server.js`
- **No test suite configured** - add testing framework if needed

## Architecture
- **Monorepo structure**: Multiple subprojects in separate directories
- **experience-builder-ui/**: Main React+Vite UI with MUI, Tailwind, React DND
- **gamified-modules/**: Reusable React components with Framer Motion
- **shared/**: Common types and utilities across projects
- **mobile-runtime/**: Mobile-specific code (minimal)
- **Database**: Supabase integration for saving experiences

## Code Style
- **TypeScript**: Strict mode enabled, ES2022 target
- **React**: Functional components with hooks, JSX transform
- **Imports**: MUI components, shared types via path mapping
- **Styling**: Tailwind CSS with custom theme, MUI styled components
- **Naming**: PascalCase components, camelCase variables/functions
- **ESLint**: React hooks rules, TypeScript recommended
- **File structure**: Components in `/components`, pages in `/pages`, types in `/types`
