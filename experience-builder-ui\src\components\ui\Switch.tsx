import React from 'react';

interface SwitchProps {
  checked: boolean;
  onChange: (checked: boolean) => void;
  disabled?: boolean;
  size?: 'small' | 'medium' | 'large';
  label?: string;
  id?: string;
}

const Switch: React.FC<SwitchProps> = ({
  checked,
  onChange,
  disabled = false,
  size = 'medium',
  label,
  id
}) => {
  const sizeClasses = {
    small: {
      container: 'w-8 h-5',
      thumb: 'w-3 h-3',
      translateOff: 'translate-x-1',
      translateOn: 'translate-x-4'
    },
    medium: {
      container: 'w-11 h-6',
      thumb: 'w-4 h-4',
      translateOff: 'translate-x-1',
      translateOn: 'translate-x-6'
    },
    large: {
      container: 'w-14 h-7',
      thumb: 'w-5 h-5',
      translateOff: 'translate-x-1',
      translateOn: 'translate-x-8'
    }
  };

  const currentSize = sizeClasses[size];

  return (
    <div className="flex items-center space-x-2">
      <button
        type="button"
        role="switch"
        aria-checked={checked}
        aria-labelledby={id}
        disabled={disabled}
        onClick={() => onChange(!checked)}
        className={`
          ${currentSize.container}
          relative inline-flex items-center rounded-full
          transition-colors duration-200 ease-in-out
          focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
          ${checked 
            ? 'bg-blue-500 shadow-inner' 
            : 'bg-gray-300 shadow-inner'
          }
          ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
        `}
      >
        <span
          className={`
            ${currentSize.thumb}
            inline-block rounded-full bg-white shadow-lg
            transform transition-transform duration-200 ease-in-out
            ${checked ? currentSize.translateOn : currentSize.translateOff}
          `}
        />
      </button>
      {label && (
        <label
          id={id}
          className={`text-sm font-medium ${disabled ? 'text-gray-400' : 'text-gray-700'} cursor-pointer`}
          onClick={() => !disabled && onChange(!checked)}
        >
          {label}
        </label>
      )}
    </div>
  );
};

export default Switch;
