@tailwind base;
@tailwind components;
@tailwind utilities;

.hide-scrollbar::-webkit-scrollbar {
  display: none;
}

/* Remove all focus outlines */
*:focus {
  outline: none !important;
  box-shadow: none !important;
}

/* Target MUI inputs specifically */
.Mui-focused fieldset {
  border-color: #e5e7eb !important;
  outline: none !important;
}

.MuiOutlinedInput-root:focus {
  outline: none !important;
  box-shadow: none !important;
}

/* Custom Launch Button Styles */
.launch-button {
  background: linear-gradient(to bottom, #00d4ff 0%, #090979 79%, #020024 100%) !important;
  color: white !important;
  padding: 10px 20px !important;
  border-radius: 20px !important;
  border: none !important;
  cursor: pointer !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  font-size: 1rem !important;
  box-shadow: 0 4px 14px 0 rgba(0, 0, 0, 0.1) !important;
  transition: all 0.3s ease !important;
  font-family: inherit !important;
  line-height: 1.5 !important;
  margin: 0 !important;
  -webkit-appearance: button !important;
  text-transform: none !important;
}

.launch-button:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 20px 0 rgba(0, 0, 0, 0.15) !important;
}

.launch-button .material-icons {
  font-size: 1.2rem !important;
}

/* :root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #ffffff;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
} */

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

body {
  margin: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-width: 320px;
  min-height: 100vh;
  overflow-x: hidden;
  background-color: #ffffff;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

/* button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #ffffff;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
} */

/* @media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #ffffff;
  }
} */

/* App layout overrides */
#root {
  width: 100%;
  max-width: 100vw;
  margin: 0 auto;
  box-sizing: border-box;
}

/* Scrollbar Hiding Utilities */
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}
.scrollbar-hide {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
