# 🚀 Staging Environment Setup Guide

This document provides comprehensive instructions for setting up, running, and deploying the **Experience Builder** staging environment.

## 📋 Overview

The staging environment is a complete isolated copy of the production application that allows you to:
- Test new features before production deployment
- Validate changes in a production-like environment
- Debug issues without affecting live users
- Demonstrate new functionality to stakeholders

## 🏗️ Architecture

### Directory Structure
```
staging/
└── Web-app-experience-staging/          # Staging codebase
    ├── experience-builder-ui/           # Main React application
    │   ├── .env.staging                 # Staging environment variables
    │   ├── package.json                 # Modified with staging scripts
    │   └── vite.config.ts               # Staging-specific Vite config
    ├── gamified-modules/                # Reusable components
    ├── shared/                          # Shared types and utilities
    ├── netlify-staging.toml             # Staging deployment config
    └── README-STAGING.md                # This file
```

### Key Differences from Production
- **Port**: Staging runs on `localhost:4000` (Production: `localhost:5173`)
- **Environment**: Uses `.env.staging` file with staging-specific variables
- **Database**: Separate Supabase configuration (staging schema/project)
- **Build**: Includes source maps and debug information
- **Git Branch**: Uses `staging` branch for deployments

## 🛠️ Prerequisites

Ensure you have the following installed:
- **Node.js**: v18+ (currently using v24.7.0)
- **npm**: v8+ (currently using v11.5.1)
- **Git**: Latest version

## ⚡ Quick Start

### 1. Navigate to Staging Directory
```bash
cd staging/Web-app-experience-staging/experience-builder-ui
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Start Staging Server
```bash
npm run start:staging
```

The staging application will be available at:
- **Local**: http://localhost:4000/
- **Network**: http://[your-ip]:4000/

## 📜 Available Scripts

### Development Scripts
```bash
# Start staging development server (port 4000)
npm run start:staging
npm run dev:staging

# Start regular development server (port 5173)
npm run dev
```

### Build Scripts
```bash
# Build for staging (with debug info and source maps)
npm run build:staging

# Build for production
npm run build
```

### Testing Scripts
```bash
# Run staging tests (includes linting)
npm run test:staging

# Run linting only
npm run lint
```

### Preview Scripts
```bash
# Preview staging build (port 4001)
npm run preview:staging

# Preview production build (port 4173)
npm run preview
```

## 🔧 Configuration Files

### Environment Variables (.env.staging)
```env
# Staging Supabase Configuration
VITE_SUPABASE_URL=https://jqaqkymjacdnllytexou.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# Staging-specific settings
VITE_ENVIRONMENT=staging
VITE_DEBUG_MODE=true
VITE_PUBLIC_URL=http://localhost:4000

# Feature flags
VITE_ENABLE_DEBUG_TOOLS=true
VITE_ENABLE_STAGING_FEATURES=true
```

### Vite Configuration (vite.config.ts)
- **Port**: Automatically uses 4000 for staging mode
- **Source Maps**: Enabled for staging builds
- **Minification**: Disabled for easier debugging
- **File Naming**: Prefixed with "staging-" for identification

## 🌐 Deployment

### Netlify Staging Deployment

1. **Configure Netlify Site**:
   - Create a new Netlify site for staging
   - Connect to the `staging` branch of your repository
   - Use `netlify-staging.toml` configuration

2. **Build Settings**:
   ```toml
   [build]
   command = "cd experience-builder-ui && npm install --force && npm run build:staging"
   publish = "experience-builder-ui/dist"
   ```

3. **Environment Variables**:
   Set the following in Netlify dashboard:
   - `VITE_ENVIRONMENT=staging`
   - `VITE_DEBUG_MODE=true`
   - Add your staging Supabase credentials

### Manual Deployment
```bash
# Build staging version
npm run build:staging

# Deploy the dist/ folder to your hosting provider
```

## 🔄 Git Workflow

### Branch Strategy
- **main**: Production deployments only
- **staging**: Staging deployments and testing
- **feature/***: Development branches

### Workflow Process
1. **Create Feature Branch**:
   ```bash
   git checkout -b feature/new-feature
   ```

2. **Develop and Test Locally**:
   ```bash
   npm run start:staging
   ```

3. **Merge to Staging**:
   ```bash
   git checkout staging
   git merge feature/new-feature
   git push origin staging
   ```

4. **Test in Staging Environment**:
   - Verify functionality at staging URL
   - Run automated tests
   - Perform manual testing

5. **Deploy to Production**:
   ```bash
   git checkout main
   git merge staging
   git push origin main
   ```

## 🧪 Testing Procedures

### Pre-Deployment Checklist
- [ ] All new features work correctly
- [ ] No console errors or warnings
- [ ] Mobile responsiveness verified
- [ ] Database operations function properly
- [ ] Authentication flows work
- [ ] QR code generation works
- [ ] All interactive modules function (spin wheel, scratch cards, etc.)

### Testing Commands
```bash
# Run all staging tests
npm run test:staging

# Check for linting issues
npm run lint

# Build and preview staging version
npm run build:staging && npm run preview:staging
```

## 🐛 Troubleshooting

### Common Issues

1. **Port Already in Use**:
   ```bash
   # Kill process using port 4000
   npx kill-port 4000
   ```

2. **Environment Variables Not Loading**:
   - Ensure `.env.staging` file exists
   - Check file encoding (should be UTF-8)
   - Restart development server

3. **Build Failures**:
   ```bash
   # Clear node_modules and reinstall
   rm -rf node_modules package-lock.json
   npm install
   ```

4. **Database Connection Issues**:
   - Verify Supabase credentials in `.env.staging`
   - Check network connectivity
   - Ensure staging database schema is up to date

### Debug Mode Features
When `VITE_DEBUG_MODE=true`:
- Enhanced error logging
- Development tools enabled
- Source maps included
- Detailed build information

## 📞 Support

For issues with the staging environment:
1. Check this documentation first
2. Review console logs for errors
3. Verify environment configuration
4. Test in production mode to isolate staging-specific issues

## 🔒 Security Notes

- **Never use production credentials** in staging environment
- Staging database should be separate from production
- Regularly update staging environment with latest security patches
- Monitor staging logs for suspicious activity

---

**Last Updated**: September 23, 2025
**Staging Environment Version**: 1.0.0
