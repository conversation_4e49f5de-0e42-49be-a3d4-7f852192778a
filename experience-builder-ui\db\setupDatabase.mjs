// <PERSON><PERSON><PERSON> to set up the Experience Builder database in Supabase
import { Client } from 'pg';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get environment variables
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const serviceRoleKey = process.env.VITE_SUPABASE_SERVICE_ROLE;

if (!supabaseUrl || !serviceRoleKey) {
  console.error('Missing Supabase environment variables. Check your .env file.');
  process.exit(1);
}

// Extract the host and port from the Supabase URL
const url = new URL(supabaseUrl);
const host = url.hostname;
const port = parseInt(url.port) || 5432;
const database = url.pathname.replace(/^\//, '');

// Create a new client
const client = new Client({
  host,
  port,
  database,
  user: 'postgres',
  password: serviceRoleKey,
  ssl: {
    rejectUnauthorized: false
  }
});

// SQL statements to create the database
const createSchemaSQL = `
CREATE SCHEMA IF NOT EXISTS experience_builder;
`;

const createExtensionsSQL = `
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
`;

const createExperiencesTableSQL = `
CREATE TABLE IF NOT EXISTS experience_builder.experiences (
    experience_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    background_image TEXT,
    status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived'))
);
`;

const createModulesTableSQL = `
CREATE TABLE IF NOT EXISTS experience_builder.modules (
    module_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    experience_id UUID NOT NULL REFERENCES experience_builder.experiences(experience_id) ON DELETE CASCADE,
    type VARCHAR(50) NOT NULL,
    title VARCHAR(100) NOT NULL,
    display_order INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    content JSONB NOT NULL DEFAULT '{}'::jsonb
);
`;

const createModulesIndexSQL = `
CREATE INDEX IF NOT EXISTS idx_modules_experience_id ON experience_builder.modules(experience_id);
`;

const createQrCodesTableSQL = `
CREATE TABLE IF NOT EXISTS experience_builder.qr_codes (
    qr_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    experience_id UUID NOT NULL REFERENCES experience_builder.experiences(experience_id) ON DELETE CASCADE,
    qr_image_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_accessed TIMESTAMP WITH TIME ZONE,
    access_count INTEGER DEFAULT 0,
    CONSTRAINT fk_experience UNIQUE (experience_id)
);
`;

const createQrCodesIndexSQL = `
CREATE INDEX IF NOT EXISTS idx_qr_codes_experience_id ON experience_builder.qr_codes(experience_id);
`;

const createAnalyticsTableSQL = `
CREATE TABLE IF NOT EXISTS experience_builder.analytics (
    analytics_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    experience_id UUID NOT NULL REFERENCES experience_builder.experiences(experience_id) ON DELETE CASCADE,
    event_type VARCHAR(50) NOT NULL,
    event_data JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
`;

const createAnalyticsIndexSQL = `
CREATE INDEX IF NOT EXISTS idx_analytics_experience_id ON experience_builder.analytics(experience_id);
`;

const createUpdateFunctionSQL = `
CREATE OR REPLACE FUNCTION experience_builder.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;
`;

const createTriggerSQL = `
DROP TRIGGER IF EXISTS update_experiences_updated_at ON experience_builder.experiences;
CREATE TRIGGER update_experiences_updated_at
BEFORE UPDATE ON experience_builder.experiences
FOR EACH ROW
EXECUTE FUNCTION experience_builder.update_updated_at_column();
`;

const createIncrementCounterFunctionSQL = `
CREATE OR REPLACE FUNCTION experience_builder.increment_counter(experience_uuid UUID)
RETURNS INTEGER AS $$
DECLARE
    current_count INTEGER;
BEGIN
    SELECT access_count INTO current_count 
    FROM experience_builder.qr_codes 
    WHERE experience_id = experience_uuid;
    
    RETURN COALESCE(current_count, 0) + 1;
END;
$$ LANGUAGE plpgsql;
`;

const enableRLSSQL = `
ALTER TABLE experience_builder.experiences ENABLE ROW LEVEL SECURITY;
ALTER TABLE experience_builder.modules ENABLE ROW LEVEL SECURITY;
ALTER TABLE experience_builder.qr_codes ENABLE ROW LEVEL SECURITY;
ALTER TABLE experience_builder.analytics ENABLE ROW LEVEL SECURITY;
`;

const createAuthPoliciesSQL = `
CREATE POLICY "Allow all for authenticated users" ON experience_builder.experiences 
    FOR ALL TO authenticated USING (true);
CREATE POLICY "Allow all for authenticated users" ON experience_builder.modules 
    FOR ALL TO authenticated USING (true);
CREATE POLICY "Allow all for authenticated users" ON experience_builder.qr_codes 
    FOR ALL TO authenticated USING (true);
CREATE POLICY "Allow all for authenticated users" ON experience_builder.analytics 
    FOR ALL TO authenticated USING (true);
`;

const createAnonPoliciesSQL = `
CREATE POLICY "Allow read for anonymous users" ON experience_builder.experiences 
    FOR SELECT TO anon USING (true);
CREATE POLICY "Allow read for anonymous users" ON experience_builder.modules 
    FOR SELECT TO anon USING (true);
CREATE POLICY "Allow read for anonymous users" ON experience_builder.qr_codes 
    FOR SELECT TO anon USING (true);
`;

// Setup function for the database
async function setupDatabase() {
  console.log('Setting up Experience Builder database...');
  
  // Connect and run the SQL
  client.connect()
    .then(() => {
      console.log('Connected to database');
      return client.query(createSchemaSQL);
    })
    .then(() => {
      console.log('Created schema');
      return client.query(createExtensionsSQL);
    })
    .then(() => {
      console.log('Created extensions');
      return client.query(createExperiencesTableSQL);
    })
    .then(() => {
      console.log('Created experiences table');
      return client.query(createModulesTableSQL);
    })
    .then(() => {
      console.log('Created modules table');
      return client.query(createModulesIndexSQL);
    })
    .then(() => {
      console.log('Created modules index');
      return client.query(createQrCodesTableSQL);
    })
    .then(() => {
      console.log('Created QR codes table');
      return client.query(createQrCodesIndexSQL);
    })
    .then(() => {
      console.log('Created QR codes index');
      return client.query(createAnalyticsTableSQL);
    })
    .then(() => {
      console.log('Created analytics table');
      return client.query(createAnalyticsIndexSQL);
    })
    .then(() => {
      console.log('Created analytics index');
      return client.query(createUpdateFunctionSQL);
    })
    .then(() => {
      console.log('Created updated_at function');
      return client.query(createTriggerSQL);
    })
    .then(() => {
      console.log('Created trigger');
      return client.query(createIncrementCounterFunctionSQL);
    })
    .then(() => {
      console.log('Created increment counter function');
      return client.query(enableRLSSQL);
    })
    .then(() => {
      console.log('Enabled Row Level Security');
      return client.query(createAuthPoliciesSQL);
    })
    .then(() => {
      console.log('Created auth policies');
      return client.query(createAnonPoliciesSQL);
    })
    .then(() => {
      console.log('Created anon policies');
      console.log('Database setup complete');
      client.end();
    })
    .catch(err => {
      console.error('Error setting up database:', err);
      client.end();
      process.exit(1);
    });
}

// Run the setup
setupDatabase();
